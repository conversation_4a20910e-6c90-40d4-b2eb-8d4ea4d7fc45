05-24 16:06:05.992     1     1 I auditd  : type=1107 audit(0.0:4): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_cam_stereo_camera_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-24 16:06:05.992     1     1 W /system/bin/init: type=1107 audit(0.0:4): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_cam_stereo_camera_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-24 16:06:05.992     1     1 I auditd  : type=1107 audit(0.0:5): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_cam_dualzoom_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-24 16:06:05.992     1     1 W /system/bin/init: type=1107 audit(0.0:5): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_cam_dualzoom_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-24 16:06:05.992     1     1 I auditd  : type=1107 audit(0.0:6): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_key_manager_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-24 16:06:05.992     1     1 W /system/bin/init: type=1107 audit(0.0:6): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_key_manager_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-24 16:06:05.992     1     1 I auditd  : type=1107 audit(0.0:7): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.build.fingerprint pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:fingerprint_prop:s0 tclass=property_service permissive=0'
05-24 16:06:05.992     1     1 W /system/bin/init: type=1107 audit(0.0:7): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.build.fingerprint pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:fingerprint_prop:s0 tclass=property_service permissive=0'
05-24 16:06:06.645   572   572 I auditd  : SELinux: Loaded service context from:
05-24 16:06:06.645   572   572 I auditd  : 		/system/etc/selinux/plat_service_contexts
05-24 16:06:06.645   572   572 I auditd  : 		/system_ext/etc/selinux/system_ext_service_contexts
05-24 16:06:06.645   572   572 I auditd  : 		/product/etc/selinux/product_service_contexts
05-24 16:06:06.645   572   572 I auditd  : 		/vendor/etc/selinux/vendor_service_contexts
05-24 16:06:06.645   572   572 I auditd  : 		/odm/etc/selinux/odm_service_contexts
05-24 16:06:06.662   574   574 I hwservicemanager: getFrameworkHalManifest: Reading VINTF information.
05-24 16:06:06.670   574   574 I hwservicemanager: Loaded APEX Infos from /bootstrap-apex/apex-info-list.xml
05-24 16:06:06.670   574   574 I hwservicemanager: getDeviceHalManifest: Reading VINTF information.
05-24 16:06:06.688   574   574 I hwservicemanager: Loaded APEX Infos from /bootstrap-apex/apex-info-list.xml
05-24 16:06:06.692   574   574 I hwservicemanager: getDeviceHalManifest: Successfully processed VINTF information
05-24 16:06:06.692   574   574 I hwservicemanager: getFrameworkHalManifest: Successfully processed VINTF information
05-24 16:06:06.697   574   574 I hwservicemanager: hwservicemanager is ready now.
05-24 16:06:06.787     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvdata" to "5": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-24 16:06:06.793     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvdata" to "5": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-24 16:06:06.799     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvcfg" to "5": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-24 16:06:06.805     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvcfg" to "5": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-24 16:06:06.904   633   633 I HidlServiceManagement: Registered android.system.suspend@1.0::ISystemSuspend/default
05-24 16:06:06.904   633   633 I HidlServiceManagement: Removing namespace from process name android.system.suspend-service to suspend-service.
05-24 16:06:06.910   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.boot@1.0::IBootControl/default in either framework or device VINTF manifest.
05-24 16:06:06.916   635   635 I <EMAIL>: Trustonic Keymaster 4.1 Service starts
05-24 16:06:06.926   635   635 I HidlServiceManagement: Registered android.hardware.keymaster@4.1::IKeymasterDevice/default
05-24 16:06:06.926   635   635 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 16:06:06.926   635   635 I <EMAIL>: Trustonic Keymaster 4.1 Service registered
05-24 16:06:06.939   634   634 I keystore2: system/security/keystore2/src/keystore2_main.rs:154 - Successfully registered Keystore 2.0 service.
05-24 16:06:07.097   572   642 W libc    : Unable to set property "ctl.interface_start" to "aidl/android.system.keystore2.IKeystoreService/default": PROP_ERROR_HANDLE_CONTROL_MESSAGE (0x20)
05-24 16:06:07.118   655   655 I libperfmgr: Pixel Power HAL AIDL Service with Extension is starting with config: /vendor/etc/powerhint.json
05-24 16:06:07.120   655   655 I libperfmgr: Failed to read Node[18]'s ResetOnInit, set to 'false'
05-24 16:06:07.120   655   655 I libperfmgr: Failed to read Node[19]'s ResetOnInit, set to 'false'
05-24 16:06:07.120   655   655 I libperfmgr: Failed to read Node[20]'s ResetOnInit, set to 'false'
05-24 16:06:07.120   655   655 I libperfmgr: Failed to read Node[21]'s ResetOnInit, set to 'false'
05-24 16:06:07.122   655   655 I libperfmgr: PowerHint AUDIO_STREAMING_LOW_LATENCY has 3 node actions, and 0 hint actions parsed
05-24 16:06:07.123   655   655 I libperfmgr: Initialized HintManager from JSON config: /vendor/etc/powerhint.json
05-24 16:06:07.124   655   655 I powerhal-libperfmgr: Initialize PowerHAL
05-24 16:06:07.125   655   655 I powerhal-libperfmgr: Pixel Power HAL AIDL Service with Extension is started.
05-24 16:06:07.127   591   591 I vold    : fscrypt_initialize_systemwide_keys
05-24 16:06:07.189   591   591 I incfs   : Initial API level of the device: 30
05-24 16:06:07.211   634   634 E keystore2:     1: system/security/keystore2/src/globals.rs:264: Trying to get Legacy wrapper. Attempt to get keystore compat service for security level r#STRONGBOX
05-24 16:06:07.211   591   591 E vold    : keystore2 Keystore earlyBootEnded returned service specific error: -68
05-24 16:06:07.226   665   665 I tombstoned: tombstoned successfully initialized
05-24 16:06:07.527   806   806 I derive_sdk: extension ad_services version is 15
05-24 16:06:07.704   809   809 I derive_classpath: ReadClasspathFragment /apex/com.android.adservices/etc/classpaths/bootclasspath.pb
05-24 16:06:07.705   809   809 I derive_classpath: ReadClasspathFragment /apex/com.android.btservices/etc/classpaths/bootclasspath.pb
05-24 16:06:07.707   809   809 I derive_classpath: ReadClasspathFragment /apex/com.android.nfcservices/etc/classpaths/bootclasspath.pb
05-24 16:06:07.708   809   809 I derive_classpath: ReadClasspathFragment /apex/com.android.permission/etc/classpaths/bootclasspath.pb
05-24 16:06:07.712   809   809 I derive_classpath: ReadClasspathFragment /apex/com.android.adservices/etc/classpaths/systemserverclasspath.pb
05-24 16:06:07.714   809   809 I derive_classpath: ReadClasspathFragment /apex/com.android.btservices/etc/classpaths/systemserverclasspath.pb
05-24 16:06:07.715   809   809 I derive_classpath: ReadClasspathFragment /apex/com.android.permission/etc/classpaths/systemserverclasspath.pb
05-24 16:06:07.718   809   809 I derive_classpath: export BOOTCLASSPATH /apex/com.android.art/javalib/core-oj.jar:/apex/com.android.art/javalib/core-libart.jar:/apex/com.android.art/javalib/okhttp.jar:/apex/com.android.art/javalib/bouncycastle.jar:/apex/com.android.art/javalib/apache-xml.jar:/system/framework/framework.jar:/system/framework/framework-graphics.jar:/system/framework/framework-location.jar:/system/framework/framework-connectivity-b.jar:/system/framework/ext.jar:/system/framework/telephony-common.jar:/system/framework/voip-common.jar:/system/framework/ims-common.jar:/system/framework/framework-platformcrashrecovery.jar:/system/framework/framework-ondeviceintelligence-platform.jar:/system/framework/mediatek-common.jar:/system/framework/mediatek-framework.jar:/system/framework/mediatek-ims-base.jar:/system/framework/mediatek-ims-common.jar:/system/framework/mediatek-telecom-common.jar:/system/framework/mediatek-telephony-base.jar:/system/framework/mediatek-telephony-common.jar:/apex/com.android.i18n/javalib/core-icu4j.jar:/apex/com.android.adservices/javalib/framework-adservices.jar:/apex/com.android.adservices/javalib/framework-sdksandbox.jar:/apex/com.android.appsearch/javalib/framework-appsearch.jar:/apex/com.android.btservices/javalib/framework-bluetooth.jar:/apex/com.android.configinfrastructure/javalib/framework-configinfrastructure.jar:/apex/com.android.conscrypt/javalib/conscrypt.jar:/apex/com.android.devicelock/javalib/framework-devicelock.jar:/apex/com.android.healthfitness/javalib/framework-healthfitness.jar:/apex/com.android.ipsec/javalib/android.net.ipsec.ike.jar:/apex/com.android.media/javalib/updatable-media.jar:/apex/com.android.mediaprovider/javalib/framework-mediaprovider.jar:/apex/com.android.mediaprovider/javalib/framework-pdf.jar:/apex/com.android.mediaprovider/javalib/framework-pdf-v.jar:/apex/com.android.mediaprovider/javalib/framework-photopicker.jar:/apex/com.android.nfcservices/javalib/framework-nfc.jar:/apex/com.android.ondevicepersonalization/javalib/framework-ondevicepersonalization.jar:/apex/com.android.os.statsd/javalib/framework-statsd.jar:/apex/com.android.permission/javalib/framework-permission.jar:/apex/com.android.permission/javalib/framework-permission-s.jar:/apex/com.android.profiling/javalib/framework-profiling.jar:/apex/com.android.scheduling/javalib/framework-scheduling.jar:/apex/com.android.sdkext/javalib/framework-sdkextensions.jar:/apex/com.android.tethering/javalib/framework-connectivity.jar:/apex/com.android.tethering/javalib/framework-connectivity-t.jar:/apex/com.android.tethering/javalib/framework-tethering.jar:/apex/com.android.uwb/javalib/framework-uwb.jar:/apex/com.android.virt/javalib/framework-virtualization.jar:/apex/com.android.wifi/javalib/framework-wifi.jar
05-24 16:06:07.718   809   809 I derive_classpath: export SYSTEMSERVERCLASSPATH /system/framework/com.android.location.provider.jar:/system/framework/services.jar:/apex/com.android.adservices/javalib/service-adservices.jar:/apex/com.android.adservices/javalib/service-sdksandbox.jar:/apex/com.android.appsearch/javalib/service-appsearch.jar:/
05-24 16:06:07.798   810   810 I art_boot: Property persist.device_config.runtime_native_boot.useartservice not set
05-24 16:06:07.825   811   811 W odsign  : Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.system.keystore2.IKeystoreService/default
05-24 16:06:07.836   811   811 I odsign  : Initialized Keystore key.
05-24 16:06:08.598   822   822 I netdClient: Skipping libnetd_client init since *we* are netd
05-24 16:06:08.615   574   574 I hwservicemanager: getFrameworkHalManifest: Reloading VINTF information.
05-24 16:06:08.616   574   574 I hwservicemanager: getFrameworkHalManifest: Reading VINTF information.
05-24 16:06:08.619   574   574 I hwservicemanager: Loaded APEX Infos from /apex/apex-info-list.xml
05-24 16:06:08.620   822   822 I NetdUpdatable: libnetd_updatable_init: Initializing
05-24 16:06:08.620   822   822 I NetdUpdatable: initMaps successfully
05-24 16:06:08.620   822   822 I netd    : libnetd_updatable_init success
05-24 16:06:08.620   574   574 I hwservicemanager: getDeviceHalManifest: Reloading VINTF information.
05-24 16:06:08.621   574   574 I hwservicemanager: getDeviceHalManifest: Reading VINTF information.
05-24 16:06:08.634   574   574 I hwservicemanager: Loaded APEX Infos from /apex/apex-info-list.xml
05-24 16:06:08.635   574   574 I hwservicemanager: getDeviceHalManifest: Successfully processed VINTF information
05-24 16:06:08.635   574   574 I hwservicemanager: getFrameworkHalManifest: Successfully processed VINTF information
05-24 16:06:08.640   832   832 I HidlServiceManagement: Registered android.hidl.allocator@1.0::IAllocator/ashmem
05-24 16:06:08.640   832   832 I HidlServiceManagement: Removing namespace from process name android.hidl.allocator@1.0-service to allocator@1.0-service.
05-24 16:06:08.641   834   834 I mtk.hal.bt@1.0-impl: Init IBluetoothHCI
05-24 16:06:08.644   834   834 I HidlServiceManagement: Registered android.hardware.bluetooth@1.1::IBluetoothHci/default
05-24 16:06:08.645   834   834 I HidlServiceManagement: Removing namespace from process name android.hardware.bluetooth@1.1-service-mediatek to bluetooth@1.1-service-mediatek.
05-24 16:06:08.650   822   822 I netd    : Initializing RouteController: 881us
05-24 16:06:08.673   835   835 I HidlServiceManagement: Registered android.hardware.cas@1.2::IMediaCasService/default
05-24 16:06:08.674   835   835 I HidlServiceManagement: Removing namespace from process name android.hardware.cas@1.2-service to cas@1.2-service.
05-24 16:06:08.678   840   840 I HidlServiceManagement: Registered android.hardware.drm@1.4::IDrmFactory/widevine
05-24 16:06:08.678   840   840 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 16:06:08.681   852   852 I android.hardware.health@2.1-service: default instance initializing with healthd_config...
05-24 16:06:08.681   840   840 I HidlServiceManagement: Registered android.hardware.drm@1.4::ICryptoFactory/widevine
05-24 16:06:08.681   840   840 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 16:06:08.681   833   833 I HidlServiceManagement: Registered android.hardware.audio@7.1::IDevicesFactory/default
05-24 16:06:08.682   833   833 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-24 16:06:08.682   833   833 I LegacySupport: Registration complete for android.hardware.audio@7.1::IDevicesFactory/default.
05-24 16:06:08.682   852   852 I HidlServiceManagement: Registered android.hardware.health@2.1::IHealth/default
05-24 16:06:08.682   852   852 I HidlServiceManagement: Removing namespace from process name android.hardware.health@2.1-service to health@2.1-service.
05-24 16:06:08.682   852   852 I android.hardware.health@2.1-service: default: Hal init done
05-24 16:06:08.685   847   847 E android.hardware.gnss-service.mediatek: safe_sendto: safe_sendto() sendto() failed path=[mtk_hal2mnl] ret=-1 reason=[Connection refused]111
05-24 16:06:08.685   847   847 E android.hardware.gnss-service.mediatek: gps_device__get_gps_interface: hal2mnl_hal_reboot failed because of safe_sendto fail ,strerror:Connection refused 
05-24 16:06:08.689   822   822 I netd    : Initializing XfrmController: 38445us
05-24 16:06:08.693   847   847 E android.hardware.gnss-service.mediatek: safe_sendto: safe_sendto() sendto() failed path=[mtk_hal2mnl] ret=-1 reason=[Connection refused]111
05-24 16:06:08.693   847   847 E android.hardware.gnss-service.mediatek: gps_device__get_gps_interface: hal2mnl_hal_reboot failed because of safe_sendto fail ,strerror:Connection refused 
05-24 16:06:08.693   822   822 I resolv  : resolv_init: Initializing resolver
05-24 16:06:08.695   847   847 I HidlServiceManagement: Registered android.hardware.gnss@2.1::IGnss/default
05-24 16:06:08.696   847   847 I HidlServiceManagement: Removing namespace from process name android.hardware.gnss-service.mediatek to gnss-service.mediatek.
05-24 16:06:08.701   850   850 I HidlServiceManagement: Registered android.hardware.graphics.allocator@4.0::IAllocator/default
05-24 16:06:08.702   850   850 I HidlServiceManagement: Removing namespace from process name android.hardware.graphics.allocator@4.0-service-mediatek to allocator@4.0-service-mediatek.
05-24 16:06:08.704   833   833 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-24 16:06:08.704   833   833 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-24 16:06:08.705   822   822 I netd    : Registering NetdNativeService: 185us
05-24 16:06:08.705   822   822 I netd    : Registering MDnsService: 255us
05-24 16:06:08.707   822   822 I HidlServiceManagement: Registered android.system.net.netd@1.1::INetd/default
05-24 16:06:08.707   822   822 I netd    : Registering NetdHwService: 1598us
05-24 16:06:08.712   869   869 I android.hardware.usb@1.3-service-mediatekv2: UsbGadget
05-24 16:06:08.714   869   869 I HidlServiceManagement: Registered android.hardware.usb@1.3::IUsb/default
05-24 16:06:08.714   869   869 I HidlServiceManagement: Removing namespace from process name android.hardware.usb@1.3-service-mediatekv2 to usb@1.3-service-mediatekv2.
05-24 16:06:08.716   869   869 I HidlServiceManagement: Registered android.hardware.usb.gadget@1.1::IUsbGadget/default
05-24 16:06:08.717   869   869 I android.hardware.usb@1.3-service-mediatekv2: USB HAL Ready.
05-24 16:06:08.729   873   873 I vtservice_hidl: [VT][SRV]before VTService_HiDL_instantiate
05-24 16:06:08.732   863   863 W <EMAIL>: ThermalHelper:tz_map_version 1
05-24 16:06:08.732   863   863 I <EMAIL>: ThermalWatcherThread started
05-24 16:06:08.734   863   863 I HidlServiceManagement: Registered android.hardware.thermal@2.0::IThermal/default
05-24 16:06:08.734   863   863 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 16:06:08.738   885   885 W <EMAIL>: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.hardware.power.IPower/default
05-24 16:06:08.738   885   885 I <EMAIL>: Connected to power AIDL HAL
05-24 16:06:08.738   873   873 I HidlServiceManagement: Registered vendor.mediatek.hardware.videotelephony@1.0::IVideoTelephony/default
05-24 16:06:08.741   885   885 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkpower@1.2::IMtkPower/default
05-24 16:06:08.741   885   885 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 16:06:08.744   885   885 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkpower@1.2::IMtkPerf/default
05-24 16:06:08.744   885   885 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 16:06:08.744   833   833 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 16:06:08.750   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_agps
05-24 16:06:08.752   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_wlan
05-24 16:06:08.754   851   851 W hwcomposer: [DRMDEV] 0xcccccccc[64] property[MSYNC2_0_ENABLE] does not do initialize  
05-24 16:06:08.754   851   851 W hwcomposer: [DRMDEV] 0xcccccccc[64] property[SKIP_CONFIG] does not do initialize  
05-24 16:06:08.754   851   851 W hwcomposer: [DRMDEV] failed to initialize crtc[0]: 64  
05-24 16:06:08.754   851   851 W hwcomposer: [DRMDEV] 0xcccccccc[88] property[MSYNC2_0_ENABLE] does not do initialize  
05-24 16:06:08.754   851   851 W hwcomposer: [DRMDEV] 0xcccccccc[88] property[SKIP_CONFIG] does not do initialize  
05-24 16:06:08.754   851   851 W hwcomposer: [DRMDEV] failed to initialize crtc[1]: 88  
05-24 16:06:08.754   851   851 W hwcomposer: [DRMDEV] 0xcccccccc[99] property[MSYNC2_0_ENABLE] does not do initialize  
05-24 16:06:08.754   851   851 W hwcomposer: [DRMDEV] 0xcccccccc[99] property[SKIP_CONFIG] does not do initialize  
05-24 16:06:08.754   851   851 W hwcomposer: [DRMDEV] failed to initialize crtc[2]: 99  
05-24 16:06:08.754   851   851 W hwcomposer: [DRMDEV] failed to initialize all crtc: -19  
05-24 16:06:08.755   851   851 E hwcomposer: [DRMDEV] failed to initialize drm resource  
05-24 16:06:08.755   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_bt
05-24 16:06:08.757   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_sensor
05-24 16:06:08.758   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_network
05-24 16:06:08.759   851   851 I hwcomposer: [PqXmlParser] [PQ_SERVICE] mDeviceVersion = AMS644VA04_MTK04_20615  
05-24 16:06:08.759   851   851 I hwcomposer: [PqXmlParser] [PQ_SERVICE] mDeviceManufacture = samsung1024  
05-24 16:06:08.759   851   851 I hwcomposer: [PqXmlParser] [PQ_SERVICE] prjName:20662  
05-24 16:06:08.759   851   851 I hwcomposer: [PqXmlParser] init: failed to open file: /vendor/etc/cust_pq.xml  
05-24 16:06:08.760   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_ipaddr
05-24 16:06:08.760   574   574 I hwservicemanager: Since vendor.mediatek.hardware.pq@2.14::IPictureQuality/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 16:06:08.760   851   851 E hwcomposer: [IPqDevice] Can't get PQ service tried (0) times  
05-24 16:06:08.762   851   851 I HidlServiceManagement: Registered vendor.mediatek.hardware.composer_ext@1.0::IComposerExt/default
05-24 16:06:08.762   851   851 I hwcomposer: [HWC] IComposerExt service registration completed.  
05-24 16:06:08.762   886   886 I HidlServiceManagement: Registered vendor.mediatek.hardware.nvram@1.1::INvram/default
05-24 16:06:08.762   886   886 I HidlServiceManagement: Removing namespace from process name vendor.mediatek.hardware.nvram@1.1-service to nvram@1.1-service.
05-24 16:06:08.764   851   851 I HidlServiceManagement: Registered android.hardware.graphics.composer@2.3::IComposer/default
05-24 16:06:08.764   851   851 I HidlServiceManagement: Removing namespace from process name android.hardware.graphics.composer@2.3-service to composer@2.3-service.
05-24 16:06:08.764   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_lbs
05-24 16:06:08.765   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_framework2agps
05-24 16:06:08.766   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_agps2framework
05-24 16:06:08.767   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mnld2nlputils
05-24 16:06:08.769   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mnld2debugService
05-24 16:06:08.769   574   896 I hwservicemanager: Tried to start vendor.mediatek.hardware.pq@2.14::IPictureQuality/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 16:06:08.770   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_debugService2mnld
05-24 16:06:08.771   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_meta2mnld
05-24 16:06:08.773   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_agpsd2debugService
05-24 16:06:08.776   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_debugService2agpsd
05-24 16:06:08.777   895   895 E vendor.oplus.hardware.charger@1.0-service: notifyScreenStatus: 0
05-24 16:06:08.777   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/AgpsInterface
05-24 16:06:08.779   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/AgpsDebugInterface
05-24 16:06:08.779   895   895 E vendor.oplus.hardware.charger@1.0-service: setChgStatusToBcc: 0
05-24 16:06:08.780   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mnld2mtklogger
05-24 16:06:08.781   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mtklogger2mnld
05-24 16:06:08.781   856   856 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/acc_cali.json) open failed: -2 (No such file or directory)
05-24 16:06:08.782   882   882 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lbs_log_v2s
05-24 16:06:08.786   895   895 I HidlServiceManagement: Registered vendor.oplus.hardware.charger@1.0::ICharger/default
05-24 16:06:08.786   895   895 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.charger@1.0-service to charger@1.0-service.
05-24 16:06:08.787   856   856 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/gyro_cali.json) open failed: -2 (No such file or directory)
05-24 16:06:08.787   892   892 I HidlServiceManagement: Registered vendor.oplus.hardware.cammidasservice@1.0::IMIDASService/default
05-24 16:06:08.788   892   892 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.cammidasservice@1.0-service to cammidasservice@1.0-service.
05-24 16:06:08.788   892   892 I vendor.oplus.hardware.cammidasservice@1.0-service: midasservice register successfully
05-24 16:06:08.789   895   895 E vendor.oplus.hardware.charger@1.0-service: ERR:Failed to get bms heating config file
05-24 16:06:08.789   895   895 E vendor.oplus.hardware.charger@1.0-service: can't parse config, rc=-1
05-24 16:06:08.790   900   900 I HidlServiceManagement: Registered vendor.oplus.hardware.performance@1.0::IPerformance/default
05-24 16:06:08.791   900   900 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.performance@1.0-service to performance@1.0-service.
05-24 16:06:08.798   856   856 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/als_cali.json) open failed: -2 (No such file or directory)
05-24 16:06:08.799   856   856 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/ps_cali.json) open failed: -2 (No such file or directory)
05-24 16:06:08.799   856   856 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/sar_cali.json) open failed: -2 (No such file or directory)
05-24 16:06:08.799   856   856 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/ois_cali.json) open failed: -2 (No such file or directory)
05-24 16:06:08.802   901   901 W MTK_FG_FUEL: fd < 0, init first!
05-24 16:06:08.802   901   901 W MTK_FG_FUEL: init failed, return!
05-24 16:06:08.802   901   901 W MTK_FG  : fd < 0, init first!
05-24 16:06:08.802   901   901 E MTK_FG  : init failed, return!
05-24 16:06:08.802   901   901 W MTK_FG  : fd < 0, init first!
05-24 16:06:08.802   901   901 E MTK_FG  : init failed, return!
05-24 16:06:08.802   901   901 W MTK_FG  : fd < 0, init first!
05-24 16:06:08.802   901   901 E MTK_FG  : init failed, return!
05-24 16:06:08.813   856   856 I HidlServiceManagement: Registered android.hardware.sensors@2.0::ISensors/default
05-24 16:06:08.813   856   856 I HidlServiceManagement: Removing namespace from <NAME_EMAIL>6893 to sensors@2.0-service-multihal.mt6893.
05-24 16:06:08.816   889   889 I HidlServiceManagement: Registered vendor.trustonic.tee@1.1::ITee/default
05-24 16:06:08.817   889   889 I HidlServiceManagement: Removing namespace from process name vendor.trustonic.tee@1.1-service to tee@1.1-service.
05-24 16:06:08.818   889   889 I HidlServiceManagement: Registered vendor.trustonic.tee.tui@1.0::ITui/default
05-24 16:06:08.820   904   904 I credstore: Registered binder service
05-24 16:06:08.824   919   919 E ccci_mdinit: (1):main, fail to open ccci_dump, err(Permission denied)
05-24 16:06:08.826   919   919 I ccci_mdinit: (1):[main] drv_ver: 2
05-24 16:06:08.826   919   919 I ccci_mdinit: (1):[main] ccci_create_md_status_listen_thread
05-24 16:06:08.826   919   919 I ccci_mdinit: (1):md_init ver:2.30, sub:0, 1
05-24 16:06:08.826   919   919 I NVRAM   : MD1 set status: vendor.mtk.md1.status=init 
05-24 16:06:08.831   919   919 I ccci_mdinit: (1):MD0 set status: mtk.md0.status=init 
05-24 16:06:08.831   919   919 I NVRAM   : MD0 set status: mtk.md0.status=init 
05-24 16:06:08.831   919   919 E ccci_mdinit: (1):get property fail: ro.vendor.mtk_mipc_support
05-24 16:06:08.831   919   919 I ccci_mdinit: (1):service names: [init.svc.vendor.gsm0710muxd][init.svc.vendor.ril-daemon-mtk][init.svc.emdlogger1][init.svc.vendor.ccci_fsd]
05-24 16:06:08.831   919   919 I ccci_mdinit: (1):md_img_exist 0 0 0 0
05-24 16:06:08.831   919   919 I ccci_mdinit: (1):MD1 set status: mtk.md1.status=reset 
05-24 16:06:08.831   919   919 E ccci_mdinit: (1):[get_mdini_killed_state] error: get mdinit killed: 25(-1)
05-24 16:06:08.832   919   919 I ccci_mdinit: (1):MD_LOG_MEMDUMP_WAIT:ro.vendor.md_log_memdump_wait not exist, using default value
05-24 16:06:08.832   919   919 I ccci_mdinit: (1):MD_LOG_MEMDUMP_WAIT value: 0
05-24 16:06:08.832   919   919 I ccci_mdinit: (1):md0: mdl_mode=0
05-24 16:06:08.832   919   919 I ccci_mdinit: (1):check_nvram_ready(), property_get("vendor.service.nvram_init") = , read_nvram_ready_retry = 1
05-24 16:06:08.832   922   922 E ccci_mdinit: (3):main, fail to open ccci_dump, err(Permission denied)
05-24 16:06:08.845   930   930 I TeeMcDaemon: Initialise Secure World [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/MobiCoreDriverDaemon.cpp:260]
05-24 16:06:08.845   930   930 W TeeMcDaemon: Cannot open key SO  (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:445]
05-24 16:06:08.846   930   930 I TeeMcDaemon: Start services [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/MobiCoreDriverDaemon.cpp:325]
05-24 16:06:08.854   655   658 W powerhal-libperfmgr: Connecting to PPS daemon failed (No such file or directory)
05-24 16:06:08.863   899   899 I HidlServiceManagement: Registered vendor.oplus.hardware.oplusSensor@1.0::ISensorFeature/default
05-24 16:06:08.863   899   899 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.oplusSensor@1.0-service to oplusSensor@1.0-service.
05-24 16:06:08.889   930   940 W TeeMcDaemon: Cannot open trustlet /data/vendor/mcRegistry/07050501000000000000000000000020.tlbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:08.889   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: failed to get path of fd 3: No such file or directory
05-24 16:06:08.892   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(15): previous definition here
05-24 16:06:08.892   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(38): previous definition here
05-24 16:06:08.893   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(69): previous definition here
05-24 16:06:08.893   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(70): previous definition here
05-24 16:06:08.893   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(26): previous definition here
05-24 16:06:08.893   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(37): previous definition here
05-24 16:06:08.893   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(45): previous definition here
05-24 16:06:08.893   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(46): previous definition here
05-24 16:06:08.893   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(53): previous definition here
05-24 16:06:08.894   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(56): previous definition here
05-24 16:06:08.894   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(59): previous definition here
05-24 16:06:08.894   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(60): previous definition here
05-24 16:06:08.894   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(61): previous definition here
05-24 16:06:08.894   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(74): previous definition here
05-24 16:06:08.894   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(15): previous definition here
05-24 16:06:08.894   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(88): previous definition here
05-24 16:06:08.895   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(89): previous definition here
05-24 16:06:08.895   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(90): previous definition here
05-24 16:06:08.895   853   853 W android.hardware.media.c2@1.2-mediatek: libminijail[853]: compile_file: <fd>(91): previous definition here
05-24 16:06:08.897   941   941 I gsid    : no DSU: No such file or directory
05-24 16:06:08.901   845   845 I HidlServiceManagement: Registered android.hardware.gatekeeper@1.0::IGatekeeper/default
05-24 16:06:08.901   845   845 I HidlServiceManagement: Removing namespace from process name android.hardware.gatekeeper@1.0-service to gatekeeper@1.0-service.
05-24 16:06:08.904   853   853 I HidlServiceManagement: Registered android.hardware.media.c2@1.1::IComponentStore/default
05-24 16:06:08.904   853   853 I HidlServiceManagement: Removing namespace from process name android.hardware.media.c2@1.2-mediatek to c2@1.2-mediatek.
05-24 16:06:08.905   903   903 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 16:06:08.906   903   903 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 16:06:08.906   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 16:06:08.908   930   940 W TeeMcDaemon: Cannot open trustlet /data/vendor/mcRegistry/08050500000000000000000000000000.tlbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:08.910   930   940 W TeeMcDaemon: Cannot open trustlet /data/vendor/mcRegistry/08050500000000000000000000000000.tlbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:08.933   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.933   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.933   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.933   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.933   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.933   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.933   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.933   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.933   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.configstore@1.0::ISurfaceFlingerConfigs/default in either framework or device VINTF manifest.
05-24 16:06:08.933   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.934   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.934   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.934   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.934   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.934   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.934   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.934   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.934   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.934   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.935   903   903 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 16:06:08.936   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 16:06:08.937   903   903 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 16:06:08.938   915   915 I SurfaceFlinger: Using HWComposer service: default
05-24 16:06:08.942   915   915 I SurfaceFlinger: SurfaceFlinger's main thread ready to run. Initializing graphics H/W...
05-24 16:06:08.942   903   903 W BatteryNotifier: batterystats service unavailable!
05-24 16:06:08.943   903   903 I ServiceManagerCppClient: Waiting for service 'media.metrics' on '/dev/binder'...
05-24 16:06:08.990   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.990   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:08.994   833   833 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 16:06:09.031   833   833 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x32c6a8d)
05-24 16:06:09.032   990   990 I bootstat: Service started: /system/bin/bootstat --set_system_boot_reason 
05-24 16:06:09.037   833   833 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x9aaf5fef)
05-24 16:06:09.039   833   833 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 16:06:09.041   833   833 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xceab1e45)
05-24 16:06:09.042   833   833 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x56d38cfd)
05-24 16:06:09.045   930   930 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/05160000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.045   930   930 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/020b0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.049   993   993 I perfetto:           probes.cc:104 Starting /system/bin/traced_probes service
05-24 16:06:09.049   993   993 I perfetto:  probes_producer.cc:373 Disconnected from tracing service
05-24 16:06:09.050   833   833 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-24 16:06:09.051   930   930 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/030b0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.051   930   930 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/03100000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.051   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 16:06:09.053   833   833 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-24 16:06:09.053   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 16:06:09.054   833   833 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-24 16:06:09.055   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 16:06:09.055   833   833 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-24 16:06:09.055   833   833 W audiohalservice: Could not register Bluetooth Audio API
05-24 16:06:09.055   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-24 16:06:09.056   833   833 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-24 16:06:09.056   833   833 W audiohalservice: Could not register Bluetooth Audio Offload API
05-24 16:06:09.061   930   930 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/031c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.061   930   930 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/033c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.061   930   930 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/035c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.061   930   930 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/037c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.061   930   930 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/031c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.061   930   930 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/032c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.061   930   930 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/033c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.061   930   930 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/034c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.061   930   930 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/035c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.061   930   930 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/036c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.061   930   930 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/037c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.069   930   930 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/070c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.069   930   930 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/090b0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.069   930   930 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/0f5eed3c3b5a47afacca69a84bf0efad.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.069   930   930 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/07060000000000000000000000007169.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.069   930   930 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/4be4f7dc1f2c11e5b5f7727283247c7f.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.069   930   930 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/08070000000000000000000000008270.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.069   930   930 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/07070000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.069   930   930 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/07407000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.073   930   930 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/6b3f5fa0f8cf55a7be2582587d62d63a.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 16:06:09.096   996   996 W perfetto:          service.cc:232 Started traced, listening on /dev/socket/traced_producer /dev/socket/traced_consumer
05-24 16:06:09.109   833   833 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-24 16:06:09.110   833   833 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-24 16:06:09.110   833   833 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-24 16:06:09.111  1004  1004 W MTK_FG_NVRAM: fd < 0, init first!
05-24 16:06:09.111  1004  1004 W MTK_FG_NVRAM: init failed, return!
05-24 16:06:09.115  1004  1004 W MTK_FG_NVRAM: fd < 0, init first!
05-24 16:06:09.115  1004  1004 W MTK_FG_NVRAM: init failed, return!
05-24 16:06:09.115  1004  1004 W MTK_FG_NVRAM: fd < 0, init first!
05-24 16:06:09.115  1004  1004 W MTK_FG_NVRAM: init failed, return!
05-24 16:06:09.115  1004  1004 W MTK_FG_NVRAM: fd < 0, init first!
05-24 16:06:09.115  1004  1004 W MTK_FG_NVRAM: init failed, return!
05-24 16:06:09.115  1004  1004 W MTK_FG_NVRAM: fd < 0, init first!
05-24 16:06:09.116  1004  1004 W MTK_FG_NVRAM: init failed, return!
05-24 16:06:09.116  1004  1004 W MTK_FG_NVRAM: fd < 0, init first!
05-24 16:06:09.116  1004  1004 W MTK_FG_NVRAM: init failed, return!
05-24 16:06:09.116  1004  1004 W MTK_FG_NVRAM: fd < 0, init first!
05-24 16:06:09.116  1004  1004 W MTK_FG_NVRAM: init failed, return!
05-24 16:06:09.116  1004  1004 W MTK_FG_NVRAM: fd < 0, init first!
05-24 16:06:09.116  1004  1004 W MTK_FG_NVRAM: init failed, return!
05-24 16:06:09.116  1004  1004 W MTK_FG_NVRAM: fd < 0, init first!
05-24 16:06:09.116  1004  1004 W MTK_FG_NVRAM: init failed, return!
05-24 16:06:09.116  1004  1004 W MTK_FG_NVRAM: fd < 0, init first!
05-24 16:06:09.116  1004  1004 W MTK_FG_NVRAM: init failed, return!
05-24 16:06:09.137   824   824 I zygote64: Initializing ART runtime metrics
05-24 16:06:09.152   825   825 I zygote  : Initializing ART runtime metrics
05-24 16:06:09.171  1014  1014 I HidlServiceManagement: Registered android.system.wifi.keystore@1.0::IKeystore/default
05-24 16:06:09.172  1005  1005 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 16:06:09.172  1005  1005 E mnld_pwr_interface: mnld_pwr_init: mnld_pwr_open failed, No such file or directory
05-24 16:06:09.173  1005  1005 E MNL2AGPS: bind_udp_socket: bind failed path=[/data/agps_supl/agps_to_mnl] reason=[No such file or directory]
05-24 16:06:09.173  1005  1005 E mtk_lbs_utility: init_timer_id_alarm: timerfd_create  CLOCK_BOOTTIME_ALARM 
05-24 16:06:09.173  1005  1005 E MNLD    : mnld_init: mnl2hal_release_wakelock failed because of safe_sendto fail ,strerror:Connection refused 
05-24 16:06:09.173  1005  1005 E MNLD    : mnld_init: mnl2hal_mnld_reboot failed because of safe_sendto fail ,strerror:Connection refused 
05-24 16:06:09.173  1005  1005 E mnld    : mtk_socket_connect_local: mtk_socket_connect_local() connect() failed reason=[Connection refused]111 for path=[mtk_mnld2debugService]
05-24 16:06:09.182  1005  1031 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 16:06:09.182  1005  1031 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:0
05-24 16:06:09.212  1041  1041 I thermal_repeater: RilRPC_init 
05-24 16:06:09.212  1041  1041 I thermal_repeater: RilRPC_init dlopen fail: dlopen failed: library "librpcril.so" not found 
05-24 16:06:09.222  1015  1015 I android.hardware.media.omx@1.0-service: mediacodecservice starting
05-24 16:06:09.225  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: failed to get path of fd 5: No such file or directory
05-24 16:06:09.225  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: allowing syscall: clock_gettime
05-24 16:06:09.225  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: allowing syscall: connect
05-24 16:06:09.225  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: allowing syscall: fcntl64
05-24 16:06:09.225  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: allowing syscall: socket
05-24 16:06:09.225  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: allowing syscall: writev
05-24 16:06:09.225  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(59): syscall getrandom redefined here
05-24 16:06:09.225  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(15): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(1): syscall read redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(9): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(2): syscall write redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(5): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(3): syscall exit redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(40): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(4): syscall rt_sigreturn redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(45): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(6): syscall exit_group redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(44): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(7): syscall clock_gettime redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(7): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(8): syscall gettimeofday redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(47): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(9): syscall futex redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(3): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(10): syscall getrandom redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(15): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(13): syscall ppoll redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(13): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(14): syscall pipe2 redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(46): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(15): syscall openat redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(30): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(16): syscall dup redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(12): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(17): syscall close redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(10): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(18): syscall lseek redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(50): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(19): syscall getdents64 redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(58): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(20): syscall faccessat redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(38): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(27): syscall rt_sigprocmask redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(41): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(30): syscall prctl redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(6): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(31): syscall madvise redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(29): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(32): syscall mprotect redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(28): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(33): syscall munmap redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(27): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(34): syscall getuid32 redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(34): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(35): syscall fstat64 redefined here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(35): previous definition here
05-24 16:06:09.226  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(36): syscall mmap2 redefined here
05-24 16:06:09.227  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(14): previous definition here
05-24 16:06:09.228  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(66): syscall getpid redefined here
05-24 16:06:09.228  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(11): previous definition here
05-24 16:06:09.228  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(67): syscall gettid redefined here
05-24 16:06:09.228  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(12): previous definition here
05-24 16:06:09.228  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(74): syscall recvfrom redefined here
05-24 16:06:09.228  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(22): previous definition here
05-24 16:06:09.228  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(77): syscall sched_getaffinity redefined here
05-24 16:06:09.228  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(75): previous definition here
05-24 16:06:09.228  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(82): syscall sysinfo redefined here
05-24 16:06:09.228  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(24): previous definition here
05-24 16:06:09.228  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(83): syscall setsockopt redefined here
05-24 16:06:09.228  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(23): previous definition here
05-24 16:06:09.228  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: logging seccomp filter failures
05-24 16:06:09.239  1010  1010 I main_extractorservice: enable media.extractor memory limits
05-24 16:06:09.246   903   903 I ServiceManagerCppClient: Waiting for service 'media.metrics' on '/dev/binder' successful after waiting 303ms
05-24 16:06:09.247  1010  1010 W mediaextractor: libminijail[1010]: failed to get path of fd 5: No such file or directory
05-24 16:06:09.248  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(38): previous definition here
05-24 16:06:09.248  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(18): previous definition here
05-24 16:06:09.248   903   903 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(6): previous definition here
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(27): previous definition here
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(29): previous definition here
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(28): previous definition here
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(4): previous definition here
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(32): previous definition here
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(12): previous definition here
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(9): previous definition here
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(8): previous definition here
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(23): previous definition here
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(41): previous definition here
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(25): previous definition here
05-24 16:06:09.250  1011  1029 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder'...
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(56): previous definition here
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(5): previous definition here
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(14): previous definition here
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(13): previous definition here
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(11): previous definition here
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(15): previous definition here
05-24 16:06:09.250   993   993 I perfetto:  probes_producer.cc:332 Connected to the service
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(16): previous definition here
05-24 16:06:09.250  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(10): previous definition here
05-24 16:06:09.258  1045  1045 I thermal_src1: ta_daemon_init
05-24 16:06:09.259  1010  1010 W mediaextractor: libminijail[1010]: compile_file: /apex/com.android.media/etc/seccomp_policy/crash_dump.arm64.policy(7): previous definition here
05-24 16:06:09.259  1010  1010 W mediaextractor: libminijail[1010]: compile_file: <fd>(56): previous definition here
05-24 16:06:09.259  1010  1010 W mediaextractor: libminijail[1010]: compile_file: /apex/com.android.media/etc/seccomp_policy/crash_dump.arm64.policy(6): previous definition here
05-24 16:06:09.259  1015  1015 I HidlServiceManagement: Registered android.hardware.media.omx@1.0::IOmx/default
05-24 16:06:09.260  1015  1015 I HidlServiceManagement: Removing namespace from process name android.hardware.media.omx@1.0-service to omx@1.0-service.
05-24 16:06:09.260  1015  1015 I android.hardware.media.omx@1.0-service: IOmx HAL service created.
05-24 16:06:09.261  1040  1040 I ULog    : ULog initialized: mode=0x1  filters: req=0x0 func=0x0/0x0 details=0xfffff000 level=3
05-24 16:06:09.265  1015  1015 E MediaCodecsXmlParser: Cannot find the role for a decoder of type audio/x-adpcm-dvi-ima
05-24 16:06:09.265  1015  1015 E MediaCodecsXmlParser: Cannot find the role for a decoder of type audio/x-adpcm-ms
05-24 16:06:09.265  1015  1015 E MediaCodecsXmlParser: Cannot find the role for a decoder of type audio/alac
05-24 16:06:09.265  1015  1015 E MediaCodecsXmlParser: Cannot find the role for a decoder of type audio/ape
05-24 16:06:09.265  1015  1015 I OmxStore: node [OMX.MTK.AUDIO.DECODER.GSM] not found in IOmx
05-24 16:06:09.265  1015  1015 I OmxStore: node [OMX.MTK.AUDIO.DECODER.MP3] not found in IOmx
05-24 16:06:09.268  1015  1015 I HidlServiceManagement: Registered android.hardware.media.omx@1.0::IOmxStore/default
05-24 16:06:09.268  1015  1015 I HidlServiceManagement: Removing namespace from process name android.hardware.media.omx@1.0-service to omx@1.0-service.
05-24 16:06:09.276   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.atrace@1.0::IAtraceDevice/default in either framework or device VINTF manifest.
05-24 16:06:09.281  1049  1049 I VPUD    : vdec_codec_service_init() block mode
05-24 16:06:09.281  1049  1049 I VPUD    : venc_codec_service_init()
05-24 16:06:09.281  1049  1049 I VPUD    : -- send_init_fin
05-24 16:06:09.282  1049  1077 I VPUD    : venc service entry TID = 1077
05-24 16:06:09.284  1049  1076 I VPUD    : vdec_service_entry()
05-24 16:06:09.286  1012  1012 I mediaserver: ServiceManager: 0xe8e02da0
05-24 16:06:09.286  1012  1012 W BatteryNotifier: batterystats service unavailable!
05-24 16:06:09.287  1012  1012 W BatteryNotifier: batterystats service unavailable!
05-24 16:06:09.275  1013  1013 I storaged: Unable to get AIDL health service, trying HIDL...
05-24 16:06:09.299  1047  1085 I MtkAgpsNative: Enter mtk_agps_up_init
05-24 16:06:09.301  1047  1085 E agps    : [agps] ERR: [MNL] bind failed path=[/data/agps_supl/mnl_to_agps] reason=[No such file or directory]
05-24 16:06:09.302  1047  1085 E agps    : [agps] ERR: [Default] mtk_expat_xml_load2() failed [fopen() No such file or directory] file [/data/vendor/agps_supl/agps_profiles_conf2.xml]
05-24 16:06:09.302  1047  1085 E agps    : [agps] ERR: [Default] mtk_expat_xml_load2() failed [fopen() No such file or directory] file [/data/agps_supl/agps_profiles_conf2.xml]
05-24 16:06:09.302  1047  1085 E agps    : [agps] ERR: [Default] mtk_expat_xml_load2() failed [fopen() No such file or directory] file [/vendor/etc/agps_profiles_conf2.xml]
05-24 16:06:09.302   903   903 E libxml2 : I/O warning : failed to load "/odm/etc/virtual_audio_policy_configuration.xml": No such file or directory
05-24 16:06:09.302   903   903 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-24 16:06:09.302   903   903 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-24 16:06:09.303   574   574 I hwservicemanager: Since vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 16:06:09.303  1006  1006 I cameraserver: ServiceManager: 0xb400006fadcc2ef0
05-24 16:06:09.306   574   574 I hwservicemanager: Since vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 16:06:09.306  1006  1006 I CameraService: CameraService started (pid=1006)
05-24 16:06:09.306  1006  1006 I CameraService: CameraService process starting
05-24 16:06:09.306  1006  1006 W BatteryNotifier: batterystats service unavailable!
05-24 16:06:09.306  1006  1006 W BatteryNotifier: batterystats service unavailable!
05-24 16:06:09.306  1067  1067 I HidlServiceManagement: getService: Trying again for vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default...
05-24 16:06:09.309   574   574 I hwservicemanager: Since android.hardware.camera.provider@2.4::ICameraProvider/internal/0 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 16:06:09.309  1006  1006 W CameraProviderManager: tryToInitializeHidlProviderLocked: HIDL Camera provider HAL 'internal/0' is not actually available
05-24 16:06:09.310  1001  1001 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-neuron
05-24 16:06:09.310  1001  1001 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-24 16:06:09.310  1001  1001 I ANNService: Registered service for mtk-neuron
05-24 16:06:09.311  1006  1006 W CameraProviderManager: tryToInitializeAidlProviderLocked: AIDL Camera provider HAL 'android.hardware.camera.provider.ICameraProvider/virtual/0' is not actually available
05-24 16:06:09.311  1071  1071 E android.hardware.biometrics.fingerprint@2.1-service: fingerprint hwbinder service starting
05-24 16:06:09.312  1071  1071 I android.hardware.biometrics.fingerprint@2.1-service: read_proc_string_data sys_buffer_data=G_OPTICAL_G3S #end
05-24 16:06:09.313  1071  1071 E android.hardware.biometrics.fingerprint@2.1-service: fp read fp_id_string = G_OPTICAL_G3S
05-24 16:06:09.313  1078  1078 I KernelSU Next: ksud::cli: command: Services
05-24 16:06:09.313  1078  1078 I KernelSU Next: ksud::init_event: on_services triggered!
05-24 16:06:09.313  1078  1078 I KernelSU Next: ksud::module: /data/adb/service.d not exists, skip
05-24 16:06:09.313  1047  1085 E mtk_socket: ERR: mtk_socket_connect_local() connect() failed reason=[Connection refused]111 for path=[mtk_agpsd2debugService]
05-24 16:06:09.313  1071  1071 E android.hardware.biometrics.fingerprint@2.1-service: optical goodix fingerprint
05-24 16:06:09.314  1047  1085 E agps    : [agps] ERR: [CP] get_ccci_uart  open failed node=[/dev/ccci2_tty2] reason=[No such file or directory]
05-24 16:06:09.314  1047  1085 E agps    :  ERR: [AGPS2WIFI] bind failed path=[/data/agps_supl/wifi_2_agps] reason=[No such file or directory]
05-24 16:06:09.314  1047  1085 E agps    : [agps] ERR: [WIFI] wifi_mgr_init  create_wifi2agps_fd failed
05-24 16:06:09.314   869   869 I android.hardware.usb@1.3-service-mediatekv2: setCurrentUsbFunctions: skip first time for usbd
05-24 16:06:09.314   869   869 I android.hardware.usb@1.3-service-mediatekv2: Usb Gadget setcurrent functions failed
05-24 16:06:09.314  1071  1071 I android.hardware.biometrics.fingerprint@2.1-service: fpSettingsInit project name:0
05-24 16:06:09.314  1071  1071 I android.hardware.biometrics.fingerprint@2.1-service: read_proc_string_data sys_buffer_data=Device version:		AMS644VA04_MTK04_20615
05-24 16:06:09.314  1071  1071 I android.hardware.biometrics.fingerprint@2.1-service: Device manufacture:		samsung1024
05-24 16:06:09.314  1071  1071 I android.hardware.biometrics.fingerprint@2.1-service:  #end
05-24 16:06:09.314  1071  1071 I android.hardware.biometrics.fingerprint@2.1-service: fpSettingsInit lcd type:1
05-24 16:06:09.314  1071  1071 I android.hardware.biometrics.fingerprint@2.1-service: fpSettingsInit select config index is :0
05-24 16:06:09.315  1071  1071 I android.hardware.biometrics.fingerprint@2.1-service: do nothing
05-24 16:06:09.316  1001  1001 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-dsp
05-24 16:06:09.317  1001  1001 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-24 16:06:09.317  1001  1001 I ANNService: Registered service for mtk-dsp
05-24 16:06:09.321  1006  1006 I HidlServiceManagement: Registered android.frameworks.cameraservice.service@2.2::ICameraService/default
05-24 16:06:09.322  1006  1006 I CameraService: CameraService pinged cameraservice proxy
05-24 16:06:09.323  1006  1006 I cameraserver: ServiceManager: 0xb400006fadcc2ef0 done instantiate
05-24 16:06:09.324  1001  1001 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-mdla
05-24 16:06:09.324  1001  1001 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-24 16:06:09.325  1001  1001 I ANNService: Registered service for mtk-mdla
05-24 16:06:09.325  1071  1071 E [GF_HAL][HalContext]: [init], init with G3 HAL.
05-24 16:06:09.328  1001  1001 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-gpu
05-24 16:06:09.328  1001  1001 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-24 16:06:09.331  1001  1001 I ANNService: Registered service for mtk-gpu
05-24 16:06:09.332   574  1156 I hwservicemanager: Tried to start android.hardware.camera.provider@2.4::ICameraProvider/internal/0 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 16:06:09.332   574  1153 I hwservicemanager: Tried to start vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 16:06:09.332   919   919 E ccci_mdinit: (1):fail to open /mnt/vendor/nvdata/APCFG/APRDCL/CXP_SBP: 2
05-24 16:06:09.332   574  1151 I hwservicemanager: Tried to start vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 16:06:09.332   919   919 I ccci_mdinit: (1):get_cip_sbp_setting, file /custom/etc/firmware/CIP_MD_SBP NOT exists!
05-24 16:06:09.332   919   919 I ccci_mdinit: (1):PRJ_SBP_ID:ro.vendor.mtk_md_sbp_custom_value not exist, using default value
05-24 16:06:09.335  1013  1013 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder'...
05-24 16:06:09.343   919   919 I ccci_mdinit: (1):SBP_SUB_ID:persist.vendor.operator.subid not exist
05-24 16:06:09.343   919   919 I ccci_mdinit: (1):set md boot data:mdl=0 sbp=0 dbg_dump=-1 sbp_sub=0
05-24 16:06:09.343   919   919 E ccci_mdinit: [SYSENV]get_env_info():240 , env_buffer[0] : 0xb4000075f9818030
05-24 16:06:09.344   919   919 I ccci_mdinit: (1):get md_type (null)
05-24 16:06:09.344   919   919 I ccci_mdinit: (1):MD1 set status: mtk.md1.status=bootup 
05-24 16:06:09.344   919   919 I ccci_mdinit: (1):md_id = 0; mdstatusfd = -1
05-24 16:06:09.363  1001  1001 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.apusys@2.1::INeuronApusys/default
05-24 16:06:09.376  1001  1001 I apuware_server: Start NeuronXrp 2.0 service 
05-24 16:06:09.378  1001  1001 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.xrp@2.0::INeuronXrp/default
05-24 16:06:09.406   999   999 I vtservice: [VT][SRV]ServiceManager: 0xb40000799208acf0
05-24 16:06:09.406   999   999 I vtservice: [VT][SRV]before VTService_instantiate
05-24 16:06:09.418  1001  1001 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.hmp@1.0::IApuwareHmp/default
05-24 16:06:09.421   833   833 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-24 16:06:09.422   833   833 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-24 16:06:09.428  1048  1048 I HidlServiceManagement: Registered vendor.mediatek.hardware.pq@2.15::IPictureQuality/default
05-24 16:06:09.428  1048  1048 I HidlServiceManagement: Removing namespace from process name vendor.mediatek.hardware.pq@2.2-service to pq@2.2-service.
05-24 16:06:09.430  1001  1001 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.utils@2.0::IApuwareUtils/default
05-24 16:06:09.442   833   833 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 16:06:09.450   915   915 I HidlServiceManagement: Registered android.frameworks.displayservice@1.0::IDisplayService/default
05-24 16:06:09.472  1070  1070 I NetdagentFirewall: setupIptablesHooks done in oem_iptables_init
05-24 16:06:09.472  1070  1070 I NetdagentController: Initializing iptables: 171.2ms
05-24 16:06:09.472  1070  1070 I Netdagent:  Create CommandService  successfully
05-24 16:06:09.478  1070  1226 I HidlServiceManagement: Registered vendor.mediatek.hardware.netdagent@1.0::INetdagent/default
05-24 16:06:09.518   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.518   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.518   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.518   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.518   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.519   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.519   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.519   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.519   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.519   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.519   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.519   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.519   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.519   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.519   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.520   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.520   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.520   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.545  1052  1052 I mediaswcodec: media swcodec service starting
05-24 16:06:09.546  1052  1052 W mediaswcodec: libminijail[1052]: failed to get path of fd 5: No such file or directory
05-24 16:06:09.547  1052  1052 W mediaswcodec: libminijail[1052]: compile_file: <fd>(39): previous definition here
05-24 16:06:09.548  1052  1052 I CodecServiceRegistrant: Creating software Codec2 service...
05-24 16:06:09.556  1052  1052 I HidlServiceManagement: Registered android.hardware.media.c2@1.2::IComponentStore/software
05-24 16:06:09.558  1052  1052 I CodecServiceRegistrant: Preferred Codec2 HIDL store is set to "default".
05-24 16:06:09.558  1052  1052 I CodecServiceRegistrant: Software Codec2 service created and registered.
05-24 16:06:09.571   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.571   833   833 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:09.574   833   833 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 16:06:09.577  1048  1207 E PQ      : [PQ_SERVICE] aisdr2hdr_pqindex is not found in cust_color.xml
05-24 16:06:09.578   824   824 E ActivityRecognitionHardware: activity_recognition HAL is deprecated. class_init is effectively a no-op
05-24 16:06:09.589  1048  1207 I vendor.mediatek.hardware.pq@2.2-service: transferAIOutput(), register trs callback
05-24 16:06:09.591   833   833 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x1842a487)
05-24 16:06:09.592   833  1237 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-24 16:06:09.594   833  1237 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0x1842a487)
05-24 16:06:09.596   833  1237 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0x1842a487)
05-24 16:06:09.646   919   933 E ccci_fsd(1): FS_OTP_init:otp_get_size:1048576, status=0, type=0!
05-24 16:06:09.649   833   833 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-24 16:06:09.655   833   833 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 16:06:09.674   824   824 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlags
05-24 16:06:09.674   824   824 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlagsImpl
05-24 16:06:09.674   824   824 W Zygote  : Class not found for preloading: android.media.audio.Flags
05-24 16:06:09.682   833   833 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-24 16:06:09.701   833   833 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 16:06:09.701   833   833 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 16:06:09.701   833   833 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 16:06:09.705   833   833 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 16:06:09.705   833   833 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 16:06:09.706   833   833 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-24 16:06:09.714   833   833 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-24 16:06:09.715   833   833 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-24 16:06:09.717   903   903 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 16:06:09.718   903   903 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 16:06:09.719   833   860 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-24 16:06:09.719   833   860 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-24 16:06:09.720   833   860 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 16:06:09.720   903   903 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-24 16:06:09.722   903   903 I AudioFlinger: openOutput() this 0xb400006f40004960, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-24 16:06:09.724   903   903 I AudioFlinger: operator(): Using 3000 ms as standby time
05-24 16:06:09.725   903   903 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 16:06:09.726   903   903 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 16:06:09.727   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 16:06:09.728   903   903 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 16:06:09.745   903  1256 I AudioFlinger: AudioFlinger's thread 0xb40000708d48c760 tid=1256 ready to run
05-24 16:06:09.745   903  1256 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:09.747   903   903 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:09.747   903   903 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:09.747   903   903 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:09.747   903   903 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:09.747   903   903 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:09.747   903   903 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:09.748   903   903 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:09.748   903   903 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:09.748   903   903 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:09.748   903   903 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:09.748   903   903 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:09.748   903   903 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:09.748   903   903 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:09.748   903   903 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:09.748   903   903 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:09.748   903   903 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:09.748   903   903 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:09.748   903   903 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:09.748   903   903 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:09.748   903   903 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:09.748   903   903 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:09.751   903  1256 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:09.753   903   903 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:09.753   903   903 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:09.753   903   903 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:09.753   903   903 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:09.753   903   903 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:09.753   903   903 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:09.753   903   903 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:09.753   903   903 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:09.753   903   903 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:09.753   903   903 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:09.753   903   903 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:09.753   903   903 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:09.753   903   903 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:09.753   903   903 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:09.753   903   903 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:09.755   903   903 W AudioFlinger: moveEffects() AUDIO_SESSION_OUTPUT_MIX not found in orphans, checking other mix
05-24 16:06:09.755   903   903 W AudioFlinger: moveEffects() bad srcIo 0
05-24 16:06:09.757   903   903 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 16:06:09.779   833   833 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x2, output_devices == current_output_devices(0x00000002), return
05-24 16:06:09.782   903   903 I AudioFlinger: openOutput() this 0xb400006f40004960, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x8
05-24 16:06:09.782   833   833 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 16:06:09.783   903   903 I AudioFlinger: HAL output buffer size 2048 frames, normal sink buffer size 2048 frames
05-24 16:06:09.784   903  1260 I AudioFlinger: AudioFlinger's thread 0xb40000708d1c8760 tid=1260 ready to run
05-24 16:06:09.784   903  1260 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:09.785   903  1260 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:09.788   903   903 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 16:06:09.799   919   933 W ccci_mdinit: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.system.suspend.ISystemSuspend/default
05-24 16:06:09.853   833   833 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x8, output_devices == current_output_devices(0x00000002), return
05-24 16:06:09.856   903   903 I AudioFlinger: openOutput() this 0xb400006f40004960, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x4
05-24 16:06:09.856   833   833 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 16:06:09.858   903   903 I AudioFlinger: HAL output buffer size 256 frames, normal sink buffer size 768 frames
05-24 16:06:09.860   903  1279 I AudioFlinger: AudioFlinger's thread 0xb400007089ee7760 tid=1279 ready to run
05-24 16:06:09.861   903  1279 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:09.862   903  1279 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:09.866   903   903 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 16:06:09.888   833  1275 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x4, output_devices == current_output_devices(0x00000002), return
05-24 16:06:09.891   903   903 I AudioFlinger: openOutput() this 0xb400006f40004960, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x8000
05-24 16:06:09.891   833  1275 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 16:06:09.894   903   903 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 16:06:09.900   903  1286 I AudioFlinger: AudioFlinger's thread 0xb400007089d9e760 tid=1286 ready to run
05-24 16:06:09.905  1040  1040 I mtkcam-devicemgr: [initialize] +
05-24 16:06:09.909   999   999 W AVSync  : initFD done, g_fd_name: /dev/ccci_imsdc
05-24 16:06:09.909   572   572 I auditd  : avc:  denied  { find } for pid=999 uid=1000 name=vendor.mediatek.hardware.videotelephony.IVideoTelephony/default scontext=u:r:vtservice:s0 tcontext=u:object_r:default_android_service:s0 tclass=service_manager permissive=0
05-24 16:06:09.909   999  1293 W ServiceManagerCppClient: Failed to get isDeclared for vendor.mediatek.hardware.videotelephony.IVideoTelephony/default: Status(-1, EX_SECURITY): 'SELinux denied for service.'
05-24 16:06:09.937   833  1241 W audio_messenger_ipi: read_ipi_thread(), read dsp fail!! length_read: 0, errno: 0
05-24 16:06:09.948  1297  1297 E DEBUG   : failed to read process info: failed to open /proc/833: No such file or directory
05-24 16:06:10.107  1297  1297 F DEBUG   : Cmdline: /vendor/bin/hw/android.hardware.audio.service
05-24 16:06:10.107  1297  1297 F DEBUG   : pid: 833, tid: 1275, name: HwBinder:833_3  >>> /vendor/bin/hw/android.hardware.audio.service <<<
05-24 16:06:10.107  1297  1297 F DEBUG   :       #01 pc 0002651d  /vendor/lib/hw/<EMAIL> (android::hardware::audio::V7_1::implementation::StreamOut::setLatencyModeCallback(android::sp<android::hardware::audio::V7_1::IStreamOutLatencyModeCallback> const&)+36) (BuildId: 544a581a8a5464266e14c74f5aea9a1c)
05-24 16:06:10.107  1297  1297 F DEBUG   :       #02 pc 00043c0f  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::_hidl_setLatencyModeCallback(android::hidl::base::V1_0::BnHwBase*, android::hardware::Parcel const&, android::hardware::Parcel*, std::__1::function<void (android::hardware::Parcel&)>)+198) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 16:06:10.107  1297  1297 F DEBUG   :       #03 pc 00044703  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::onTransact(unsigned int, android::hardware::Parcel const&, android::hardware::Parcel*, unsigned int, std::__1::function<void (android::hardware::Parcel&)>)+1926) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 16:06:10.150   903   971 E HalDeathHandler: HAL server crashed, audio server is restarting
05-24 16:06:10.172  1005  1022 E mnld    : thread_adc_capture_init: set IOCTL_EMI_MEMORY_INIT failed,(Success)
05-24 16:06:10.183  1005  1031 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 16:06:10.183  1005  1031 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:1
05-24 16:06:10.247  1071  1071 E [GF_HAL][ShenzhenSensor]: [init] gainvalue: 150/100
05-24 16:06:10.247  1071  1071 E [GF_HAL][ShenzhenSensor]: [init] expotime 38
05-24 16:06:10.247  1071  1071 E [GF_HAL][ShenzhenSensor]: [init] @@@@@ mQRCode=Z918095013A0061493,len=18
05-24 16:06:10.248  1071  1071 E [GF_HAL][ShenzhenSensor]: [init] module_type = 0x6
05-24 16:06:10.248  1071  1071 E [GF_HAL][ShenzhenSensor]: [init] lens_type = 0xa
05-24 16:06:10.265  1045  1045 I thermal_src: u_CATM_ON == -1, get catm init val again
05-24 16:06:10.265  1045  1045 I thermal_src: ta_catm_init_flow
05-24 16:06:10.265  1045  1045 I thermal_src: u_CATM_ON == -1, get catm init val
05-24 16:06:10.307  1067  1067 W HidlServiceManagement: Waited one second for vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default
05-24 16:06:10.308   574   574 I hwservicemanager: Since vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 16:06:10.308  1067  1067 I HidlServiceManagement: getService: Trying again for vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default...
05-24 16:06:10.309   574  1310 I hwservicemanager: Tried to start vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 16:06:10.452  1304  1316 W system_server: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: stats
05-24 16:06:10.453  1304  1316 W BpBinder: Linking to death on android.os.IStatsd but there are no threads (yet?) listening to incoming transactions. See ProcessState::startThreadPool and ProcessState::setThreadPoolMaxThreadCount. Generally you should setup the binder threadpool before other initialization steps.
05-24 16:06:10.457   823   827 I ServiceManagerCppClient: Waiting for service 'statscompanion' on '/dev/binder'...
05-24 16:06:10.610  1304  1304 E system_server: memevent listener failed to initialize, not supported kernel
05-24 16:06:10.617  1304  1304 W Binder  : 	at android.os.IServiceManager$Stub$Proxy.checkService(IServiceManager.java:507)
05-24 16:06:10.617  1304  1304 W Binder  : 	at android.os.ServiceManagerProxy.checkService(ServiceManagerNative.java:73)
05-24 16:06:10.617  1304  1304 W Binder  : 	at android.os.ServiceManagerProxy.getService2(ServiceManagerNative.java:69)
05-24 16:06:10.617  1304  1304 W Binder  : 	at android.os.ServiceManager.rawGetService(ServiceManager.java:430)
05-24 16:06:10.617  1304  1304 W Binder  : 	at android.os.ServiceManager.getService(ServiceManager.java:175)
05-24 16:06:10.617  1304  1304 W Binder  : 	at android.app.ActivityThread.initializeSystemThread(ActivityThread.java:8715)
05-24 16:06:10.617  1304  1304 W Binder  : 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:597)
05-24 16:06:10.617  1304  1304 W Binder  : 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:889)
05-24 16:06:10.632  1304  1304 I SystemServerInitThreadPool: Creating instance with 8 threads
05-24 16:06:10.655  1304  1338 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-com.android.providers.media.module.xml
05-24 16:06:10.657  1304  1338 I SystemConfig: Reading permissions from /system/etc/sysconfig/app.grapheneos.gmscompat.xml
05-24 16:06:10.659  1304  1338 I SystemConfig: Reading permissions from /system/etc/sysconfig/app.grapheneos.networklocation.xml
05-24 16:06:10.660  1304  1338 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-platform-full-base.xml
05-24 16:06:10.661  1304  1338 I SystemConfig: Reading permissions from /system/etc/sysconfig/framework-sysconfig.xml
05-24 16:06:10.662  1304  1338 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-asl-files.xml
05-24 16:06:10.662  1304  1338 I SystemConfig: Reading permissions from /system/etc/sysconfig/hiddenapi-whitelist-co.aospa.sense.xml
05-24 16:06:10.663  1304  1338 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-strict-signature.xml
05-24 16:06:10.664  1304  1338 I SystemConfig: Reading permissions from /system/etc/sysconfig/backup.xml
05-24 16:06:10.665  1304  1338 I SystemConfig: Reading permissions from /system/etc/sysconfig/hiddenapi-package-whitelist.xml
05-24 16:06:10.666  1304  1338 I SystemConfig: Reading permissions from /system/etc/sysconfig/enhanced-confirmation.xml
05-24 16:06:10.667  1304  1338 I SystemConfig: Reading permissions from /system/etc/sysconfig/package-shareduid-allowlist.xml
05-24 16:06:10.668  1304  1338 I SystemConfig: Reading permissions from /system/etc/sysconfig/initial-package-stopped-states.xml
05-24 16:06:10.668  1304  1338 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-platform-handheld-system.xml
05-24 16:06:10.669  1304  1338 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-platform.xml
05-24 16:06:10.672  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/DigitalWellbeing.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.672  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/GoogleContacts.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.672  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/GoogleContactsSyncAdapter.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.672  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/Drive.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.672  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/GoogleMaps.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.673  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.intentresolver.xml
05-24 16:06:10.674  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/GoogleCalendar.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.674  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/CarrierServices.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.674  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/GoogleDialer.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.674  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/GoogleOneTimeInitializer.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.674  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.documentsui.xml
05-24 16:06:10.676  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/app.grapheneos.networklocation.xml
05-24 16:06:10.676  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/DeviceHealthServices.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.676  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/ExtraFiles.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.677  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/org.apache.http.legacy.xml
05-24 16:06:10.678  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.live_wallpaper.xml
05-24 16:06:10.678  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/GooglePlayStore.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.679  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/privapp-permissions-mediatek.xml
05-24 16:06:10.681  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/GBoard.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.681  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/GoogleKeep.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.681  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/GoogleRestore.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.682  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.nfc_extras.xml
05-24 16:06:10.683  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.networkstack.xml
05-24 16:06:10.683  1071  1071 E [GF_HAL][FingerprintCore]: [init_report_data] algo version is V03.02.02.230.005
05-24 16:06:10.684  1071  1071 E [GF_HAL][FingerprintCore]: [init_report_data] lcdtype_prop = SDC
05-24 16:06:10.684  1071  1071 E [GF_HAL][FingerprintCore]: [init_report_data] type = V03.02.02.230.005_S_SDC
05-24 16:06:10.685  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.credentials.xml
05-24 16:06:10.686  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/privapp-permissions-platform.xml
05-24 16:06:10.697  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/android.test.mock.xml
05-24 16:06:10.700  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/android.test.base.xml
05-24 16:06:10.701  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.webview.xml
05-24 16:06:10.702  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/android.hardware.biometrics.face.xml
05-24 16:06:10.702  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/GoogleMessages.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.702  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/app.grapheneos.logviewer.xml
05-24 16:06:10.703  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/GoogleServicesFramework.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.703  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/GoogleCalendarSyncAdapter.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.703  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/javax.obex.xml
05-24 16:06:10.704  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.location.provider.xml
05-24 16:06:10.704  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/GoogleLocationHistory.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.704  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/android.test.runner.xml
05-24 16:06:10.704  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/GooglePhotos.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.704  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.media.remotedisplay.xml
05-24 16:06:10.705  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.sip.voip.xml
05-24 16:06:10.705  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.window_magnification.xml
05-24 16:06:10.706  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/GoogleClock.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.706  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/GoogleCalculator.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.706  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.future.usb.accessory.xml
05-24 16:06:10.706  1071  1071 I HidlServiceManagement: Registered vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default
05-24 16:06:10.706  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.mediadrm.signer.xml
05-24 16:06:10.706  1071  1071 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.biometrics.fingerprint@2.1-service to fingerprint@2.1-service.
05-24 16:06:10.706  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/privapp_whitelist_co.aospa.sense.xml
05-24 16:06:10.707  1304  1338 I SystemConfig: Non-xml file /system/etc/permissions/GmsCore.prop in /system/etc/permissions directory, ignoring
05-24 16:06:10.707  1304  1338 I SystemConfig: Reading permissions from /system/etc/permissions/platform.xml
05-24 16:06:10.708   574   574 W hwservicemanager: Detected instance of android.hardware.biometrics.fingerprint@2.3::IBiometricsFingerprint (pid: 1067) registering over instance of or with base of android.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint (pid: 1071).
05-24 16:06:10.709  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.ese.xml
05-24 16:06:10.709  1067  1067 I HidlServiceManagement: Registered android.hardware.biometrics.fingerprint@2.3::IBiometricsFingerprint/default
05-24 16:06:10.709  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.xml
05-24 16:06:10.709  1067  1067 I HidlServiceManagement: Removing namespace from <NAME_EMAIL>6893 to fingerprint@2.3-service.mt6893.
05-24 16:06:10.709  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.location.gps.xml
05-24 16:06:10.710  1304  1304 I SystemServiceManager: Starting com.android.server.security.FileIntegrityService
05-24 16:06:10.710  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.multitouch.distinct.xml
05-24 16:06:10.710  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.verified_boot.xml
05-24 16:06:10.710  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.mediatek.hardware.vow.xml
05-24 16:06:10.711  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.accelerometer.xml
05-24 16:06:10.711  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.bluetooth_le.xml
05-24 16:06:10.711  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.flash-autofocus.xml
05-24 16:06:10.712  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.stepcounter.xml
05-24 16:06:10.712  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.gyroscope.xml
05-24 16:06:10.712  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.wifi.passpoint.xml
05-24 16:06:10.712  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.telephony.ims.xml
05-24 16:06:10.713  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.bluetooth.xml
05-24 16:06:10.713  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.raw.xml
05-24 16:06:10.714  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.android.nfc_extras.xml
05-24 16:06:10.714  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.vulkan.version.xml
05-24 16:06:10.714  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.front.xml
05-24 16:06:10.715  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.hifi_sensors.xml
05-24 16:06:10.715  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.opengles.deqp.level.xml
05-24 16:06:10.716  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.xml
05-24 16:06:10.716  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/nfc_features.xml
05-24 16:06:10.716  1304  1304 I SystemServiceManager: Starting com.android.server.pm.Installer
05-24 16:06:10.717  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.usb.accessory.xml
05-24 16:06:10.717  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.telephony.gsm.xml
05-24 16:06:10.718  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.wifi.xml
05-24 16:06:10.718  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.freeform_window_management.xml
05-24 16:06:10.719  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.hcef.xml
05-24 16:06:10.719  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.midi.xml
05-24 16:06:10.720  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.usb.host.xml
05-24 16:06:10.720  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.fingerprint.xml
05-24 16:06:10.720  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.multitouch.jazzhand.xml
05-24 16:06:10.721  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.multitouch.xml
05-24 16:06:10.721  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.ipsec_tunnels.xml
05-24 16:06:10.722  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.se.omapi.uicc.xml
05-24 16:06:10.722  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.opengles.aep.xml
05-24 16:06:10.722  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.se.omapi.ese.xml
05-24 16:06:10.723  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/handheld_core_hardware.xml
05-24 16:06:10.724  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.hce.xml
05-24 16:06:10.724  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.faketouch.xml
05-24 16:06:10.724  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.light.xml
05-24 16:06:10.725  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.audio.low_latency.xml
05-24 16:06:10.725  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.vulkan.deqp.level.xml
05-24 16:06:10.725  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.vulkan.level.xml
05-24 16:06:10.726  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.xml
05-24 16:06:10.726  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.vulkan.compute.xml
05-24 16:06:10.726  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.wifi.direct.xml
05-24 16:06:10.727  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.nxp.mifare.xml
05-24 16:06:10.727  1304  1304 I SystemServiceManager: Starting com.android.server.os.DeviceIdentifiersPolicyService
05-24 16:06:10.727  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.stepdetector.xml
05-24 16:06:10.727  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.compass.xml
05-24 16:06:10.728  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.proximity.xml
05-24 16:06:10.728  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.mediatek.hardware.vow_dsp.xml
05-24 16:06:10.728  1304  1304 I SystemServiceManager: Starting com.android.server.flags.FeatureFlagsService
05-24 16:06:10.728  1304  1338 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.full.xml
05-24 16:06:10.730  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-overlays.xml
05-24 16:06:10.731  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/GoogleCamera_6gb_or_more_ram.xml
05-24 16:06:10.731  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/nga.xml
05-24 16:06:10.732  1304  1304 I SystemServiceManager: Starting com.android.server.uri.UriGrantsManagerService$Lifecycle
05-24 16:06:10.732  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-org.lineageos.glimpse.xml
05-24 16:06:10.732  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-google-install-constraints-package-allowlist.xml
05-24 16:06:10.733  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/quick_tap.xml
05-24 16:06:10.733  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/initial-package-stopped-states-org.lineageos.etar.xml
05-24 16:06:10.733  1304  1304 I SystemServiceManager: Starting com.android.server.powerstats.PowerStatsService
05-24 16:06:10.734  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/google.xml
05-24 16:06:10.735  1304  1338 I SystemConfig: Adding association: com.google.android.as <- com.android.bluetooth.services
05-24 16:06:10.735  1304  1338 I SystemConfig: Adding association: com.google.android.as <- com.google.android.bluetooth.services
05-24 16:06:10.737  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/pixel_experience_2017.xml
05-24 16:06:10.738  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-voltage-product.xml
05-24 16:06:10.739  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/pixel_2016_exclusive.xml
05-24 16:06:10.739  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/adaptivecharging.xml
05-24 16:06:10.739  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-handheld-product.xml
05-24 16:06:10.739   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.power.stats@1.0::IPowerStats/default in either framework or device VINTF manifest.
05-24 16:06:10.740  1304  1304 E PowerStatsService: Unable to get power.stats HAL service.
05-24 16:06:10.740  1304  1304 E PowerStatsService: nativeInit failed to connect to power.stats HAL
05-24 16:06:10.740  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/com.android.messaging.allowlist.xml
05-24 16:06:10.741  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-google-hiddenapi-package-whitelist.xml
05-24 16:06:10.741  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/voltage-component-overrides.xml
05-24 16:06:10.742  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-google-staged-installer-whitelist.xml
05-24 16:06:10.742  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/google_build.xml
05-24 16:06:10.743  1304  1304 I HidlServiceManagement: Registered android.frameworks.stats@1.0::IStats/default
05-24 16:06:10.743  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-org.lineageos.etar.xml
05-24 16:06:10.743  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/com.android.deskclock_allowlist.xml
05-24 16:06:10.744  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/dreamliner.xml
05-24 16:06:10.744  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/initial-package-stopped-states-org.lineageos.glimpse.xml
05-24 16:06:10.744  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/initial-package-stopped-states-org.lineageos.aperture.xml
05-24 16:06:10.745  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-preinstalled-packages-product-pixel-2017-and-newer.xml
05-24 16:06:10.745  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/d2d_cable_migration_feature.xml
05-24 16:06:10.746  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-telephony-product.xml
05-24 16:06:10.747  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/org.lineageos.etar.allowlist.xml
05-24 16:06:10.748  1304  1304 I SystemServiceManager: Starting com.android.server.permission.access.AccessCheckingService
05-24 16:06:10.748  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-org.lineageos.aperture.xml
05-24 16:06:10.749  1304  1338 I SystemConfig: Reading permissions from /product/etc/sysconfig/nexus.xml
05-24 16:06:10.749  1304  1304 I SystemServiceManager: Starting com.android.server.wm.ActivityTaskManagerService$Lifecycle
05-24 16:06:10.751  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.maps.xml
05-24 16:06:10.753  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.widevine.software.drm.xml
05-24 16:06:10.753  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/privapp-permissions-hotword.xml
05-24 16:06:10.754  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/android.software.freeform_window_management.xml
05-24 16:06:10.754  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.gms.xml
05-24 16:06:10.756  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/privapp-permissions-googleapps-turbo.xml
05-24 16:06:10.757  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.wellbeing.xml
05-24 16:06:10.757  1304  1304 I SystemServiceManager: Starting com.android.server.am.ActivityManagerService$Lifecycle
05-24 16:06:10.757  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/privapp-permissions-xhotword.xml
05-24 16:06:10.758  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.onetimeinitializer.xml
05-24 16:06:10.758  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.dialer.support.xml
05-24 16:06:10.759  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.keep.xml
05-24 16:06:10.759  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-split-permissions-google.xml
05-24 16:06:10.760  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.dialer.xml
05-24 16:06:10.760  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-privapp-permissions-google-p.xml
05-24 16:06:10.764  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.turbo.xml
05-24 16:06:10.764  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.vending.xml
05-24 16:06:10.765  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/privapp_whitelist_com.android.dialer-ext.xml
05-24 16:06:10.766  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.dialer.xml
05-24 16:06:10.766  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.media.effects.xml
05-24 16:06:10.767  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.ims.xml
05-24 16:06:10.767  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.messaging.xml
05-24 16:06:10.768  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/android.software.angle.xml
05-24 16:06:10.768  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.imsserviceentitlement.xml
05-24 16:06:10.769  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.settings.intelligence.xml
05-24 16:06:10.770  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-privapp-permissions-google-se.xml
05-24 16:06:10.772  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-privapp-permissions-google.xml
05-24 16:06:10.772  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.contacts.xml
05-24 16:06:10.773  1304  1338 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.restore.xml
05-24 16:06:10.774  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/sysconfig/allowlist_com.stevesoltys.seedvault.xml
05-24 16:06:10.774  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/sysconfig/android.telephony.satellite.xml
05-24 16:06:10.775  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.emergency.xml
05-24 16:06:10.775  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_whitelist_com.android.launcher3-ext.xml
05-24 16:06:10.776  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_allowlist_com.libremobileos.freeform.xml
05-24 16:06:10.776  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.hotwordenrollment.common.util.xml
05-24 16:06:10.776  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/permissions_com.stevesoltys.seedvault.xml
05-24 16:06:10.777  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_whitelist_org.lineageos.setupwizard.xml
05-24 16:06:10.777  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/androidx.window.sidecar.xml
05-24 16:06:10.778  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.bluetooth.bthelper.xml
05-24 16:06:10.778  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_whitelist_io.chaldeaprjkt.gamespace.xml
05-24 16:06:10.778  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/android.software.theme_picker.xml
05-24 16:06:10.779  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.systemui.xml
05-24 16:06:10.779  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.settings.xml
05-24 16:06:10.780  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.carrierconfig.xml
05-24 16:06:10.781  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/androidx.window.extensions.xml
05-24 16:06:10.781  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_allowlist_com.libremobileos.sidebar.xml
05-24 16:06:10.782  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp-permissions-custom.xml
05-24 16:06:10.782  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.launcher3.xml
05-24 16:06:10.783  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.google.android.gsf.xml
05-24 16:06:10.784  1304  1338 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.storagemanager.xml
05-24 16:06:10.785  1304  1338 I SystemConfig: Reading permissions from /apex/com.android.tethering/etc/permissions/permissions.xml
05-24 16:06:10.786  1304  1338 I SystemConfig: Reading permissions from /apex/com.android.mediaprovider/etc/permissions/com.android.photopicker.xml
05-24 16:06:10.787  1304  1338 I SystemConfig: Reading permissions from /apex/com.android.mediaprovider/etc/permissions/com.android.providers.media.module.xml
05-24 16:06:10.788  1304  1338 I SystemConfig: Reading permissions from /apex/com.android.cellbroadcast/etc/permissions/com.android.cellbroadcastreceiver.module.xml
05-24 16:06:10.789  1304  1338 I SystemConfig: Reading permissions from /apex/com.android.cellbroadcast/etc/permissions/com.android.cellbroadcastservice.xml
05-24 16:06:10.790  1304  1338 I SystemConfig: Reading permissions from /apex/com.android.nfcservices/etc/permissions/com.android.nfc.xml
05-24 16:06:10.791  1304  1338 I SystemConfig: Reading permissions from /apex/com.android.apex.cts.shim/etc/permissions/signature-permission-allowlist.xml
05-24 16:06:10.792  1304  1338 I SystemConfig: Reading permissions from /apex/com.android.adservices/etc/permissions/com.android.adservices.api.xml
05-24 16:06:10.794  1304  1338 I SystemConfig: Reading permissions from /apex/com.android.permission/etc/permissions/com.android.permissioncontroller.xml
05-24 16:06:10.796  1304  1338 I SystemConfig: Reading permissions from /apex/com.android.ipsec/etc/permissions/android.net.ipsec.ike.xml
05-24 16:06:10.797  1304  1338 I SystemConfig: Reading permissions from /apex/com.android.healthfitness/etc/permissions/com.android.healthconnect.controller.xml
05-24 16:06:10.798  1304  1338 I SystemConfig: Reading permissions from /apex/com.android.extservices/etc/permissions/android.ext_sminus.services.xml
05-24 16:06:10.800  1304  1338 I SystemConfig: Reading permissions from /apex/com.android.devicelock/etc/permissions/com.android.devicelockcontroller.xml
05-24 16:06:10.801  1304  1338 I SystemConfig: Reading permissions from /apex/com.android.btservices/etc/permissions/com.android.bluetooth.xml
05-24 16:06:10.803  1304  1338 I incfs   : Initial API level of the device: 30
05-24 16:06:10.806  1304  1348 E system_server: memevent deregister all events failed, failure to initialize
05-24 16:06:10.806  1304  1348 E OomConnection: failed waiting for OOM events: java.lang.RuntimeException: Failed to initialize memevents listener
05-24 16:06:10.910  1304  1323 W android.permission.PermissionManager: Missing ActivityManager; assuming 1047 does not hold android.permission.MANAGE_APP_OPS_MODES
05-24 16:06:10.912  1304  1304 I SystemServiceManager: Starting com.android.server.pm.DataLoaderManagerService
05-24 16:06:10.916  1304  1304 I SystemServiceManager: Starting com.android.server.power.PowerManagerService
05-24 16:06:10.921  1304  1304 I PowerHalLoader: Successfully connected to Power HAL AIDL service.
05-24 16:06:10.923  1304  1304 I SystemServiceManager: Starting com.android.server.power.ThermalManagerService
05-24 16:06:10.924  1304  1304 I SystemServiceManager: Starting com.android.server.recoverysystem.RecoverySystemService$Lifecycle
05-24 16:06:10.926  1304  1304 I SystemServiceManager: Starting com.android.server.lights.LightsService
05-24 16:06:10.927  1304  1304 I SystemServiceManager: Starting com.android.server.display.DisplayManagerService
05-24 16:06:10.933  1304  1304 I SystemServiceManager: Starting phase 100
05-24 16:06:10.937  1304  1334 E DisplayManagerService: Default display is null for info request from uid 1000
05-24 16:06:10.958  1304  1304 I UserManagerService: Upgrading users from userVersion 11 to 11
05-24 16:06:11.105  1304  1304 W android.permission.PermissionManager: Missing ActivityManager; assuming 1000 holds android.permission.SET_PREFERRED_APPLICATIONS
05-24 16:06:11.135  1304  1304 W PackageManager: No package known for package restrictions com.android.adservices
05-24 16:06:11.160  1304  1304 W PackageManager: No package known for package restrictions com.android.permission
05-24 16:06:11.183  1005  1031 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 16:06:11.183  1005  1031 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:2
05-24 16:06:11.204  1304  1304 W PackageManager: No package known for package restrictions com.android.btservices
05-24 16:06:11.253   919   919 I ccci_mdinit: (1):MD1 set status: mtk.md1.status=ready 
05-24 16:06:11.253   919   919 I ccci_mdinit: (1):start_service init.svc.emdlogger1, but returned 0, maybe has no this property
05-24 16:06:11.254   919   919 I ccci_mdinit: (1):wait_for_property: is_stop_wait: 0
05-24 16:06:11.264   919   919 I ccci_mdinit: (1):start_service init.svc.vendor.gsm0710muxd, but returned 0, maybe has no this property
05-24 16:06:11.267   919   919 I ccci_mdinit: (1):wait_for_property: is_stop_wait: 0
05-24 16:06:11.267   919   919 I ccci_mdinit: (1):wait_for_property:success(init.svc.vendor.gsm0710muxd=running), loop:600
05-24 16:06:11.327  1304  1304 W PackageManager: No package known for package restrictions com.android.extservices
05-24 16:06:11.381  1304  1304 W PackageManager: No package known for package restrictions com.android.nfcservices
05-24 16:06:11.387  1304  1304 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.noCutout on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.font.sanfrancisco on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package org.omnirom.omnijaws on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.android.systemui.clocks.metro on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package in.zeta.android on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.android.cts.priv.ctsshim on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package org.voltage.theme.font.dosis on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.google.android.youtube on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.android.uwb.resources on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.nikgapps.overlay.messages on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.corner on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.android.adservices.api on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.double on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.themepicker on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.rifsxd.ksunext on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package app.grapheneos.gmscompat.config on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.settings on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.truecaller on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.android.healthconnect.controller on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.manhwabuddy on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.luascans on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.xperia.settings on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.gradicon.android on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.google.android.onetimeinitializer on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.android.health.connect.backuprestore on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.android.virtualmachine.res on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.mxtech.videoplayer.pro on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.systemui on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.android.managedprovisioning.auto_generated_rro_product__ on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package org.omnirom.omnijaws.auto_generated_rro_product__ on user 0
05-24 16:06:11.388  1304  1304 W PackageSettings: Missing permission state for package com.apkupdater on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.settings on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.narrow on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.systemui on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.systemui on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.settings on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.android on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.documentsui on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.externalstorage on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.server.deviceconfig.resources on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.settings on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.nikgapps.overlay.googlelocationhistory on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.whatsapp on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.companiondevicemanager on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.coderstory.toolkit on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package io.github.jica98 on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.gourmetscans on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package app.grapheneos.logviewer on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.systemui on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.systemui.auto_generated_rro_product__ on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.google.android.apps.messaging on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.mediatek.engineermode on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.federatedcompute.services on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.android on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.carrierconfig.mt6893 on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package io.chaldeaprjkt.gamespace.auto_generated_rro_product__ on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.lonelycatgames.Xplore on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.system.monet.snowpaintdrop on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.scyllascans on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.nikgapps.overlay.googlephotos on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.xperia.systemui on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package app.komikku on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package jp.pxv.android on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.systemui on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.themepicker on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package net.thunderbird.android on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.systemui.clocks.bignum on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.avatarpicker on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.xayah.databackup.foss on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.systemui on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.font.rookery on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.snowmtl on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.systemui.plugin.globalactions.wallet on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.safetycenter.resources on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package org.zwanoo.android.speedtest on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.system.monet.vivid on user 0
05-24 16:06:11.389  1304  1304 W PackageSettings: Missing permission state for package com.android.vending on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.pacprocessor on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.simappdialog on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package org.protonaosp.deviceconfig.auto_generated_rro_product__ on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package eu.darken.sdmse on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.systemui on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.systemui.clocks.growth on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.connectivity.resources on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.hole on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.tall on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.wide on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.ancient.telephonyoverlay on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.networkstack.overlay on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package org.lineageos.glimpse.frameworksbaseoverlay on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.modulemetadata on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.certinstaller on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.carrierconfig on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.launcher on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.lorn.android on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.internal.systemui.navbar.threebutton on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.brave.browser on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.aurorascans on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.talkback on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.wifi.dialog on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.nikgapps.overlay.gmscore on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.hotwordenrollment.xgoogle on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.oneplusparts.overlay.rm on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package ru.mike.updatelocker on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.launcher on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.launcher on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.philiascans on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package co.aospa.sense.auto_generated_rro_product__ on user 0
05-24 16:06:11.390  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.kewnscans on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.shojoscans on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.settings on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package org.lineageos.cupida.frameworkresoverlay on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.android on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package ru.andr7e.deviceinfohw on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.android.egg on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.android.launcher3 on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package livio.pack.lang.en_US on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.google.android.trichromelibrary_710306033 on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package org.lineageos.settings.doze.overlay on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.android.backupconfirm on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.font.fluidsans on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.axiel7.anihyou on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.android.settings.auto_generated_rro_vendor__ on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package org.voltage.theme.font.opposans on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.android.systemui.auto_generated_rro_vendor__ on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.google.android.deskclock on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package org.protonaosp.deviceconfig on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.wmods.wppenhacer on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.android.systemui.clocks.numoverlap on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.android.statementservice on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.xperia.android on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.google.android.gm on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package org.calyxos.backup.contacts on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.launcher on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.voltageos.colorstub on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.webtoons on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.oplus.doze.overlay_system on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.settings on user 0
05-24 16:06:11.391  1304  1304 W PackageSettings: Missing permission state for package com.android.settings.intelligence on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.systemui.clocks.calligraphy on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.systemui.accessibility.accessibilitymenu on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package org.voltage.theme.font.linotte on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.oplus.doze.overlay_systemui on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.themepicker on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package org.adaway on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.zeptoconsumerapp on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.f0x1d.logfox on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.ancient.frameworkresoverlay.mt6893 on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package ru.tech.imageresizershrinker on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.mangademon on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.ancient.systemuioverlay.mt6893 on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package org.lineageos.setupwizard.auto_generated_rro_product__ on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.sharedstoragebackup on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.launcher on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.printspooler on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.hotwordenrollment.okgoogle on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.emergency.auto_generated_rro_product__ on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.settings on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.dreams.basic on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.settings.overlay.oplus.target on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.launcher on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.providers.settings.auto_generated_rro_product__ on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package org.mozilla.focus on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.photopicker on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.systemui on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.webview on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.permissioncontroller.overlay on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package app.grapheneos.networklocation on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.google.android.apps.wellbeing on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.coffeemanga on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.rkpdapp on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.google.android.dialer on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.launcher on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.bips on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.themepicker on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.settings on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.intentresolver.auto_generated_rro_product__ on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.android on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package org.eu.droid_ng.jellyfish on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.android.musicfx on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package app.vitune.android on user 0
05-24 16:06:11.392  1304  1304 W PackageSettings: Missing permission state for package com.google.android.apps.docs on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package ellipi.messenger on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.systemui on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.asurascans on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package app.grapheneos.gmscompat.lib on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package duy.com.text_converter on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.customization.themes on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.font.googlesans on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package net.one97.paytm on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.cellbroadcastreceiver on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.google.android.webview on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package android.ext.shared on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.bluetooth.bthelper.auto_generated_rro_product__ on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.google.android.contactkeys on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.google.android.contacts on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.google.android.syncadapters.contacts on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.system.monet.expresso on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.nikgapps.overlay.googleclock on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package org.calyxos.datura on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.themepicker on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.systemui.clocks.inflate on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.google.android.calculator on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.adultwebtoon on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package org.voltage.theme.font.manrope on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.printservice.recommendation on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package app.grapheneos.AppCompatConfig on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package me.jmh.authenticatorpro on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.systemui on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.mangadex on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.kaiscans on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.google.android.gms on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.google.android.ims on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.system.theme.black on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package android.ext.services on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.wifi.resources on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.plumpy.systemui on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.cameraextensions on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.packageinstaller on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.carrierdefaultapp on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.magusmanga on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.gradicon.systemui on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.necroscans on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.font.opsans on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.batoto on user 0
05-24 16:06:11.393  1304  1304 W PackageSettings: Missing permission state for package com.android.credentialmanager on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.android on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.font.notoserifsource on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.android on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.proxyhandler on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.launcher3.auto_generated_rro_product__ on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.waterfall on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.intentresolver on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.systemui on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package io.github.muntashirakon.AppManager on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.internal.systemui.navbar.transparent on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.providers.settings.overlay on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.android on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.google.android.apps.photos on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.android on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.managedprovisioning on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.aeinscans on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package io.github.dovecoteescapee.byedpi on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.lorn.systemui on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.dreams.phototable on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package org.lineageos.settings.doze.auto_generated_rro_product__ on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.networkstack.tethering.mt6893 on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.launcher on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package org.voltage.theme.font.recursive_casual on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package air.kukulive.mailnow on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.looker.droidify on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.android on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.smspush on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.role.notes.enabled on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.berdik.letmedowngrade on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.wallpaper.livepicker on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package org.lineageos.aperture on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.cellbroadcastreceiver.module on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.systemui.clocks.flex on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.apps.tag on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.inputmethod.latin.auto_generated_rro_product__ on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.power.hub.udfps.icons on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.imsserviceentitlement on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.appsearch.apk on user 0
05-24 16:06:11.394  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.launcher on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.valirscans on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package org.voltage.theme.font.recursive_linear on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.avoidAppsInCutout on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package org.lineageos.cupida.wifioverlay on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package idm.internet.download.manager.plus on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.plumpy.android on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.arvenscans on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.melody on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.android on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.android.storagemanager on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.zerodha.kite3 on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.android.bookmarkprovider on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.fitbit.FitbitMobile on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.aurora.systemui on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.launcher on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package uk.akane.omni on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package org.protonaosp.theme.font.linotte on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.google.android.apps.googlecamera.fishfood on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.voltage.overlay.customization.keyboard.nonavbar on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.google.android.apps.turbo on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.enryumanga on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.google.android.safetycore on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.whalemanga on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package app.grapheneos.gmscompat on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.themepicker on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.android.wifi.resources.mt6893 on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package proton.android.pass on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.launcher on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.android.wallpaper on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.nikgapps.overlay.turbo on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.android.vpndialogs on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.goping.user on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.nyxscans on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.google.android.keep on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.android.angle on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.mangareadorg on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.linkbox.plus.android on user 0
05-24 16:06:11.395  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.themepicker on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.android.sdksandbox on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.android.wallpaperbackup on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.stevesoltys.seedvault.auto_generated_rro_product__ on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.android.settings.auto_generated_rro_product__ on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.voltageos.Covers on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.android.providers.media.module on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.power.hub.udfps.animations on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package in.swiggy.android on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.android.hotspot2.osulogin on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.solarmtl on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.google.android.gms.location.history on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.intsig.camscanner on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.android.internal.systemui.navbar.gestural on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package co.aospa.sense.settings.overlay on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.themepicker on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.wstxda.viper4android on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.harimanga on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.mangadistrict on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.android.bluetoothmidiservice on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.ancient.settingsoverlay.mt6893 on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package org.akanework.gramophone on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.android.permissioncontroller on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.android.storagemanager.auto_generated_rro_product__ on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.zerodha.coin on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.android.dialer.auto_generated_rro_vendor__ on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package app.customerportal.tachyon1 on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.android.imsserviceentitlement.auto_generated_rro_product__ on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.android.phone.auto_generated_rro_product__ on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package android.auto_generated_rro_product__ on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.ezmanga on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.templescan on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.android.dialer.auto_generated_rro_product__ on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.settings on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.themepicker on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.android.ondevicepersonalization.services on user 0
05-24 16:06:11.396  1304  1304 W PackageSettings: Missing permission state for package com.android.documentsui.overlay on user 0
05-24 16:06:11.397  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.anisascans on user 0
05-24 16:06:11.397  1304  1304 W PackageSettings: Missing permission state for package com.android.captiveportallogin on user 0
05-24 16:06:11.397  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.aurora.android on user 0
05-24 16:06:11.397  1304  1304 W PackageSettings: Missing permission state for package com.android.devicelockcontroller on user 0
05-24 16:06:11.397  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.settings on user 0
05-24 16:06:11.397  1304  1304 W PackageSettings: Missing permission state for package com.tukann.confinedandhorny on user 0
05-24 16:06:11.397  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.settings on user 0
05-24 16:06:11.397  1304  1304 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.likemanga on user 0
05-24 16:06:11.397  1304  1304 W PackageSettings: Missing permission state for package com.nikgapps.overlay.wellbeing on user 0
05-24 16:06:11.397  1304  1304 W PackageSettings: Missing permission state for package com.nikgapps.overlay.dialer on user 0
05-24 16:06:11.397  1304  1304 W PackageSettings: Missing permission state for package com.google.android.inputmethod.latin on user 0
05-24 16:06:11.397  1304  1304 W PackageSettings: Missing permission state for package org.lineageos.aperture.frameworksbaseoverlay on user 0
05-24 16:06:11.397  1304  1304 W PackageSettings: Missing permission state for package com.nikgapps.overlay.contacts on user 0
05-24 16:06:11.397  1304  1304 W PackageSettings: Missing permission state for package android.auto_generated_rro_vendor__ on user 0
05-24 16:06:11.397  1304  1304 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.android on user 0
05-24 16:06:11.397  1304  1304 W PackageSettings: Missing permission state for package com.google.android.apps.restore on user 0
05-24 16:06:11.397  1304  1304 W PackageSettings: Missing permission state for shared user: android.uid.log
05-24 16:06:11.397  1304  1304 W PackageSettings: Missing permission state for shared user: android.uid.uwb
05-24 16:06:11.538  1304  1304 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-24 16:06:11.546  1304  1304 I PackageManager: /system/apex/com.android.btservices.apex changed; collecting certs
05-24 16:06:11.575  1304  1304 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-24 16:06:11.597  1304  1304 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-24 16:06:11.605  1304  1304 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-24 16:06:11.702  1304  1318 I system_server: Compiler allocated 4688KB to compile com.android.server.pm.ScanResult com.android.server.pm.ScanPackageUtils.scanPackageOnly(com.android.server.pm.ScanRequest, com.android.server.pm.PackageManagerServiceInjector, boolean, long)
05-24 16:06:11.800  1401  1401 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/slot1
05-24 16:06:11.803  1401  1401 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSlot1
05-24 16:06:11.805  1401  1401 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/imsAospSlot1
05-24 16:06:11.807  1401  1401 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/imsSlot1
05-24 16:06:11.810  1401  1401 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/se1
05-24 16:06:11.811  1401  1401 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSe1
05-24 16:06:11.814  1401  1401 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/em1
05-24 16:06:11.816  1401  1401 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkEm1
05-24 16:06:11.817  1401  1401 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkAssist1
05-24 16:06:11.819  1401  1401 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRcs1
05-24 16:06:11.821  1401  1401 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkCap1
05-24 16:06:11.822  1401  1407 E SchedPolicy: open of /dev/cpuctl/bg_non_interactive/tasks failed: No such file or directory
05-24 16:06:11.823  1401  1401 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSmartRatSwitch1
05-24 16:06:11.825  1401  1401 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRsu1
05-24 16:06:11.835  1401  1401 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/slot2
05-24 16:06:11.836  1401  1401 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSlot2
05-24 16:06:11.838  1401  1401 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/imsAospSlot2
05-24 16:06:11.840  1401  1401 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/imsSlot2
05-24 16:06:11.843  1401  1401 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/se2
05-24 16:06:11.844  1401  1401 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSe2
05-24 16:06:11.846  1401  1413 I RmcVsim : [0] RmcVsimUrcHandler init slot: 0, ch id 0
05-24 16:06:11.846  1401  1401 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/em2
05-24 16:06:11.848  1401  1401 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkEm2
05-24 16:06:11.848  1401  1415 I RmcVsim : [1] RmcVsimUrcHandler init slot: 1, ch id 0
05-24 16:06:11.849  1401  1401 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkAssist2
05-24 16:06:11.850  1401  1401 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRcs2
05-24 16:06:11.851  1401  1401 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkCap2
05-24 16:06:11.852  1401  1401 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSmartRatSwitch2
05-24 16:06:11.855  1401  1401 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRsu2
05-24 16:06:11.863  1401  1401 I RILC-OplusRadioAIDL: radio::registerService starting IOplusRadio vendor.oplus.hardware.radio.IRadioStable/OplusRadio0
05-24 16:06:11.868  1401  1434 I RmcDcImsDc2ReqHandler: [0][handleImsBearerNotify] urc=+EIMSPDN: "init"
05-24 16:06:11.873  1401  1463 I RmcDcImsDc2ReqHandler: [1][handleImsBearerNotify] urc=+EIMSPDN: "init"
05-24 16:06:11.876  1401  1401 I RILC-OplusRadioAIDL: radio::registerService IOplusRadio status: 0
05-24 16:06:11.876  1401  1401 I RILC-OplusRadioAIDL: radio::registerService starting IOplusRadio vendor.oplus.hardware.radio.IRadioStable/OplusRadio1
05-24 16:06:11.878  1401  1401 I RILC-OplusRadioAIDL: radio::registerService IOplusRadio status: 0
05-24 16:06:11.878  1401  1401 I RILC-OplusAppRadio: radio::registerService starting IOplusAppRadio oplus_app_slot1
05-24 16:06:11.879  1401  1401 I HidlServiceManagement: Registered vendor.oplus.hardware.appradio@1.0::IOplusAppRadio/oplus_app_slot1
05-24 16:06:11.880  1401  1401 I RILC-OplusAppRadio: radio::registerService IOplusAppRadio status: 0
05-24 16:06:11.880  1401  1401 I RILC-OplusAppRadio: radio::registerService starting IOplusAppRadio oplus_app_slot2
05-24 16:06:11.881  1401  1401 I HidlServiceManagement: Registered vendor.oplus.hardware.appradio@1.0::IOplusAppRadio/oplus_app_slot2
05-24 16:06:11.881  1401  1401 I RILC-OplusAppRadio: radio::registerService IOplusAppRadio status: 0
05-24 16:06:11.882  1401  1401 I HidlServiceManagement: Registered android.hardware.radio.config@1.3::IRadioConfig/default
05-24 16:06:11.886  1401  1406 I WpfaCppUtils: initialRuleContainer!
05-24 16:06:11.888  1401  1401 I HidlServiceManagement: Registered android.hardware.radio@1.2::ISap/slot1
05-24 16:06:11.888  1401  1406 I WpfaCppUtils: initialA2MRingBuffer!
05-24 16:06:11.893  1401  1401 I HidlServiceManagement: Registered android.hardware.radio@1.2::ISap/slot2
05-24 16:06:11.909   999   999 I AVSync  : avInit, st 1f9fa8097, int=8, frac=7d4ed0ef
05-24 16:06:11.909   999   999 I vtservice: [VT][SRV]after VTService_instantiate
05-24 16:06:11.909   999  1483 I AVSync  : avInit, st 1f9fd67ae, int=8, frac=7d5b4c39
05-24 16:06:11.922   873  1296 I VT HIDL : [IVT] [VT THREAD] [VT_Bind] des = volte_imsvt1 initialize communication
05-24 16:06:11.995  1401  1473 E libmnlUtils: No action: deInitReaderLoop can't get mMnlsocket
05-24 16:06:11.995  1401  1473 I wpfa    : initReaderLoop() done, buf_size=67583
05-24 16:06:11.995  1401  1473 I wpfa    : WPFA_DL initialized
05-24 16:06:12.003  1304  1370 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 16:06:12.004  1304  1370 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 16:06:12.005  1304  1370 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 16:06:12.023  1304  1370 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 16:06:12.030  1304  1370 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 16:06:12.030  1304  1370 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 16:06:12.063  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.CAMERA in package: com.mediatek.engineermode at: Binary XML file line #30
05-24 16:06:12.173  1304  1371 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.POST_NOTIFICATIONS in package: com.android.phone at: Binary XML file line #171
05-24 16:06:12.181  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.SATELLITE_COMMUNICATION in package: com.android.shell at: Binary XML file line #775
05-24 16:06:12.183  1005  1031 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 16:06:12.183  1005  1031 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:3
05-24 16:06:12.185  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE in package: com.android.shell at: Binary XML file line #874
05-24 16:06:12.185  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE_SYSTEM_EXEMPTED in package: com.android.shell at: Binary XML file line #875
05-24 16:06:12.319  1040  1040 I KEYFILE : [INFO   ] CustomCommon.cpp:161 init() ro.vendor.config.oplus.low_ram = 0
05-24 16:06:12.319  1040  1040 I KEYFILE : [INFO   ] CustomCommon.cpp:162 init() vendor.debug.camera.bss.aishutter.weighting = 100,98,96,94,92,90,90,90
05-24 16:06:12.319  1040  1040 I KEYFILE : [INFO   ] CustomCommon.cpp:163 init() vendor.debug.tpi.s.semi.run = 0
05-24 16:06:12.320  1040  1040 I KEYFILE : [INFO   ] CustomCommon.cpp:164 init() vendor.debug.camera.FDAsync = true
05-24 16:06:12.320  1040  1040 E KEYFILE : [ERROR   ] CustomMetadata.cpp:375 init() PROP_SYS_CAM_PACKNAME err 0!
05-24 16:06:12.320  1040  1040 I KEYFILE : [INFO   ] CustomerData.cpp:93 init() 0xb400006f4b664130, size: 288 byte
05-24 16:06:12.324  1304  1304 W PackageManager: Failed to scan /product/priv-app/CarrierServices: Package com.google.android.ims at /product/priv-app/CarrierServices ignored: updated version 31144015 better than this 30939330
05-24 16:06:12.447  1401  1406 E RILC-OplusRadioAIDL: oplusRadioServiceAIDL: oplusRadioServiceAIDL[0]->mRadioIndicationOplus == NULL
05-24 16:06:12.654  1401  1406 E RILC-OplusRadioAIDL: oplusRadioServiceAIDL: oplusRadioServiceAIDL[1]->mRadioIndicationOplus == NULL
05-24 16:06:13.013  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.CONFIGURE_WIFI_DISPLAY in package: com.android.systemui at: Binary XML file line #174
05-24 16:06:13.014  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.GET_RUNTIME_PERMISSIONS in package: com.android.systemui at: Binary XML file line #252
05-24 16:06:13.014  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.READ_DEVICE_CONFIG in package: com.android.systemui at: Binary XML file line #372
05-24 16:06:13.014  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.MODIFY_AUDIO_SETTINGS in package: com.android.systemui at: Binary XML file line #405
05-24 16:06:13.014  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FORCE_STOP_PACKAGES in package: com.android.systemui at: Binary XML file line #426
05-24 16:06:13.117  1304  1304 I ApexManager: Registering com.android.cellbroadcastservice as apk-in-apex of com.android.cellbroadcast
05-24 16:06:13.164  1304  1370 W PackageParsing: Unknown element under <manifest>: meta-data at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #102
05-24 16:06:13.164  1304  1370 W PackageParsing: Unknown element under <manifest>: provider at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #106
05-24 16:06:13.164  1304  1370 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #116
05-24 16:06:13.164  1304  1370 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #122
05-24 16:06:13.164  1304  1370 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #129
05-24 16:06:13.164  1304  1370 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #135
05-24 16:06:13.164  1304  1370 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #140
05-24 16:06:13.164  1304  1370 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #147
05-24 16:06:13.164  1304  1370 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #153
05-24 16:06:13.165  1304  1370 W PackageParsing: Unknown element under <manifest>: receiver at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #158
05-24 16:06:13.165  1304  1370 W PackageParsing: Unknown element under <manifest>: service at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #166
05-24 16:06:13.165  1304  1370 W PackageParsing: Unknown element under <manifest>: provider at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #168
05-24 16:06:13.165  1304  1370 W PackageParsing: Unknown element under <manifest>: receiver at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #177
05-24 16:06:13.169  1304  1304 I ApexManager: Registering com.android.nfc as apk-in-apex of com.android.nfcservices
05-24 16:06:13.183  1005  1031 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 16:06:13.183  1005  1031 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:4
05-24 16:06:13.195  1304  1304 I ApexManager: Registering com.android.safetycenter.resources as apk-in-apex of com.android.permission
05-24 16:06:13.243  1304  1304 I ApexManager: Registering com.android.permissioncontroller as apk-in-apex of com.android.permission
05-24 16:06:13.255  1304  1304 I ApexManager: Registering com.android.ondevicepersonalization.services as apk-in-apex of com.android.ondevicepersonalization
05-24 16:06:13.264  1304  1304 I ApexManager: Registering com.android.federatedcompute.services as apk-in-apex of com.android.ondevicepersonalization
05-24 16:06:13.295  1304  1371 W PackageParsing: com.android.healthconnect.controller defines a background permission. Only the 'android' packages can do that.
05-24 16:06:13.295  1304  1371 W PackageParsing: com.android.healthconnect.controller defines a background permission. Only the 'android' packages can do that.
05-24 16:06:13.295  1304  1371 W PackageParsing: com.android.healthconnect.controller defines a background permission. Only the 'android' packages can do that.
05-24 16:06:13.338  1304  1304 I ApexManager: Registering android.ext.services as apk-in-apex of com.android.extservices
05-24 16:06:13.401  1304  1304 I ApexManager: Registering com.android.bluetooth as apk-in-apex of com.android.btservices
05-24 16:06:13.658  1401  1406 E RadioConfig_service: radioConfigService[0] or mRadioConfigIndication is NULL
05-24 16:06:13.676  1493  1493 I HidlServiceManagement: Registered android.hardware.audio@7.1::IDevicesFactory/default
05-24 16:06:13.676  1493  1493 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-24 16:06:13.677  1493  1493 I LegacySupport: Registration complete for android.hardware.audio@7.1::IDevicesFactory/default.
05-24 16:06:13.683  1493  1493 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-24 16:06:13.683  1493  1493 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-24 16:06:13.692  1493  1493 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 16:06:13.732  1304  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.RECEIVE_BOOT_COMPLETED in package: com.android.adservices.api at: Binary XML file line #159
05-24 16:06:13.736  1304  1304 I ApexManager: Registering com.android.adservices.api as apk-in-apex of com.android.adservices
05-24 16:06:13.744  1304  1304 I ApexManager: Registering com.android.sdksandbox as apk-in-apex of com.android.adservices
05-24 16:06:13.775  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.775  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.776  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.776  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.776  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.776  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.776  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.776  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.776  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.776  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.776  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.777  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.777  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.777  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.777  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.777  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.777  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.777  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.823  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.823  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.826  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 16:06:13.832  1500  1500 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 16:06:13.833  1500  1500 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 16:06:13.834   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 16:06:13.844  1493  1493 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x637fba17)
05-24 16:06:13.848  1493  1493 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x20169047)
05-24 16:06:13.850  1493  1493 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 16:06:13.851  1493  1493 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xc56265bf)
05-24 16:06:13.853  1493  1493 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0xb57a1b63)
05-24 16:06:13.856  1493  1493 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-24 16:06:13.857  1500  1500 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 16:06:13.857   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 16:06:13.857   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 16:06:13.858  1493  1493 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-24 16:06:13.858   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 16:06:13.859  1500  1500 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 16:06:13.862  1493  1493 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-24 16:06:13.862   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 16:06:13.863  1493  1493 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-24 16:06:13.863  1493  1493 W audiohalservice: Could not register Bluetooth Audio API
05-24 16:06:13.863   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-24 16:06:13.867  1304  1323 W android.permission.PermissionManager: Missing ActivityManager; assuming 1041 does not hold android.permission.UPDATE_DEVICE_STATS
05-24 16:06:13.867  1493  1493 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-24 16:06:13.867  1493  1493 W audiohalservice: Could not register Bluetooth Audio Offload API
05-24 16:06:13.868  1304  1323 W Binder  : java.lang.SecurityException: Access denied, requires: android.permission.UPDATE_DEVICE_STATS
05-24 16:06:13.868  1304  1323 W Binder  : 	at android.os.PermissionEnforcer.enforcePermission(PermissionEnforcer.java:146)
05-24 16:06:13.868  1304  1323 W Binder  : 	at com.android.internal.app.IBatteryStats$Stub.noteResetAudio_enforcePermission(IBatteryStats.java:3472)
05-24 16:06:13.868  1304  1323 W Binder  : 	at com.android.server.am.BatteryStatsService.noteResetAudio(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:1)
05-24 16:06:13.870  1500  1500 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-24 16:06:13.873  1493  1493 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-24 16:06:13.873  1493  1493 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-24 16:06:13.873  1493  1493 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-24 16:06:13.883  1500  1500 E libxml2 : I/O warning : failed to load "/odm/etc/virtual_audio_policy_configuration.xml": No such file or directory
05-24 16:06:13.884  1500  1500 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-24 16:06:13.884  1500  1500 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-24 16:06:13.894  1493  1493 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-24 16:06:13.894  1493  1493 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-24 16:06:13.897  1493  1493 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 16:06:13.967  1040  1040 I mtkcam-devicemgr: [initialize] -
05-24 16:06:13.967  1040  1040 I mtkcam-camprovider: [initialize] +
05-24 16:06:13.968  1040  1040 I mtkcam-camprovider: [initialize] -
05-24 16:06:13.970  1040  1040 I HidlServiceManagement: Registered android.hardware.camera.provider@2.6::ICameraProvider/internal/0
05-24 16:06:13.971  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.971  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.971  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.971  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.971  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.971  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.971  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.971  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.972  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.972  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.972  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.972  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.972  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.972  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.973  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.973  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.973  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.973  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:13.989  1040  1040 I HidlServiceManagement: Registered vendor.mediatek.hardware.camera.isphal@1.0::IISPModule/internal/0
05-24 16:06:13.990  1006  1155 I CameraService: onDeviceStatusChanged: Status changed for cameraId=4, newStatus=1
05-24 16:06:13.990  1006  1155 I CameraService: onDeviceStatusChanged: Unknown camera ID 4, a new camera is added
05-24 16:06:13.991  1006  1155 I CameraService: onDeviceStatusChanged: Status changed for cameraId=3, newStatus=1
05-24 16:06:13.991  1006  1155 I CameraService: onDeviceStatusChanged: Unknown camera ID 3, a new camera is added
05-24 16:06:13.991  1006  1155 I CameraService: onDeviceStatusChanged: Status changed for cameraId=2, newStatus=1
05-24 16:06:13.991  1006  1155 I CameraService: onDeviceStatusChanged: Unknown camera ID 2, a new camera is added
05-24 16:06:13.991  1006  1155 I CameraService: onDeviceStatusChanged: Status changed for cameraId=1, newStatus=1
05-24 16:06:13.991  1006  1155 I CameraService: onDeviceStatusChanged: Unknown camera ID 1, a new camera is added
05-24 16:06:13.991  1006  1155 I CameraService: onDeviceStatusChanged: Status changed for cameraId=0, newStatus=1
05-24 16:06:13.991  1006  1155 I CameraService: onDeviceStatusChanged: Unknown camera ID 0, a new camera is added
05-24 16:06:13.997  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.READ_EXTERNAL_STORAGE in package: ellipi.messenger at: Binary XML file line #111
05-24 16:06:13.997  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WRITE_EXTERNAL_STORAGE in package: ellipi.messenger at: Binary XML file line #179
05-24 16:06:14.006  1040  1040 I MtkCam/BGService: IBGService  into HIDL_FETCH_IBGService
05-24 16:06:14.008  1040  1040 I HidlServiceManagement: Registered vendor.mediatek.hardware.camera.bgservice@1.1::IBGService/internal/0
05-24 16:06:14.008  1040  1040 I LegacySupport: Registration complete for vendor.mediatek.hardware.camera.bgservice@1.1::IBGService/internal/0.
05-24 16:06:14.016  1304  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.MANAGE_OWN_CALLS in package: com.truecaller at: Binary XML file line #141
05-24 16:06:14.024  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:14.024  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:14.025  1040  1040 I HidlServiceManagement: Registered vendor.mediatek.hardware.camera.atms@1.0::IATMs/default
05-24 16:06:14.027  1493  1493 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 16:06:14.058  1493  1493 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xef466119)
05-24 16:06:14.059  1304  1371 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.INTERNET in package: com.intsig.camscanner at: Binary XML file line #34
05-24 16:06:14.060  1493  1514 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-24 16:06:14.061  1493  1514 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0xef466119)
05-24 16:06:14.062  1493  1514 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0xef466119)
05-24 16:06:14.113  1304  1372 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #9
05-24 16:06:14.113  1304  1372 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #10
05-24 16:06:14.113  1304  1372 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #11
05-24 16:06:14.113  1304  1372 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #12
05-24 16:06:14.145  1493  1493 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-24 16:06:14.150  1493  1493 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 16:06:14.174  1493  1493 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-24 16:06:14.183  1005  1031 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 16:06:14.183  1005  1031 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:5
05-24 16:06:14.188  1493  1493 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 16:06:14.188  1493  1493 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 16:06:14.188  1493  1493 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 16:06:14.193  1493  1493 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 16:06:14.193  1493  1493 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 16:06:14.193  1493  1493 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-24 16:06:14.196  1493  1493 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-24 16:06:14.197  1493  1493 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-24 16:06:14.198  1500  1500 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 16:06:14.198  1500  1500 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 16:06:14.198  1493  1499 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-24 16:06:14.198  1493  1499 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-24 16:06:14.199  1493  1499 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 16:06:14.199  1500  1500 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-24 16:06:14.199  1500  1500 I AudioFlinger: openOutput() this 0xb400007e5507f960, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-24 16:06:14.200  1500  1500 I AudioFlinger: operator(): Using 3000 ms as standby time
05-24 16:06:14.200  1500  1500 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 16:06:14.201  1500  1500 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 16:06:14.201   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 16:06:14.202  1500  1500 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 16:06:14.215  1500  1529 I AudioFlinger: AudioFlinger's thread 0xb400007f2bdab760 tid=1529 ready to run
05-24 16:06:14.216  1500  1529 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:14.217  1500  1500 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:14.218  1500  1529 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:14.218  1500  1500 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:14.218  1500  1500 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:14.218  1500  1500 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:14.218  1500  1500 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:14.218  1500  1500 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:14.218  1500  1500 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:14.218  1500  1500 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:14.218  1500  1500 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:14.218  1500  1500 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:14.218  1500  1500 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:14.218  1500  1500 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:14.218  1500  1500 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:14.218  1500  1500 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:14.218  1500  1500 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:14.218  1500  1500 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:14.219  1500  1500 W AudioFlinger: moveEffects() AUDIO_SESSION_OUTPUT_MIX not found in orphans, checking other mix
05-24 16:06:14.219  1500  1500 W AudioFlinger: moveEffects() bad srcIo 0
05-24 16:06:14.219  1500  1500 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 16:06:14.241  1493  1499 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x2, output_devices == current_output_devices(0x00000002), return
05-24 16:06:14.243  1500  1500 I AudioFlinger: openOutput() this 0xb400007e5507f960, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x8
05-24 16:06:14.243  1493  1499 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 16:06:14.244  1500  1500 I AudioFlinger: HAL output buffer size 2048 frames, normal sink buffer size 2048 frames
05-24 16:06:14.244  1500  1530 I AudioFlinger: AudioFlinger's thread 0xb400007f2bc1c760 tid=1530 ready to run
05-24 16:06:14.280  1493  1516 W audio_messenger_ipi: read_ipi_thread(), read dsp fail!! length_read: 0, errno: 0
05-24 16:06:14.284  1011  1029 W ServiceManagerCppClient: Service package_native didn't start. Returning NULL
05-24 16:06:14.284  1011  1029 E ServiceUtilities: getCachedInfo: Cannot find package_native
05-24 16:06:14.286  1533  1533 E DEBUG   : failed to read process info: failed to open /proc/1493: No such file or directory
05-24 16:06:14.340  1533  1533 F DEBUG   : Cmdline: /vendor/bin/hw/android.hardware.audio.service
05-24 16:06:14.340  1533  1533 F DEBUG   : pid: 1493, tid: 1499, name: HwBinder:1493_2  >>> /vendor/bin/hw/android.hardware.audio.service <<<
05-24 16:06:14.341  1533  1533 F DEBUG   :       #01 pc 0002651d  /vendor/lib/hw/<EMAIL> (android::hardware::audio::V7_1::implementation::StreamOut::setLatencyModeCallback(android::sp<android::hardware::audio::V7_1::IStreamOutLatencyModeCallback> const&)+36) (BuildId: 544a581a8a5464266e14c74f5aea9a1c)
05-24 16:06:14.341  1533  1533 F DEBUG   :       #02 pc 00043c0f  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::_hidl_setLatencyModeCallback(android::hidl::base::V1_0::BnHwBase*, android::hardware::Parcel const&, android::hardware::Parcel*, std::__1::function<void (android::hardware::Parcel&)>)+198) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 16:06:14.341  1533  1533 F DEBUG   :       #03 pc 00044703  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::onTransact(unsigned int, android::hardware::Parcel const&, android::hardware::Parcel*, unsigned int, std::__1::function<void (android::hardware::Parcel&)>)+1926) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 16:06:14.374  1500  1505 E HalDeathHandler: HAL server crashed, audio server is restarting
05-24 16:06:14.374  1500  1530 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:14.390  1013  1013 W ServiceManagerCppClient: Service package_native didn't start. Returning NULL
05-24 16:06:14.390  1013  1013 E storaged: getService package_native failed
05-24 16:06:14.398  1013  1539 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder'...
05-24 16:06:15.184  1005  1031 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 16:06:15.184  1005  1031 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:6
05-24 16:06:15.433  1304  1372 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 16:06:15.434  1304  1372 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 16:06:15.435  1304  1372 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 16:06:15.436  1304  1372 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #34
05-24 16:06:15.437  1304  1372 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 16:06:15.438  1304  1372 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 16:06:15.439  1304  1372 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 16:06:15.482   823   827 W ServiceManagerCppClient: Service statscompanion didn't start. Returning NULL
05-24 16:06:15.482   823   827 E statsd  : Uid 1000 does not have the android.permission.REGISTER_STATS_PULL_ATOM permission when registering atom 10205 (-1)
05-24 16:06:15.545  1304  1373 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.USE_FINGERPRINT in package: org.mozilla.focus at: Binary XML file line #73
05-24 16:06:15.585  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.ACCESS_NETWORK_STATE in package: com.google.android.gm at: Binary XML file line #311
05-24 16:06:15.585  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE in package: com.google.android.gm at: Binary XML file line #314
05-24 16:06:15.585  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE_DATA_SYNC in package: com.google.android.gm at: Binary XML file line #315
05-24 16:06:15.585  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.INTERNET in package: com.google.android.gm at: Binary XML file line #316
05-24 16:06:15.585  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.RECEIVE_BOOT_COMPLETED in package: com.google.android.gm at: Binary XML file line #317
05-24 16:06:15.585  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.VIBRATE in package: com.google.android.gm at: Binary XML file line #318
05-24 16:06:15.586  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WAKE_LOCK in package: com.google.android.gm at: Binary XML file line #319
05-24 16:06:15.586  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WRITE_EXTERNAL_STORAGE in package: com.google.android.gm at: Binary XML file line #321
05-24 16:06:15.586  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.POST_NOTIFICATIONS in package: com.google.android.gm at: Binary XML file line #322
05-24 16:06:15.586  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.c2dm.permission.RECEIVE in package: com.google.android.gm at: Binary XML file line #324
05-24 16:06:15.586  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.GET_PACKAGE_SIZE in package: com.google.android.gm at: Binary XML file line #325
05-24 16:06:15.586  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.providers.gsf.permission.READ_GSERVICES in package: com.google.android.gm at: Binary XML file line #326
05-24 16:06:15.586  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.providers.gsf.permission.READ_GSERVICES in package: com.google.android.gm at: Binary XML file line #331
05-24 16:06:15.586  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.GET_PACKAGE_SIZE in package: com.google.android.gm at: Binary XML file line #332
05-24 16:06:15.586  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.ACCESS_NETWORK_STATE in package: com.google.android.gm at: Binary XML file line #334
05-24 16:06:15.586  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.CAMERA in package: com.google.android.gm at: Binary XML file line #345
05-24 16:06:15.586  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE in package: com.google.android.gm at: Binary XML file line #360
05-24 16:06:15.586  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.INTERNET in package: com.google.android.gm at: Binary XML file line #361
05-24 16:06:15.586  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.RECORD_AUDIO in package: com.google.android.gm at: Binary XML file line #363
05-24 16:06:15.586  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WAKE_LOCK in package: com.google.android.gm at: Binary XML file line #364
05-24 16:06:15.587  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.POST_NOTIFICATIONS in package: com.google.android.gm at: Binary XML file line #373
05-24 16:06:15.587  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE_DATA_SYNC in package: com.google.android.gm at: Binary XML file line #379
05-24 16:06:15.587  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.gm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION in package: com.google.android.gm at: Binary XML file line #385
05-24 16:06:15.587  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.VIBRATE in package: com.google.android.gm at: Binary XML file line #386
05-24 16:06:15.587  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.READ_CONTACTS in package: com.google.android.gm at: Binary XML file line #387
05-24 16:06:15.587  1304  1372 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.hangouts.START_HANGOUT in package: com.google.android.gm at: Binary XML file line #388
05-24 16:06:16.184  1005  1031 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 16:06:16.184  1005  1031 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:7
05-24 16:06:16.302  1047  1368 W mtk_agpsd: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.system.suspend.ISystemSuspend/default
05-24 16:06:17.165  1304  1304 W AppIdPermissionPolicy: Ignoring permission com.google.android.gtalkservice.permission.GTALK_SERVICE declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-24 16:06:17.165  1304  1304 W AppIdPermissionPolicy: Ignoring permission com.android.vending.INTENT_VENDING_ONLY declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-24 16:06:17.165  1304  1304 W AppIdPermissionPolicy: Ignoring permission com.google.android.providers.settings.permission.WRITE_GSETTINGS declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-24 16:06:17.165  1304  1304 W AppIdPermissionPolicy: Ignoring permission com.google.android.providers.gsf.permission.WRITE_GSERVICES declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-24 16:06:17.165  1304  1304 W AppIdPermissionPolicy: Ignoring permission lineageos.permission.MANAGE_REMOTE_PREFERENCES declared in system package com.android.settings: already declared in another system package io.chaldeaprjkt.gamespace
05-24 16:06:17.168  1304  1304 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_TOPICS declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 16:06:17.168  1304  1304 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_ATTRIBUTION declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 16:06:17.168  1304  1304 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 16:06:17.168  1304  1304 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_AD_SELECTION declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 16:06:17.168  1304  1304 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_PROTECTED_SIGNALS declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 16:06:17.168  1304  1304 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_AD_ID declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 16:06:17.184  1005  1031 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 16:06:17.184  1005  1031 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:8
05-24 16:06:17.280  1304  1304 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.adservices/appid
05-24 16:06:17.282  1304  1304 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.permission/appid
05-24 16:06:17.285  1304  1304 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.btservices/appid
05-24 16:06:17.294  1304  1304 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.extservices/appid
05-24 16:06:17.298  1304  1304 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.nfcservices/appid
05-24 16:06:17.412  1304  1304 I SystemServiceManager: Starting com.android.server.pm.UserManagerService$LifeCycle
05-24 16:06:17.416  1013  1539 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder' successful after waiting 3017ms
05-24 16:06:17.471  1304  1580 W PackageManager: Skipping preparing app data for com.android.adservices
05-24 16:06:17.471  1304  1580 W PackageManager: Skipping preparing app data for com.android.permission
05-24 16:06:17.472  1304  1580 W PackageManager: Skipping preparing app data for com.android.btservices
05-24 16:06:17.473  1304  1580 W PackageManager: Skipping preparing app data for com.android.extservices
05-24 16:06:17.474  1304  1580 W PackageManager: Skipping preparing app data for com.android.nfcservices
05-24 16:06:17.631  1304  1304 I SystemServiceManager: Starting com.android.server.sensors.SensorService
05-24 16:06:17.632  1304  1304 I SystemServiceManager: Starting com.android.server.SystemConfigService
05-24 16:06:17.635  1304  1304 I SystemServiceManager: Starting com.android.server.BatteryService
05-24 16:06:17.638   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.sensors@2.1::ISensors/default in either framework or device VINTF manifest.
05-24 16:06:17.653  1304  1588 W SensorService: lsm6dso ACCELEROMETER's max range 78.453201293945 is not a multiple of the resolution 0.001200000057 - updated to 78.453605651855
05-24 16:06:17.653  1304  1588 I SensorService: lsm6dso ACCELEROMETER's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.653  1304  1588 W SensorService: mmc5603 MAGNETOMETER's max range 4912.000000000000 is not a multiple of the resolution 0.150000005960 - updated to 4912.050292968750
05-24 16:06:17.653  1304  1588 I SensorService: mmc5603 MAGNETOMETER's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.653  1304  1588 W SensorService: lsm6dso GYROSCOPE's max range 34.906600952148 is not a multiple of the resolution 0.001099999994 - updated to 34.906299591064
05-24 16:06:17.653  1304  1588 I SensorService: lsm6dso GYROSCOPE's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.653  1304  1588 I SensorService: tcs3701 PROXIMITY's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.653  1304  1588 W SensorService: UNCALI_MAG's max range 4912.000000000000 is not a multiple of the resolution 0.150000005960 - updated to 4912.050292968750
05-24 16:06:17.653  1304  1588 I SensorService: UNCALI_MAG's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.653  1304  1588 W SensorService: UNCALI_GYRO's max range 34.906600952148 is not a multiple of the resolution 0.001099999994 - updated to 34.906299591064
05-24 16:06:17.653  1304  1588 I SensorService: UNCALI_GYRO's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.653  1304  1588 I SensorService: SIGNIFICANT_MOTION's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.653  1304  1588 I SensorService: STEP_DETECTOR's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.653  1304  1588 I SensorService: STEP_COUNTER's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.653  1304  1588 I SensorService: DEVICE_ORIENTATION's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.653  1304  1588 I SensorService: STATIONARY_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.653  1304  1588 I SensorService: MOTION_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.653  1304  1588 W SensorService: UNCALI_ACC's max range 39.226600646973 is not a multiple of the resolution 0.001200000057 - updated to 39.226802825928
05-24 16:06:17.653  1304  1588 I SensorService: UNCALI_ACC's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.653  1304  1588 I SensorService: tcs3701 LIGHT's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.653  1304  1588 I SensorService: RAW_MAG's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.653  1304  1588 I SensorService: mn29005 rear_als's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.653  1304  1588 I SensorService: ai_shutter's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.653  1304  1588 I SensorService: STEP_DETECTOR_WAKEUP's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.653  1304  1588 I SensorService: PICKUP_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.654  1304  1588 I SensorService: FP_DISPLAY's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.654  1304  1588 I SensorService: LUX_AOD's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.654  1304  1588 I SensorService: PEDO_MINUTE's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.654  1304  1588 I SensorService: OPLUS_ACTIVITY_RECOGNITION's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.654  1304  1588 I SensorService: ELEVATOR_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-24 16:06:17.656   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.light@2.0::ILight/default in either framework or device VINTF manifest.
05-24 16:06:17.667  1304  1304 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 16:06:17.668  1304  1304 I HealthServiceWrapperHidl: health: HealthServiceWrapper listening to instance default
05-24 16:06:17.668  1304  1304 I BatteryService: health: Waited 0ms and received the update.
05-24 16:06:17.670  1304  1304 I SystemServiceManager: Starting com.android.server.usage.UsageStatsService
05-24 16:06:17.675  1304  1304 I SystemServiceManager: Starting com.android.server.webkit.WebViewUpdateService
05-24 16:06:17.677  1304  1304 I SystemServiceManager: Starting com.android.server.CachedDeviceStateService
05-24 16:06:17.677  1304  1304 I SystemServiceManager: Starting com.android.server.BinderCallsStatsService$LifeCycle
05-24 16:06:17.678  1304  1304 I SystemServiceManager: Starting com.android.server.LooperStatsService$Lifecycle
05-24 16:06:17.678  1304  1304 I SystemServiceManager: Starting com.android.server.rollback.RollbackManagerService
05-24 16:06:17.684  1304  1304 I SystemServiceManager: Starting com.android.server.os.NativeTombstoneManagerService
05-24 16:06:17.685  1304  1304 I SystemServiceManager: Starting com.android.server.os.BugreportManagerService
05-24 16:06:17.685  1304  1304 I SystemServiceManager: Starting com.android.server.gpu.GpuService
05-24 16:06:17.686  1304  1304 I SystemServiceManager: Starting com.android.server.security.rkp.RemoteProvisioningService
05-24 16:06:17.688  1304  1304 I SystemServiceManager: Starting com.android.server.security.KeyChainSystemService
05-24 16:06:17.688  1304  1304 I SystemServiceManager: Starting com.android.server.BinaryTransparencyService
05-24 16:06:17.689  1304  1304 I TransparencyService: Started BinaryTransparencyService
05-24 16:06:17.689  1304  1304 I SystemServiceManager: Starting com.android.server.telecom.TelecomLoaderService
05-24 16:06:17.692  1304  1304 I SystemServiceManager: Starting com.android.server.accounts.AccountManagerService$Lifecycle
05-24 16:06:17.696  1304  1304 I SystemServiceManager: Starting com.android.server.content.ContentService$Lifecycle
05-24 16:06:17.715  1304  1601 I SchedulingPolicyService: Moving 1052 back to group default
05-24 16:06:17.782  1304  1304 I Freezer : Cannot open freezer path "/sys/fs/cgroup/uid_1000/pid_1304/frozen/freezer.state": No such file or directory
05-24 16:06:17.782  1304  1304 I SystemServiceManager: Starting com.android.server.deviceconfig.DeviceConfigInit$Lifecycle
05-24 16:06:17.784  1304  1304 I SystemServiceManager: Starting com.android.server.DropBoxManagerService
05-24 16:06:17.785  1304  1304 I SystemServiceManager: Starting com.android.ecm.EnhancedConfirmationService
05-24 16:06:17.787  1304  1304 I SystemServiceManager: Starting com.android.server.power.hint.HintManagerService
05-24 16:06:17.790  1304  1304 I SystemServiceManager: Starting com.android.role.RoleService
05-24 16:06:17.793  1304  1304 I SystemServiceManager: Starting com.android.server.vibrator.VibratorManagerService$Lifecycle
05-24 16:06:17.802  1304  1304 I SystemServiceManager: Starting com.android.server.alarm.AlarmManagerService
05-24 16:06:17.823  1304  1304 I InputManager: Initializing input manager, mUseDevInputEventForAudioJack=true
05-24 16:06:17.824  1304  1304 I SystemServiceManager: Starting com.android.server.devicestate.DeviceStateManagerService
05-24 16:06:17.827  1304  1304 E DeviceStateManagerService: Cannot notify device state info change before the initial state has been committed.
05-24 16:06:17.827   825   825 E ActivityRecognitionHardware: activity_recognition HAL is deprecated. class_init is effectively a no-op
05-24 16:06:17.827  1304  1304 I DeviceStateManagerService: Cannot notify device state info change when pending state is present.
05-24 16:06:17.829  1304  1304 I SystemServiceManager: Starting com.android.server.camera.CameraServiceProxy
05-24 16:06:17.830  1304  1304 I SystemServiceManager: Starting phase 200
05-24 16:06:17.931   825   825 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlags
05-24 16:06:17.931   825   825 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlagsImpl
05-24 16:06:17.931   825   825 W Zygote  : Class not found for preloading: android.media.audio.Flags
05-24 16:06:17.937  1304  1614 I HidlServiceManagement: Registered android.frameworks.schedulerservice@1.0::ISchedulingPolicyService/default
05-24 16:06:17.937  1304  1304 I SystemServiceManager: Starting com.android.server.bluetooth.BluetoothService
05-24 16:06:17.937  1304  1613 I HidlServiceManagement: Registered android.frameworks.sensorservice@1.0::ISensorManager/default
05-24 16:06:17.947  1304  1304 I SystemServiceManager: Starting com.android.server.connectivity.IpConnectivityMetrics
05-24 16:06:17.947  1304  1304 I SystemServiceManager: Starting com.android.server.net.watchlist.NetworkWatchlistService$Lifecycle
05-24 16:06:17.951  1304  1304 I SystemServiceManager: Starting com.android.server.pinner.PinnerService
05-24 16:06:17.953  1304  1304 I SystemServiceManager: Starting com.android.server.integrity.AppIntegrityManagerService
05-24 16:06:17.960  1304  1304 I SystemServiceManager: Starting com.android.server.logcat.LogcatManagerService
05-24 16:06:17.966  1304  1304 I SystemServiceManager: Starting com.android.server.inputmethod.InputMethodManagerService$Lifecycle
05-24 16:06:17.976  1304  1304 I SystemServiceManager: Starting com.android.server.accessibility.AccessibilityManagerService$Lifecycle
05-24 16:06:17.994  1304  1304 I SystemServiceManager: Starting com.android.server.StorageManagerService$Lifecycle
05-24 16:06:18.001  1304  1344 W PinnerService: Could not find pinlist.meta for "/system_ext/priv-app/SystemUI/SystemUI.apk": pinning as blob
05-24 16:06:18.002  1304  1304 I SystemServiceManager: Starting com.android.server.usage.StorageStatsService$Lifecycle
05-24 16:06:18.007  1304  1304 I SystemServiceManager: Starting com.android.server.UiModeManagerService
05-24 16:06:18.009  1304  1304 I SystemServiceManager: Starting com.android.server.locales.LocaleManagerService
05-24 16:06:18.012  1304  1304 I SystemServiceManager: Starting com.android.server.grammaticalinflection.GrammaticalInflectionService
05-24 16:06:18.013  1304  1304 I SystemServiceManager: Starting com.android.server.apphibernation.AppHibernationService
05-24 16:06:18.016  1304  1304 I SystemServiceManager: Starting com.android.server.locksettings.LockSettingsService$Lifecycle
05-24 16:06:18.029  1304  1304 I SystemServiceManager: Starting com.android.server.pdb.PersistentDataBlockService
05-24 16:06:18.030  1304  1304 I SystemServiceManager: Starting com.android.server.testharness.TestHarnessModeService
05-24 16:06:18.030  1304  1304 I SystemServiceManager: Starting com.android.server.oemlock.OemLockService
05-24 16:06:18.032   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.oemlock@1.0::IOemLock/default in either framework or device VINTF manifest.
05-24 16:06:18.036  1304  1338 I PersistentDataBlockService: Writing default FRP secret
05-24 16:06:18.036  1304  1338 I PersistentDataBlockService: Writing FRP secret magic
05-24 16:06:18.036  1304  1304 I SystemServiceManager: Starting com.android.server.DeviceIdleController
05-24 16:06:18.042  1304  1304 I SystemServiceManager: Starting com.android.server.devicepolicy.DevicePolicyManagerService$Lifecycle
05-24 16:06:18.054  1304  1304 I SystemServiceManager: Starting com.android.server.systemcaptions.SystemCaptionsManagerService
05-24 16:06:18.055  1304  1304 I SystemServiceManager: Starting com.android.server.texttospeech.TextToSpeechManagerService
05-24 16:06:18.055  1304  1304 I SystemServiceManager: Starting com.android.server.wearable.WearableSensingManagerService
05-24 16:06:18.057  1304  1304 I SystemServiceManager: Starting com.android.server.ondeviceintelligence.OnDeviceIntelligenceManagerService
05-24 16:06:18.059  1304  1304 I SystemServiceManager: Starting com.android.server.speech.SpeechRecognitionManagerService
05-24 16:06:18.060  1304  1304 I SystemServiceManager: Starting com.android.server.appprediction.AppPredictionManagerService
05-24 16:06:18.061  1304  1304 I SystemServiceManager: Starting com.android.server.contentsuggestions.ContentSuggestionsManagerService
05-24 16:06:18.062  1304  1304 I SystemServiceManager: Starting com.android.server.contextualsearch.ContextualSearchManagerService
05-24 16:06:18.067  1304  1304 I FontManagerService: Using optimized boot-time font loading.
05-24 16:06:18.068  1304  1304 I SystemServiceManager: Starting com.android.server.textservices.TextServicesManagerService$Lifecycle
05-24 16:06:18.069  1304  1304 I SystemServiceManager: Starting com.android.server.textclassifier.TextClassificationManagerService$Lifecycle
05-24 16:06:18.072  1304  1304 I SystemServiceManager: Starting com.android.server.NetworkScoreService$Lifecycle
05-24 16:06:18.072  1304  1304 I NetworkScoreService: Registering network_score
05-24 16:06:18.073  1304  1304 I SystemServiceManager: Starting com.android.server.NetworkStatsServiceInitializer
05-24 16:06:18.103  1304  1304 I NetworkStatsServiceInitializer: Registering netstats
05-24 16:06:18.110  1304  1304 I SystemServiceManager: Starting com.android.server.wifi.WifiService
05-24 16:06:18.114  1304  1304 I WifiContext: Got resolveInfos for com.android.server.wifi.intent.action.SERVICE_WIFI_RESOURCES_APK: [ResolveInfo{b544fc0 com.android.wifi.resources/android.app.Activity m=0x108000 userHandle=UserHandle{0}}]
05-24 16:06:18.121  1304  1304 E WifiScoringParams: 	at com.android.server.wifi.ScoringParams.<init>(ScoringParams.java:262)
05-24 16:06:18.121  1304  1304 E WifiScoringParams: 	at com.android.server.wifi.WifiInjector.<init>(WifiInjector.java:319)
05-24 16:06:18.121  1304  1304 E WifiScoringParams: 	at com.android.server.wifi.WifiService.<init>(WifiService.java:44)
05-24 16:06:18.121  1304  1304 E WifiScoringParams: 	at com.android.server.SystemServiceManager.startService(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:9)
05-24 16:06:18.121  1304  1304 E WifiScoringParams: 	at com.android.server.SystemServiceManager.startServiceFromJar(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:88)
05-24 16:06:18.121  1304  1304 E WifiScoringParams: 	at com.android.server.SystemServer.startOtherServices(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:322)
05-24 16:06:18.121  1304  1304 E WifiScoringParams: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:597)
05-24 16:06:18.121  1304  1304 E WifiScoringParams: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:889)
05-24 16:06:18.127  1304  1304 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.2::IServiceManager/default
05-24 16:06:18.133  1304  1304 I SupplicantStaIfaceHal: Initializing SupplicantStaIfaceHal using AIDL implementation.
05-24 16:06:18.135  1304  1304 I SupplicantP2pIfaceHal: Initializing SupplicantP2pIfaceHal using AIDL implementation.
05-24 16:06:18.138  1304  1344 W PinnerService: Could not find pinlist.meta for "/system_ext/priv-app/Launcher3QuickStep/Launcher3QuickStep.apk": pinning as blob
05-24 16:06:18.161  1304  1304 I WifiService: Registering wifi
05-24 16:06:18.162  1304  1304 I SystemServiceManager: Starting com.android.server.wifi.scanner.WifiScanningService
05-24 16:06:18.162  1304  1304 I WifiScanningService: Creating wifiscanner
05-24 16:06:18.166  1304  1304 I WifiScanningService: Publishing wifiscanner
05-24 16:06:18.167  1304  1304 I SystemServiceManager: Starting com.android.server.wifi.p2p.WifiP2pService
05-24 16:06:18.168  1304  1304 I WifiContext: Got resolveInfos for com.android.server.wifi.intent.action.SERVICE_WIFI_RESOURCES_APK: [ResolveInfo{d398d0 com.android.wifi.resources/android.app.Activity m=0x108000 userHandle=UserHandle{0}}]
05-24 16:06:18.171  1304  1304 I WifiP2pService: Registering wifip2p
05-24 16:06:18.174  1304  1304 I SystemServiceManager: Starting com.android.server.ConnectivityServiceInitializer
05-24 16:06:18.184  1005  1031 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 16:06:18.184  1005  1031 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:9
05-24 16:06:18.204  1304  1304 I MulticastRoutingCoordinatorService: socket created for multicast routing: java.io.FileDescriptor@28756f1
05-24 16:06:18.208  1304  1647 W BroadcastLoopers: Found previously unknown looper Thread[NsdService,5,main]
05-24 16:06:18.217  1304  1304 I ConnectivityServiceInitializer: Registering ethernet
05-24 16:06:18.218  1304  1304 I ConnectivityServiceInitializer: Registering connectivity
05-24 16:06:18.218  1304  1304 I ConnectivityServiceInitializer: Registering ipsec
05-24 16:06:18.218  1304  1304 I ConnectivityServiceInitializer: Registering connectivity_native
05-24 16:06:18.219  1304  1304 I ConnectivityServiceInitializer: Registering servicediscovery
05-24 16:06:18.219  1304  1304 I ConnectivityServiceInitializer: Registering nearby
05-24 16:06:18.222  1304  1304 I SystemServiceManager: Starting com.android.server.ConnectivityServiceInitializerB
05-24 16:06:18.225  1304  1304 I ConnectivityServiceInitializerB: Registering vcn_management
05-24 16:06:18.226  1304  1304 I SystemUpdateManagerService: No existing info file /data/system/system-update-info.xml
05-24 16:06:18.228  1304  1304 I SystemServiceManager: Starting com.android.server.notification.NotificationManagerService
05-24 16:06:18.292  1304  1304 I NotificationManagerService.NotificationListeners: Read notification listener permissions from xml
05-24 16:06:18.294  1304  1304 W ActivityManager: Unable to start service Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener } U=0: not found
05-24 16:06:18.294  1304  1304 W NotificationManagerService.NotificationListeners: Unable to bind notification listener service: Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener (has extras) } in user 0
05-24 16:06:18.294  1304  1304 I NotificationManagerService.NotificationAssistants: Read notification assistant permissions from xml
05-24 16:06:18.298  1304  1304 I ConditionProviders: Read condition provider permissions from xml
05-24 16:06:18.298  1304  1304 I ConditionProviders: Read condition provider permissions from xml
05-24 16:06:18.299  1304  1304 I ConditionProviders:  Allowing condition provider android.ext.services/android.ext.services.notification.Assistant (userSet: true)
05-24 16:06:18.307  1304  1304 W SystemServiceManager: Service com.android.server.notification.NotificationManagerService took 78 ms in onStart
05-24 16:06:18.310  1304  1304 I SystemServiceManager: Starting com.android.server.storage.DeviceStorageMonitorService
05-24 16:06:18.311  1304  1304 I SystemServiceManager: Starting com.android.server.timedetector.TimeDetectorService$Lifecycle
05-24 16:06:18.315  1304  1304 I SystemServiceManager: Starting com.android.server.location.LocationManagerService$Lifecycle
05-24 16:06:18.322  1304  1304 I SystemServiceManager: Starting com.android.server.timezonedetector.TimeZoneDetectorService$Lifecycle
05-24 16:06:18.328  1304  1304 I SystemServiceManager: Starting com.android.server.location.altitude.AltitudeService$Lifecycle
05-24 16:06:18.329  1304  1304 I SystemServiceManager: Starting com.android.server.timezonedetector.location.LocationTimeZoneManagerService$Lifecycle
05-24 16:06:18.330  1304  1304 I SystemServiceManager: Starting com.android.server.search.SearchManagerService$Lifecycle
05-24 16:06:18.331  1304  1304 I SystemServiceManager: Starting com.android.server.wallpaper.WallpaperManagerService$Lifecycle
05-24 16:06:18.334  1304  1304 I SystemServiceManager: Starting com.android.server.audio.AudioService$Lifecycle
05-24 16:06:18.646  1655  1655 I HidlServiceManagement: Registered android.hardware.audio@7.1::IDevicesFactory/default
05-24 16:06:18.647  1655  1655 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-24 16:06:18.647  1655  1655 I LegacySupport: Registration complete for android.hardware.audio@7.1::IDevicesFactory/default.
05-24 16:06:18.658  1655  1655 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-24 16:06:18.659  1655  1655 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-24 16:06:18.667  1655  1655 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 16:06:18.733  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.733  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.733  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.733  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.733  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.734  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.734  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.734  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.734  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.734  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.734  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.734  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.735  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.735  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.735  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.736  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.736  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.736  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.786  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.786  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.789  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 16:06:18.811  1655  1655 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xe9ac5997)
05-24 16:06:18.817  1655  1655 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x474eae37)
05-24 16:06:18.819  1655  1655 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 16:06:18.821  1655  1655 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x492be5c1)
05-24 16:06:18.822  1655  1655 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x3c254bdd)
05-24 16:06:18.826  1655  1655 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-24 16:06:18.826   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 16:06:18.827  1655  1655 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-24 16:06:18.827   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 16:06:18.828  1655  1655 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-24 16:06:18.829   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 16:06:18.829  1655  1655 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-24 16:06:18.829  1655  1655 W audiohalservice: Could not register Bluetooth Audio API
05-24 16:06:18.830   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-24 16:06:18.830  1655  1655 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-24 16:06:18.830  1655  1655 W audiohalservice: Could not register Bluetooth Audio Offload API
05-24 16:06:18.834  1664  1664 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 16:06:18.836  1664  1664 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 16:06:18.837   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 16:06:18.837  1655  1655 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-24 16:06:18.837  1655  1655 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-24 16:06:18.837  1655  1655 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-24 16:06:18.844  1664  1664 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 16:06:18.844   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 16:06:18.845  1664  1664 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 16:06:18.851  1664  1664 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-24 16:06:18.859  1664  1664 E libxml2 : I/O warning : failed to load "/odm/etc/virtual_audio_policy_configuration.xml": No such file or directory
05-24 16:06:18.859  1664  1664 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-24 16:06:18.859  1664  1664 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-24 16:06:18.869  1655  1655 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-24 16:06:18.870  1655  1655 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-24 16:06:18.876  1655  1655 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 16:06:18.938  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.939  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.939  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.939  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.939  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.939  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.939  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.939  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.939  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.940  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.940  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.940  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.940  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.940  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.940  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.940  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.940  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.940  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.994  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.994  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 16:06:18.997  1655  1655 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 16:06:19.021  1655  1655 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xeeb432a9)
05-24 16:06:19.021  1655  1674 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-24 16:06:19.022  1655  1674 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0xeeb432a9)
05-24 16:06:19.023  1655  1674 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0xeeb432a9)
05-24 16:06:19.056  1655  1655 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-24 16:06:19.061  1655  1655 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 16:06:19.080  1655  1655 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-24 16:06:19.096  1655  1655 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 16:06:19.097  1655  1655 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 16:06:19.097  1655  1655 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 16:06:19.104  1655  1655 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 16:06:19.104  1655  1655 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 16:06:19.105  1655  1655 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-24 16:06:19.109  1655  1655 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-24 16:06:19.109  1655  1655 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-24 16:06:19.110  1664  1664 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 16:06:19.110  1664  1664 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 16:06:19.110  1655  1661 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-24 16:06:19.110  1655  1661 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-24 16:06:19.111  1655  1661 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 16:06:19.111  1664  1664 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-24 16:06:19.111  1664  1664 I AudioFlinger: openOutput() this 0xb4000075bcaf98a0, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-24 16:06:19.112  1664  1664 I AudioFlinger: operator(): Using 3000 ms as standby time
05-24 16:06:19.112  1664  1664 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 16:06:19.113  1664  1664 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 16:06:19.113   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 16:06:19.114  1664  1664 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 16:06:19.126  1664  1689 I AudioFlinger: AudioFlinger's thread 0xb4000078173c7760 tid=1689 ready to run
05-24 16:06:19.126  1664  1689 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:19.127  1664  1664 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:19.128  1664  1689 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.128  1664  1664 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:19.128  1664  1664 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:19.128  1664  1664 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:19.128  1664  1664 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:19.128  1664  1664 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:19.128  1664  1664 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:19.128  1664  1664 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:19.128  1664  1664 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:19.128  1664  1664 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:19.129  1664  1664 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:19.129  1664  1664 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:19.129  1664  1664 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:19.129  1664  1664 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 16:06:19.129  1664  1664 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 16:06:19.129  1664  1664 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 16:06:19.129  1664  1664 W AudioFlinger: moveEffects() AUDIO_SESSION_OUTPUT_MIX not found in orphans, checking other mix
05-24 16:06:19.129  1664  1664 W AudioFlinger: moveEffects() bad srcIo 0
05-24 16:06:19.129  1664  1664 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 16:06:19.151  1655  1688 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x2, output_devices == current_output_devices(0x00000002), return
05-24 16:06:19.153  1664  1664 I AudioFlinger: openOutput() this 0xb4000075bcaf98a0, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x8
05-24 16:06:19.153  1655  1688 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 16:06:19.154  1664  1664 I AudioFlinger: HAL output buffer size 2048 frames, normal sink buffer size 2048 frames
05-24 16:06:19.155  1664  1691 I AudioFlinger: AudioFlinger's thread 0xb400007817245760 tid=1691 ready to run
05-24 16:06:19.155  1664  1691 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.156  1664  1691 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.157  1664  1664 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 16:06:19.222  1655  1688 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x8, output_devices == current_output_devices(0x00000002), return
05-24 16:06:19.225  1664  1664 I AudioFlinger: openOutput() this 0xb4000075bcaf98a0, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x4
05-24 16:06:19.225  1655  1688 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 16:06:19.226  1664  1664 I AudioFlinger: HAL output buffer size 256 frames, normal sink buffer size 768 frames
05-24 16:06:19.227  1664  1693 I AudioFlinger: AudioFlinger's thread 0xb400007817188760 tid=1693 ready to run
05-24 16:06:19.228  1664  1693 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.228  1664  1693 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.230  1664  1664 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 16:06:19.252  1655  1688 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x4, output_devices == current_output_devices(0x00000002), return
05-24 16:06:19.254  1664  1664 I AudioFlinger: openOutput() this 0xb4000075bcaf98a0, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x8000
05-24 16:06:19.254  1655  1688 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 16:06:19.255  1664  1664 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 16:06:19.256  1664  1694 I AudioFlinger: AudioFlinger's thread 0xb400007814a75760 tid=1694 ready to run
05-24 16:06:19.256  1664  1694 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.257  1664  1694 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.259  1664  1664 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 16:06:19.290  1655  1688 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x8000, output_devices == current_output_devices(0x00000002), return
05-24 16:06:19.292  1664  1664 I AudioFlinger: openOutput() this 0xb4000075bcaf98a0, module 10 Device AUDIO_DEVICE_OUT_TELEPHONY_TX, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x10000
05-24 16:06:19.293  1655  1688 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 16:06:19.294  1664  1664 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 16:06:19.295  1664  1695 I AudioFlinger: AudioFlinger's thread 0xb400007814a14760 tid=1695 ready to run
05-24 16:06:19.295  1664  1695 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.297  1664  1695 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.325  1664  1696 I AudioFlinger: AudioFlinger's thread 0xb400007813522a78 tid=1696 ready to run
05-24 16:06:19.326  1664  1696 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.326  1664  1696 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.328  1664  1698 I AudioFlinger: AudioFlinger's thread 0xb400007813522a78 tid=1698 ready to run
05-24 16:06:19.329  1664  1698 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.330  1664  1698 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.331  1664  1700 I AudioFlinger: AudioFlinger's thread 0xb400007813522a78 tid=1700 ready to run
05-24 16:06:19.332  1664  1700 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.333  1664  1700 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.335  1664  1702 I AudioFlinger: AudioFlinger's thread 0xb400007813522a78 tid=1702 ready to run
05-24 16:06:19.336  1664  1702 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.336  1664  1702 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.339  1664  1705 I AudioFlinger: AudioFlinger's thread 0xb400007813522a78 tid=1705 ready to run
05-24 16:06:19.340  1664  1705 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.340  1664  1705 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.342  1655  1687 E DevicesFactoryHAL: loadAudioInterface couldn't load audio hw module audio.binaural_record (No such file or directory)
05-24 16:06:19.342  1664  1664 E AudioFlinger: loadHwModule() error -22 loading module binaural_record
05-24 16:06:19.342  1664  1664 W APM_AudioPolicyManager: could not load HW module binaural_record
05-24 16:06:19.348  1664  1664 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 16:06:19.348  1664  1664 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 16:06:19.349  1664  1664 I AudioFlinger: loadHwModule() Loaded bluetooth audio interface, handle 18
05-24 16:06:19.349  1655  1687 E DevicesFactoryHAL: loadAudioInterface couldn't load audio hw module audio.a2dp (No such file or directory)
05-24 16:06:19.349  1664  1664 E AudioFlinger: loadHwModule() error -22 loading module a2dp
05-24 16:06:19.349  1664  1664 W APM_AudioPolicyManager: could not load HW module a2dp
05-24 16:06:19.350  1655  1687 E DevicesFactoryHAL: loadAudioInterface couldn't load audio hw module audio.usb (No such file or directory)
05-24 16:06:19.350  1664  1664 E AudioFlinger: loadHwModule() error -22 loading module usb
05-24 16:06:19.350  1664  1664 W APM_AudioPolicyManager: could not load HW module usb
05-24 16:06:19.352  1655  1687 I r_submix: adev_open(name=audio_hw_if)
05-24 16:06:19.353  1664  1664 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 16:06:19.353  1664  1664 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 16:06:19.353  1655  1687 I r_submix: adev_init_check()
05-24 16:06:19.353  1664  1664 I AudioFlinger: loadHwModule() Loaded r_submix audio interface, handle 26
05-24 16:06:19.355  1664  1707 I AudioFlinger: AudioFlinger's thread 0xb400007813522a78 tid=1707 ready to run
05-24 16:06:19.356  1664  1707 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.356  1664  1707 W AudioFlinger: no wake lock to update, system not ready yet
05-24 16:06:19.359  1664  1664 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 16:06:19.360   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 16:06:19.361  1664  1664 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 16:06:19.369  1011  1537 W AudioAnalytics: onAudioServerStart: (key=audio.policy) AudioPolicy ctor, loadTimeMs:517.956909
05-24 16:06:19.370  1664  1664 I audioserver: main: initialization done in 537.995 ms, joining thread pool
05-24 16:06:19.384  1655  1688 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 16:06:19.384  1655  1688 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 16:06:19.385  1655  1688 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 16:06:19.385  1655  1688 W audio_engineer_test: unknown enum value string receiver for ctl TFA98XX Profile
05-24 16:06:19.385  1655  1688 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 16:06:19.385  1655  1688 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 16:06:19.385  1655  1688 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 16:06:19.385  1655  1688 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 16:06:19.395  1304  1304 I AS.AudioService: Stream 5: using max vol of 7
05-24 16:06:19.395  1304  1304 I AS.AudioService: Stream 5: using default vol of 5
05-24 16:06:19.395  1304  1304 I AS.AudioService: Stream 2: using max vol of 7
05-24 16:06:19.395  1304  1304 I AS.AudioService: Stream 2: using default vol of 5
05-24 16:06:19.399  1304  1304 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 16:06:19.431  1304  1304 I SystemServiceManager: Starting com.android.server.soundtrigger_middleware.SoundTriggerMiddlewareService$Lifecycle
05-24 16:06:19.431  1304  1713 I AS.AudioService: updateIndexFactors() stream:0 index min/max:1/15 indexStepFactor:2.3333333
05-24 16:06:19.433  1304  1713 I AS.AudioService: updateIndexFactors() stream:1 index min/max:0/7 indexStepFactor:1.0
05-24 16:06:19.433  1304  1713 I AS.AudioService: updateIndexFactors() stream:2 index min/max:0/7 indexStepFactor:1.0
05-24 16:06:19.434  1304  1713 I AS.AudioService: updateIndexFactors() stream:3 index min/max:0/15 indexStepFactor:1.0
05-24 16:06:19.434  1304  1713 I AS.AudioService: updateIndexFactors() stream:4 index min/max:1/7 indexStepFactor:1.0
05-24 16:06:19.435  1304  1713 I AS.AudioService: updateIndexFactors() stream:5 index min/max:0/7 indexStepFactor:1.0
05-24 16:06:19.435  1304  1713 I AS.AudioService: updateIndexFactors() stream:7 index min/max:0/7 indexStepFactor:1.0
05-24 16:06:19.435  1304  1713 I AS.AudioService: updateIndexFactors() stream:8 index min/max:0/15 indexStepFactor:1.0
05-24 16:06:19.436  1304  1713 I AS.AudioService: updateIndexFactors() stream:9 index min/max:0/15 indexStepFactor:1.0
05-24 16:06:19.436  1304  1713 I AS.AudioService: updateIndexFactors() stream:10 index min/max:1/15 indexStepFactor:1.0
05-24 16:06:19.437  1304  1713 I AS.AudioService: updateIndexFactors() stream:11 index min/max:0/15 indexStepFactor:1.0
05-24 16:06:19.442  1304  1304 I SystemServiceManager: Starting com.android.server.DockObserver
05-24 16:06:19.444  1304  1304 W WiredAccessoryManager: This kernel does not have usb audio support
05-24 16:06:19.444  1304  1304 W WiredAccessoryManager: This kernel does not have HDMI audio support
05-24 16:06:19.444  1304  1304 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/3/0 does not have DP audio support
05-24 16:06:19.444  1304  1304 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/2/0 does not have DP audio support
05-24 16:06:19.444  1304  1304 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/1/0 does not have DP audio support
05-24 16:06:19.444  1304  1304 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/0/0 does not have DP audio support
05-24 16:06:19.445  1304  1304 I SystemServiceManager: Starting com.android.server.midi.MidiService$Lifecycle
05-24 16:06:19.445  1304  1304 I SystemServiceManager: Starting com.android.server.adb.AdbService$Lifecycle
05-24 16:06:19.447  1304  1304 I SystemServiceManager: Starting com.android.server.usb.UsbService$Lifecycle
05-24 16:06:19.447  1304  1304 I SystemServiceManager: Starting com.android.server.SerialService$Lifecycle
05-24 16:06:19.448  1304  1304 I HardwarePropertiesManagerService-JNI: Thermal AIDL service is not declared, trying HIDL
05-24 16:06:19.450  1304  1304 I SystemServiceManager: Starting com.android.server.twilight.TwilightService
05-24 16:06:19.451  1304  1304 I SystemServiceManager: Starting com.android.server.display.color.ColorDisplayService
05-24 16:06:19.452  1304  1304 I SystemServiceManager: Starting com.android.server.job.JobSchedulerService
05-24 16:06:19.454  1304  1713 W BroadcastLoopers: Found previously unknown looper Thread[AudioService,5,main]
05-24 16:06:19.456  1304  1601 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 16:06:19.457  1304  1663 I UsbDeviceManager: Usb gadget hal service started android.hardware.usb.gadget@1.0::IUsbGadget default
05-24 16:06:19.459  1304  1333 W JobInfo : Job 'com.google.android.setupwizard/.deviceorigin.provider.DeviceOriginWipeOutJobService#8580' has a deadline with no functional constraints. The deadline won't improve job execution latency. Consider removing the deadline.
05-24 16:06:19.461  1304  1601 W StorageManagerService: No primary storage defined yet; hacking together a stub
05-24 16:06:19.464  1304  1304 I SystemServiceManager: Starting com.android.server.soundtrigger.SoundTriggerService
05-24 16:06:19.465  1304  1304 I SystemServiceManager: Starting com.android.server.trust.TrustManagerService
05-24 16:06:19.466  1304  1304 I SystemServiceManager: Starting com.android.server.backup.BackupManagerService$Lifecycle
05-24 16:06:19.467  1304  1304 I SystemServiceManager: Starting com.android.server.appwidget.AppWidgetService
05-24 16:06:19.473  1304  1333 W JobInfo : Job 'android/com.android.server.usage.UsageStatsIdleService#0' has a deadline with no functional constraints. The deadline won't improve job execution latency. Consider removing the deadline.
05-24 16:06:19.474  1304  1304 I SystemServiceManager: Starting com.android.server.voiceinteraction.VoiceInteractionManagerService
05-24 16:06:19.477  1664  1664 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_HEARING_AID, connection: wireless}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 16:06:19.477  1664  1664 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x8000000, enabled 1, streamToDriveAbs 3
05-24 16:06:19.477  1304  1601 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 16:06:19.477  1664  1664 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_HEADSET, connection: bt-le}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 16:06:19.477  1664  1664 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x20000000, enabled 1, streamToDriveAbs 3
05-24 16:06:19.477  1664  1664 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_SPEAKER, connection: bt-le}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 16:06:19.477  1664  1664 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x20000001, enabled 1, streamToDriveAbs 3
05-24 16:06:19.478  1664  1664 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_BROADCAST, connection: bt-le}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 16:06:19.478  1664  1664 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x20000002, enabled 1, streamToDriveAbs 3
05-24 16:06:19.478  1304  1663 I UsbPortManager: Usb hal service started android.hardware.usb@1.0::IUsb default
05-24 16:06:19.478  1304  1304 I SystemServiceManager: Starting com.android.server.GestureLauncherService
05-24 16:06:19.479  1304  1304 I SystemServiceManager: Starting com.android.server.SensorNotificationService
05-24 16:06:19.479  1304  1713 E BluetoothAdapter: Bluetooth service is null
05-24 16:06:19.480  1304  1713 E BluetoothAdapter: Bluetooth service is null
05-24 16:06:19.480  1304  1713 E BluetoothAdapter: Bluetooth service is null
05-24 16:06:19.481  1304  1713 E BluetoothAdapter: Bluetooth service is null
05-24 16:06:19.481  1304  1713 I AS.SpatializerHelper: init effectExpected=false
05-24 16:06:19.481  1304  1713 I AS.SpatializerHelper: init(): setting state to STATE_NOT_SUPPORTED due to effect not expected
05-24 16:06:19.481   869   869 I android.hardware.usb@1.3-service-mediatekv2: Registering 1.2 callback
05-24 16:06:19.481   869   869 I android.hardware.usb@1.3-service-mediatekv2: registering callback
05-24 16:06:19.481   869  1727 E android.hardware.usb@1.3-service-mediatekv2: creating thread
05-24 16:06:19.482   869   869 I android.hardware.usb@1.3-service-mediatekv2: port0
05-24 16:06:19.482   869   869 I android.hardware.usb@1.3-service-mediatekv2: HAL version V1_2
05-24 16:06:19.482   869   869 I android.hardware.usb@1.3-service-mediatekv2: 0:port0 connected:1 canChangeMode:1 canChagedata:0 canChangePower:0 supportedModes:3
05-24 16:06:19.482   869   869 I android.hardware.usb@1.3-service-mediatekv2: ContaminantDetectionStatus:2 ContaminantProtectionStatus:0
05-24 16:06:19.559  1304  1304 I SystemServiceManager: Starting com.android.server.emergency.EmergencyAffordanceService
05-24 16:06:19.559  1304  1304 I SystemServiceManager: Starting com.android.server.blob.BlobStoreManagerService
05-24 16:06:19.562  1304  1304 I SystemServiceManager: Starting com.android.server.dreams.DreamManagerService
05-24 16:06:19.566   823   827 I ServiceManagerCppClient: Waiting for service 'statscompanion' on '/dev/binder'...
05-24 16:06:19.567  1304  1304 I SystemServiceManager: Starting com.android.server.print.PrintManagerService
05-24 16:06:19.568  1304  1304 I SystemServiceManager: Starting com.android.server.security.AttestationVerificationManagerService
05-24 16:06:19.569  1304  1304 I SystemServiceManager: Starting com.android.server.companion.CompanionDeviceManagerService
05-24 16:06:19.576  1304  1304 I SystemServiceManager: Starting com.android.server.companion.virtual.VirtualDeviceManagerService
05-24 16:06:19.579  1304  1304 I SystemServiceManager: Starting com.android.server.restrictions.RestrictionsManagerService
05-24 16:06:19.579  1304  1304 I SystemServiceManager: Starting com.android.server.media.MediaSessionService
05-24 16:06:19.581  1304  1304 I SystemServiceManager: Starting com.android.server.media.MediaResourceMonitorService
05-24 16:06:19.584  1304  1304 I SystemServiceManager: Starting com.android.server.biometrics.sensors.face.FaceService
05-24 16:06:19.585  1304  1304 I SystemServiceManager: Starting com.android.server.biometrics.sensors.fingerprint.FingerprintService
05-24 16:06:19.586  1304  1304 I SystemServiceManager: Starting com.android.server.biometrics.BiometricService
05-24 16:06:19.589  1304  1304 I CameraManagerGlobal: Connecting to camera service
05-24 16:06:19.594  1304  1304 I SystemServiceManager: Starting com.android.server.biometrics.AuthService
05-24 16:06:19.594  1304  1304 I FingerprintService: Before:getDeclaredInstances: IFingerprint instance found, a.length=0
05-24 16:06:19.594  1304  1304 I FingerprintService: After:getDeclaredInstances: a.length=1
05-24 16:06:19.595  1304  1304 I FaceService: Before:getDeclaredInstances: IFace instance found, a.length=0
05-24 16:06:19.595  1304  1304 I FaceService: After:getDeclaredInstances: a.length=1
05-24 16:06:19.595  1304  1304 E AuthService: Unknown modality: 2
05-24 16:06:19.596  1304  1304 I SystemServiceManager: Starting com.android.server.security.authenticationpolicy.AuthenticationPolicyService
05-24 16:06:19.598  1304  1304 I SystemServiceManager: Starting com.android.server.app.AppLockManagerService$Lifecycle
05-24 16:06:19.601  1304  1304 I SystemServiceManager: Starting com.android.server.display.FreeformService
05-24 16:06:19.612  1304  1304 I SystemServiceManager: Starting com.android.server.pm.ShortcutService$Lifecycle
05-24 16:06:19.615  1304  1304 I SystemServiceManager: Starting com.android.server.pm.LauncherAppsService
05-24 16:06:19.618  1304  1304 I SystemServiceManager: Starting com.android.server.pm.CrossProfileAppsService
05-24 16:06:19.619  1304  1304 I SystemServiceManager: Starting com.android.server.pocket.PocketService
05-24 16:06:19.630  1304  1304 I SystemServiceManager: Starting com.android.server.people.PeopleService
05-24 16:06:19.631  1304  1304 I SystemServiceManager: Starting com.android.server.media.metrics.MediaMetricsManagerService
05-24 16:06:19.632  1304  1304 I SystemServiceManager: Starting com.android.server.pm.BackgroundInstallControlService
05-24 16:06:19.634  1304  1304 I SystemServiceManager: Starting com.android.server.voltage.CustomDeviceConfigService
05-24 16:06:19.634  1304  1304 I SystemServiceManager: Starting com.android.server.custom.LineageHardwareService
05-24 16:06:19.636  1304  1304 I SystemServiceManager: Starting com.android.server.custom.display.LiveDisplayService
05-24 16:06:19.638  1304  1304 I SystemServiceManager: Starting com.android.server.custom.health.HealthInterfaceService
05-24 16:06:19.640  1304  1304 I SystemServiceManager: Starting com.android.server.HideAppListService
05-24 16:06:19.640  1304  1304 I HideAppListService: Starting HideAppListService
05-24 16:06:19.640  1304  1304 I SystemServiceManager: Starting com.android.server.GameSpaceManagerService
05-24 16:06:19.641  1304  1304 I SystemServiceManager: Starting com.android.server.media.projection.MediaProjectionManagerService
05-24 16:06:19.646  1304  1304 I SystemServiceManager: Starting com.android.server.slice.SliceManagerService$Lifecycle
05-24 16:06:19.649  1304  1304 I SystemServiceManager: Starting com.android.server.stats.StatsCompanion$Lifecycle
05-24 16:06:19.650  1304  1304 I SystemServiceManager: Starting com.android.server.scheduling.RebootReadinessManagerService$Lifecycle
05-24 16:06:19.653  1304  1304 I SystemServiceManager: Starting com.android.server.stats.pull.StatsPullAtomService
05-24 16:06:19.654  1304  1304 I SystemServiceManager: Starting com.android.server.stats.bootstrap.StatsBootstrapAtomService$Lifecycle
05-24 16:06:19.654  1304  1304 I SystemServiceManager: Starting com.android.server.incident.IncidentCompanionService
05-24 16:06:19.655  1304  1304 I SystemServiceManager: Starting com.android.server.sdksandbox.SdkSandboxManagerService$Lifecycle
05-24 16:06:19.662  1304  1304 I SystemServiceManager: Starting com.android.server.adservices.AdServicesManagerService$Lifecycle
05-24 16:06:19.664  1304  1304 I SystemServiceManager: Starting com.android.server.ondevicepersonalization.OnDevicePersonalizationSystemService$Lifecycle
05-24 16:06:19.665  1304  1304 I ondevicepersonalization: OnDevicePersonalizationSystemService started!
05-24 16:06:19.665  1304  1304 I SystemServiceManager: Starting android.os.profiling.ProfilingService$Lifecycle
05-24 16:06:19.666   823   827 I ServiceManagerCppClient: Waiting for service 'statscompanion' on '/dev/binder' successful after waiting 100ms
05-24 16:06:19.670  1304  1304 I SystemServiceManager: Starting com.android.server.MmsServiceBroker
05-24 16:06:19.671  1304  1304 I SystemServiceManager: Starting com.android.server.autofill.AutofillManagerService
05-24 16:06:19.674  1304  1304 E AutofillManagerServiceImpl: Bad service name: proton.android.pass/proton.android.pass.autofill.ProtonPassAutofillService
05-24 16:06:19.677  1304  1304 I SystemServiceManager: Starting com.android.server.credentials.CredentialManagerService
05-24 16:06:19.679  1304  1304 I SystemServiceManager: Starting com.android.server.clipboard.ClipboardService
05-24 16:06:19.682  1304  1304 I SystemServiceManager: Starting com.android.server.appbinding.AppBindingService$Lifecycle
05-24 16:06:19.683  1304  1304 I SystemServiceManager: Starting com.android.server.tracing.TracingServiceProxy
05-24 16:06:19.685   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.authsecret@1.0::IAuthSecret/default in either framework or device VINTF manifest.
05-24 16:06:19.686  1304  1304 I LockSettingsService: Device doesn't implement AuthSecret HAL
05-24 16:06:19.687  1304  1304 I SystemServiceManager: Starting phase 480
05-24 16:06:19.710  1304  1304 W PocketService: Un-handled boot phase:480
05-24 16:06:19.711  1304  1304 I SystemServiceManager: Starting phase 500
05-24 16:06:19.711  1304  1304 E StatsPullAtomCallbackImpl: Failed to start PowerStatsService statsd pullers
05-24 16:06:19.714  1304  1304 E BatteryStatsService: Could not register PowerStatsInternal
05-24 16:06:19.716   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.power.stats@1.0::IPowerStats/default in either framework or device VINTF manifest.
05-24 16:06:19.716  1304  1354 E BatteryStatsService: Unable to load Power.Stats.HAL. Setting rail availability to false
05-24 16:06:19.717  1304  1354 E BluetoothAdapter: Bluetooth service is null
05-24 16:06:19.740  1304  1361 W KeyguardServiceDelegate: onScreenTurningOn(): no keyguard service!
05-24 16:06:19.774  1304  1344 E AppWidgetManager: Notify service of inheritance info
05-24 16:06:19.774  1304  1344 E AppWidgetManager: 	at com.android.server.appwidget.AppWidgetServiceImpl.ensureGroupStateLoadedLocked(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:22)
05-24 16:06:19.774  1304  1344 E AppWidgetManager: 	at com.android.server.appwidget.AppWidgetServiceImpl.getInstalledProvidersForProfile(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:76)
05-24 16:06:19.824  1304  1304 I WifiScanningService: Starting wifiscanner
05-24 16:06:19.826  1304  1304 I EthernetServiceImpl: Starting Ethernet service
05-24 16:06:19.826  1304  1640 I WifiContext: Got resolveInfos for com.android.server.wifi.intent.action.SERVICE_WIFI_RESOURCES_APK: [ResolveInfo{3b41866 com.android.wifi.resources/android.app.Activity m=0x108000 userHandle=UserHandle{0}}]
05-24 16:06:19.841  1304  1636 I WifiService: WifiService starting up with Wi-Fi disabled
05-24 16:06:19.853  1304  1636 I WifiHalHidlImpl: Initializing the WiFi HAL
05-24 16:06:19.854  1304  1636 I WifiHalHidlImpl: initServiceManagerIfNecessaryLocked
05-24 16:06:19.858  1304  1636 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.2::IServiceManager/default
05-24 16:06:19.860  1304  1636 I WifiHalHidlImpl: initWifiIfNecessaryLocked
05-24 16:06:19.861   574   574 I hwservicemanager: Since android.hardware.wifi@1.0::IWifi/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 16:06:19.863   574   574 I hwservicemanager: Since android.hardware.wifi@1.0::IWifi/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 16:06:19.863  1304  1636 I HidlServiceManagement: getService: Trying again for android.hardware.wifi@1.0::IWifi/default...
05-24 16:06:19.890  1304  1344 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 16:06:19.891  1304  1344 I HealthServiceWrapperHidl: health: HealthServiceWrapper listening to instance default
05-24 16:06:19.898   574   574 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IKeySwapper/default in either framework or device VINTF manifest.
05-24 16:06:19.898  1304  1344 I StatsPullAtomService: register thermal listener successfully
05-24 16:06:19.913  1304  1304 I SystemServiceManager: Starting com.android.server.policy.PermissionPolicyService
05-24 16:06:19.916  1759  1759 I android.hardware.wifi@1.0-service-lazy: Wifi Hal is booting up...
05-24 16:06:19.920  1759  1759 I HidlServiceManagement: Registered android.hardware.wifi@1.5::IWifi/default
05-24 16:06:19.921  1759  1759 I HidlServiceManagement: Removing namespace from process name android.hardware.wifi@1.0-service-lazy to wifi@1.0-service-lazy.
05-24 16:06:19.927   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.wifi@1.6::IWifi/default in either framework or device VINTF manifest.
05-24 16:06:19.934  1304  1304 E UserManagerService: Auto-lock preference updated but private space user not found
05-24 16:06:19.937  1304  1304 I AS.AudioService: registerAudioPolicy for android.media.audiopolicy.AudioPolicy$1@36c01a9 u/pid:1000/1304 with config:reg:32:ap:0
05-24 16:06:19.960  1304  1304 I SystemServiceManager: Starting com.android.server.crashrecovery.CrashRecoveryModule$Lifecycle
05-24 16:06:19.969  1304  1365 W DefaultPermGrantPolicy: No such package:com.google.android.apps.camera.services
05-24 16:06:19.971  1304  1365 W DefaultPermGrantPolicy: No such package:com.verizon.mips.services
05-24 16:06:19.973  1304  1304 I BrightnessSynchronizer: Initial brightness readings: 88(int), 0.34251967(float)
05-24 16:06:19.974  1304  1365 W DefaultPermGrantPolicy: No such package:com.google.android.adservices
05-24 16:06:19.977  1304  1365 W DefaultPermGrantPolicy: No such package:com.google.android.apps.actionsservice
05-24 16:06:19.978  1304  1304 I SystemServiceManager: Starting com.android.server.app.GameManagerService$Lifecycle
05-24 16:06:19.988  1304  1304 I SystemServiceManager: Starting phase 520
05-24 16:06:20.006   574   574 I hwservicemanager: getTransport: Cannot find entry vendor.oplus.hardware.commondcs@1.0::ICommonDcsHalService/commondcsservice in either framework or device VINTF manifest.
05-24 16:06:20.006  1071  1341 E android.hardware.biometrics.fingerprint@2.1-service: service NULL
05-24 16:06:20.047  1304  1304 W SystemServiceManager: Service com.android.server.notification.NotificationManagerService took 57 ms in onBootPhase
05-24 16:06:20.048  1304  1304 W PocketService: Un-handled boot phase:520
05-24 16:06:20.051  1304  1304 I SystemServiceManager: Starting com.android.safetycenter.SafetyCenterService
05-24 16:06:20.072  1304  1304 I SystemServiceManager: Starting com.android.server.appsearch.AppSearchModule$Lifecycle
05-24 16:06:20.085  1304  1304 I AppSearchModule: AppsIndexer service is disabled.
05-24 16:06:20.085  1304  1304 I AppSearchModule: AppOpenEventIndexer service is disabled.
05-24 16:06:20.086  1304  1304 I SystemServiceManager: Starting com.android.server.media.MediaCommunicationService
05-24 16:06:20.088  1304  1304 I SystemServiceManager: Starting com.android.server.compat.overrides.AppCompatOverridesService$Lifecycle
05-24 16:06:20.089  1304  1304 I SystemServiceManager: Starting com.android.server.power.SleepModeService
05-24 16:06:20.093  1304  1304 I SystemServiceManager: Starting com.android.server.healthconnect.HealthConnectManagerService
05-24 16:06:20.103  1304  1304 I SystemServiceManager: Starting com.android.server.devicelock.DeviceLockService
05-24 16:06:20.107  1304  1304 I DeviceLockService: Registering device_lock
05-24 16:06:20.108  1304  1304 I SystemServiceManager: Starting com.android.server.SensitiveContentProtectionManagerService
05-24 16:06:20.141  1304  1304 E ActivityManager: Unable to find com.android.overlay.permissioncontroller/u0
05-24 16:06:20.144  1304  1304 E ActivityManager: Unable to find com.google.android.printservice.recommendation/u0
05-24 16:06:20.199  1304  1304 I SystemServer: Making services ready
05-24 16:06:20.199  1304  1304 I SystemServiceManager: Starting phase 550
05-24 16:06:20.210  1304  1304 I ThermalManagerService$ThermalHalWrapper: Thermal HAL 2.0 service connected.
05-24 16:06:20.210   863   863 I <EMAIL>: thermal_zone_num are changed0
05-24 16:06:20.211   863   863 W <EMAIL>: tz_data_v1[2].tz_idx:0
05-24 16:06:20.212   863   863 W <EMAIL>: tz_data_v1[3].tz_idx:2
05-24 16:06:20.212   863   863 W <EMAIL>: tz_data_v1[5].tz_idx:3
05-24 16:06:20.213   863   863 W <EMAIL>: tz_data_v1[0].tz_idx:5
05-24 16:06:20.213   863   863 W <EMAIL>: tz_data_v1[1].tz_idx:5
05-24 16:06:20.213   863   863 W <EMAIL>: tz_data_v1[9].tz_idx:5
05-24 16:06:20.214   863   863 W <EMAIL>: init_tz_path_v1:find out tz path
05-24 16:06:20.214   863   863 W <EMAIL>: get_tz_map: tz0, name=mtktscpu, label=CPU, muti_tz_num=1
05-24 16:06:20.214   863   863 W <EMAIL>: tz_idx0:5, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 16:06:20.214   863   863 W <EMAIL>: get_tz_map: tz1, name=mtktscpu, label=GPU, muti_tz_num=1
05-24 16:06:20.214   863   863 W <EMAIL>: tz_idx0:5, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 16:06:20.214   863   863 W <EMAIL>: get_tz_map: tz2, name=mtktsbattery, label=BATTERY, muti_tz_num=1
05-24 16:06:20.214   863   863 W <EMAIL>: tz_idx0:0, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 16:06:20.214   863   863 W <EMAIL>: get_tz_map: tz3, name=mtktsAP, label=SKIN, muti_tz_num=1
05-24 16:06:20.214   863   863 W <EMAIL>: tz_idx0:2, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 16:06:20.214   863   863 W <EMAIL>: get_tz_map: tz4, name=notsupport, label=USB_PORT, muti_tz_num=1
05-24 16:06:20.214   863   863 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 16:06:20.214   863   863 W <EMAIL>: get_tz_map: tz5, name=mtktsbtsmdpa, label=POWER_AMPLIFIER, muti_tz_num=1
05-24 16:06:20.214   863   863 W <EMAIL>: tz_idx0:3, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 16:06:20.214   863   863 W <EMAIL>: get_tz_map: tz6, name=notsupport, label=BCL_VOLTAGE, muti_tz_num=1
05-24 16:06:20.215   863   863 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 16:06:20.215   863   863 W <EMAIL>: get_tz_map: tz7, name=notsupport, label=BCL_CURRENT, muti_tz_num=1
05-24 16:06:20.215   863   863 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 16:06:20.215   863   863 W <EMAIL>: get_tz_map: tz8, name=notsupport, label=BCL_PERCENTAGE, muti_tz_num=1
05-24 16:06:20.215   863   863 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 16:06:20.215   863   863 W <EMAIL>: get_tz_map: tz9, name=mtktscpu, label=NPU, muti_tz_num=1
05-24 16:06:20.215   863   863 W <EMAIL>: tz_idx0:5, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 16:06:20.215   863   863 I <EMAIL>: fill_temperatures filterType0 name: CPU type: CPU throttlingStatus: NONE value: 56.476 ret_temps size 0
05-24 16:06:20.215   863   863 I <EMAIL>: fill_temperatures filterType0 name: GPU type: GPU throttlingStatus: NONE value: 56.476 ret_temps size 1
05-24 16:06:20.216   863   863 I <EMAIL>: fill_temperatures filterType0 name: BATTERY type: BATTERY throttlingStatus: NONE value: 37.133 ret_temps size 2
05-24 16:06:20.216   863   863 I <EMAIL>: fill_temperatures filterType0 name: SKIN type: SKIN throttlingStatus: NONE value: 45.23 ret_temps size 3
05-24 16:06:20.218   863   863 I <EMAIL>: fill_temperatures filterType0 name: POWER_AMPLIFIER type: POWER_AMPLIFIER throttlingStatus: NONE value: 43.841 ret_temps size 4
05-24 16:06:20.218   863   863 I <EMAIL>: fill_temperatures filterType0 name: NPU type: NPU throttlingStatus: NONE value: 56.492 ret_temps size 5
05-24 16:06:20.220   863   863 I <EMAIL>: fill_thresholds filterType1 name: SKIN type: SKIN hotThrottlingThresholds: 50 vrThrottlingThreshold: 50 ret_thresholds size 0
05-24 16:06:20.231  1304  1304 W ActivityManager: Too early to start/bind service in system_server: Phase=550 ComponentInfo{com.android.server.telecom/com.android.server.telecom.components.TelecomService}
05-24 16:06:20.231  1304  1304 W ActivityManager: Too early to start/bind service in system_server: Phase=550 ComponentInfo{com.android.server.telecom/com.android.server.telecom.components.TelecomService}
05-24 16:06:20.289  1304  1304 W SystemServiceManager: Service com.android.server.content.ContentService$Lifecycle took 56 ms in onBootPhase
05-24 16:06:20.293   574   574 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IGloveMode/default in either framework or device VINTF manifest.
05-24 16:06:20.295   574   574 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IStylusMode/default in either framework or device VINTF manifest.
05-24 16:06:20.298   574   574 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IHighTouchPollingRate/default in either framework or device VINTF manifest.
05-24 16:06:20.331  1304  1625 W StorageManagerService: Failed to get storage lifetime
05-24 16:06:20.344  1012  1083 I OMXClient: IOmx service obtained
05-24 16:06:20.346  1015  1064 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.aac.decoder'  err=ComponentNotFound(0x80001003)
05-24 16:06:20.347  1012  1083 W OmxInfoBuilder: Fail to add media type audio/mp4a-latm to codec OMX.google.aac.decoder
05-24 16:06:20.349  1012  1083 I OMXClient: IOmx service obtained
05-24 16:06:20.351  1015  1064 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.amrnb.decoder'  err=ComponentNotFound(0x80001003)
05-24 16:06:20.351  1012  1083 W OmxInfoBuilder: Fail to add media type audio/3gpp to codec OMX.google.amrnb.decoder
05-24 16:06:20.353  1012  1083 I OMXClient: IOmx service obtained
05-24 16:06:20.354  1015  1064 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.amrwb.decoder'  err=ComponentNotFound(0x80001003)
05-24 16:06:20.355  1012  1083 W OmxInfoBuilder: Fail to add media type audio/amr-wb to codec OMX.google.amrwb.decoder
05-24 16:06:20.357  1012  1083 I OMXClient: IOmx service obtained
05-24 16:06:20.358  1015  1064 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.flac.decoder'  err=ComponentNotFound(0x80001003)
05-24 16:06:20.359  1012  1083 W OmxInfoBuilder: Fail to add media type audio/flac to codec OMX.google.flac.decoder
05-24 16:06:20.360  1012  1083 I OMXClient: IOmx service obtained
05-24 16:06:20.361  1015  1064 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.g711.alaw.decoder'  err=ComponentNotFound(0x80001003)
05-24 16:06:20.361  1012  1083 W OmxInfoBuilder: Fail to add media type audio/g711-alaw to codec OMX.google.g711.alaw.decoder
05-24 16:06:20.363  1012  1083 I OMXClient: IOmx service obtained
05-24 16:06:20.364  1015  1064 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.g711.mlaw.decoder'  err=ComponentNotFound(0x80001003)
05-24 16:06:20.365  1012  1083 W OmxInfoBuilder: Fail to add media type audio/g711-mlaw to codec OMX.google.g711.mlaw.decoder
05-24 16:06:20.367  1012  1083 I OMXClient: IOmx service obtained
05-24 16:06:20.368  1015  1064 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.mp3.decoder'  err=ComponentNotFound(0x80001003)
05-24 16:06:20.369  1012  1083 W OmxInfoBuilder: Fail to add media type audio/mpeg to codec OMX.google.mp3.decoder
05-24 16:06:20.371  1012  1083 I OMXClient: IOmx service obtained
05-24 16:06:20.372  1015  1015 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.opus.decoder'  err=ComponentNotFound(0x80001003)
05-24 16:06:20.372  1012  1083 W OmxInfoBuilder: Fail to add media type audio/opus to codec OMX.google.opus.decoder
05-24 16:06:20.374  1012  1083 I OMXClient: IOmx service obtained
05-24 16:06:20.375  1015  1064 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.raw.decoder'  err=ComponentNotFound(0x80001003)
05-24 16:06:20.376  1012  1083 W OmxInfoBuilder: Fail to add media type audio/raw to codec OMX.google.raw.decoder
05-24 16:06:20.378  1012  1083 I OMXClient: IOmx service obtained
05-24 16:06:20.379  1015  1064 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.vorbis.decoder'  err=ComponentNotFound(0x80001003)
05-24 16:06:20.379  1012  1083 W OmxInfoBuilder: Fail to add media type audio/vorbis to codec OMX.google.vorbis.decoder
05-24 16:06:20.381  1012  1083 I OMXClient: IOmx service obtained
05-24 16:06:20.383  1015  1015 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.aac.encoder'  err=ComponentNotFound(0x80001003)
05-24 16:06:20.383  1012  1083 W OmxInfoBuilder: Fail to add media type audio/mp4a-latm to codec OMX.google.aac.encoder
05-24 16:06:20.385  1012  1083 I OMXClient: IOmx service obtained
05-24 16:06:20.386  1015  1064 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.amrnb.encoder'  err=ComponentNotFound(0x80001003)
05-24 16:06:20.386  1012  1083 W OmxInfoBuilder: Fail to add media type audio/3gpp to codec OMX.google.amrnb.encoder
05-24 16:06:20.388  1012  1083 I OMXClient: IOmx service obtained
05-24 16:06:20.389  1015  1064 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.amrwb.encoder'  err=ComponentNotFound(0x80001003)
05-24 16:06:20.390  1012  1083 W OmxInfoBuilder: Fail to add media type audio/amr-wb to codec OMX.google.amrwb.encoder
05-24 16:06:20.392  1012  1083 I OMXClient: IOmx service obtained
05-24 16:06:20.394  1015  1775 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.flac.encoder'  err=ComponentNotFound(0x80001003)
05-24 16:06:20.394  1012  1083 W OmxInfoBuilder: Fail to add media type audio/flac to codec OMX.google.flac.encoder
05-24 16:06:20.397  1012  1083 I Codec2Client: Available Codec2 services: "default" "software"
05-24 16:06:20.451  1304  1304 W SystemServiceManager: Service com.android.server.NetworkStatsServiceInitializer took 122 ms in onBootPhase
05-24 16:06:20.451  1304  1304 I ConnectivityServiceInitializerB: Starting vcn_management
05-24 16:06:20.462  1304  1650 I VcnManagementService: new snapshot: TelephonySubscriptionSnapshot{ mActiveDataSubId=-1, mSubIdToInfoMap={}, mSubIdToCarrierConfigMap={}, mPrivilegedPackages={} }
05-24 16:06:20.471  1304  1713 I AS.AudioDeviceBroker: setBluetoothScoOn: false, mBluetoothScoOn: false, btScoRequesterUId: -1, from: resetBluetoothSco
05-24 16:06:20.476  1304  1710 W BroadcastLoopers: Found previously unknown looper Thread[AudioDeviceBroker,5,main]
05-24 16:06:20.477  1664  1664 I AudioFlinger: systemReady
05-24 16:06:20.477   869   869 I android.hardware.usb@1.3-service-mediatekv2: port0
05-24 16:06:20.478   869   869 I android.hardware.usb@1.3-service-mediatekv2: HAL version V1_2
05-24 16:06:20.478   869   869 I android.hardware.usb@1.3-service-mediatekv2: 0:port0 connected:1 canChangeMode:1 canChagedata:0 canChangePower:0 supportedModes:3
05-24 16:06:20.478   869   869 I android.hardware.usb@1.3-service-mediatekv2: ContaminantDetectionStatus:2 ContaminantProtectionStatus:0
05-24 16:06:20.482  1304  1304 I AppLockManagerService: onBootCompleted
05-24 16:06:20.485  1304  1304 I LMOFreeform/LMOFreeformUIService: add SystemService: com.libremobileos.freeform.server.LMOFreeformUIService@eef1f7e
05-24 16:06:20.485  1304  1346 I ActivityManager: Start proc 1781:com.android.systemui/u0a226 for service {com.android.systemui/com.android.systemui.wallpapers.ImageWallpaper}
05-24 16:06:20.485  1304  1304 W PocketService: Un-handled boot phase:550
05-24 16:06:20.486  1304  1304 I AppBindingService: Updating constants with: null
05-24 16:06:20.588  1304  1304 W SystemServiceManager: Service com.android.server.policy.PermissionPolicyService took 102 ms in onBootPhase
05-24 16:06:20.629   822   822 I netd    : networkSetPermissionForUser(1, [1002, 10160, 10191, 10204, 10221, 10222, 10271, 10272, 10273, 10282, 10355]) <0.06ms>
05-24 16:06:20.629   822   822 I netd    : networkSetPermissionForUser(2, [1000, 1001, 1073, 2000, 10152, 10171, 10183, 10199, 10201, 10205, 10226, 10234, 10251]) <0.01ms>
05-24 16:06:20.632  1304  1613 W PinnerService: Could not find pinlist.meta for "/product/app/webview/webview.apk": pinning as blob
05-24 16:06:20.783  1304  1304 I SystemServiceManager: Starting phase 600
05-24 16:06:20.798  1304  1304 I ServiceWatcher: [network] chose new implementation 10369/app.grapheneos.networklocation/.NetworkLocationService@0
05-24 16:06:20.804  1304  1304 I ServiceWatcher: [fused] chose new implementation 1000/com.android.location.fused/.FusedLocationService@0
05-24 16:06:20.805  1012  1083 I Codec2InfoBuilder: adding type 'audio/x-adpcm-dvi-ima'
05-24 16:06:20.806  1012  1083 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-dvi-ima
05-24 16:06:20.813  1012  1083 I Codec2InfoBuilder: adding type 'audio/x-adpcm-ms'
05-24 16:06:20.815  1012  1083 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-ms
05-24 16:06:20.822  1012  1083 I Codec2InfoBuilder: adding type 'audio/alac'
05-24 16:06:20.823  1012  1083 W AudioCapabilities: Unsupported mediaType audio/alac
05-24 16:06:20.827  1012  1083 I Codec2InfoBuilder: adding type 'audio/ape'
05-24 16:06:20.828  1012  1083 W AudioCapabilities: Unsupported mediaType audio/ape
05-24 16:06:20.830  1781  1801 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 16:06:20.841  1005  1037 W MNLD    : hal_gps_init: hal_gps_init
05-24 16:06:20.842  1304  1304 I GnssLocationProviderJni: Unable to initialize IGnssGeofencing interface.
05-24 16:06:20.844  1304  1304 I GnssManager: gnss hal initialized
05-24 16:06:20.846  1304  1304 I ServiceWatcher: [GeocoderProxy] chose new implementation null
05-24 16:06:20.847  1304  1304 I ServiceWatcher: [HardwareActivityRecognitionProxy] chose new implementation null
05-24 16:06:20.847  1304  1304 I ServiceWatcher: [GeofenceProxy] chose new implementation null
05-24 16:06:20.849  1304  1304 W SystemServiceManager: Service com.android.server.location.LocationManagerService$Lifecycle took 53 ms in onBootPhase
05-24 16:06:20.881  1304  1346 I ActivityManager: Start proc 1844:app.grapheneos.networklocation/u0a369 for service {app.grapheneos.networklocation/app.grapheneos.networklocation.NetworkLocationService}
05-24 16:06:20.942  1304  1304 I ServiceWatcher: [TrustManagerService] chose new implementation null
05-24 16:06:20.959  1304  1304 W PocketService: Un-handled boot phase:600
05-24 16:06:20.960  1304  1304 W AlarmManager: Unrecognized alarm listener com.android.server.stats.StatsCompanionService$PullingAlarmListener@42356b2
05-24 16:06:20.960  1304  1304 W AlarmManager: Unrecognized alarm listener com.android.server.stats.StatsCompanionService$PeriodicAlarmListener@7225603
05-24 16:06:20.965  1304  1304 I StatsCompanionService: Told statsd that StatsCompanionService is alive.
05-24 16:06:20.985  1304  1346 I ActivityManager: Start proc 1890:com.android.networkstack.process/1073 for service {com.android.networkstack/com.android.server.NetworkStackService}
05-24 16:06:20.994  1304  1886 E StatsCompanionService: Could not get installer for package: com.google.android.trichromelibrary
05-24 16:06:20.994  1304  1886 E StatsCompanionService: android.content.pm.PackageManager$NameNotFoundException: com.google.android.trichromelibrary
05-24 16:06:20.994  1304  1886 E StatsCompanionService: 	at android.app.ApplicationPackageManager.getInstallSourceInfo(ApplicationPackageManager.java:2772)
05-24 16:06:20.994  1304  1886 E StatsCompanionService: 	at com.android.server.stats.StatsCompanionService.getInstallerPackageName(StatsCompanionService.java:153)
05-24 16:06:20.994  1304  1886 E StatsCompanionService: 	at com.android.server.stats.StatsCompanionService.$r8$lambda$MBPStrBhgnmbybdtzkoTAe-YOYw(StatsCompanionService.java:229)
05-24 16:06:20.994  1304  1886 E StatsCompanionService: 	at com.android.server.stats.StatsCompanionService$$ExternalSyntheticLambda1.run(R8$$SyntheticClass:0)
05-24 16:06:20.994  1304  1886 E StatsCompanionService: 	at android.os.Handler.handleCallback(Handler.java:991)
05-24 16:06:20.994  1304  1886 E StatsCompanionService: 	at android.os.Handler.dispatchMessage(Handler.java:102)
05-24 16:06:20.994  1304  1886 E StatsCompanionService: 	at android.os.Looper.loopOnce(Looper.java:232)
05-24 16:06:20.994  1304  1886 E StatsCompanionService: 	at android.os.Looper.loop(Looper.java:317)
05-24 16:06:20.994  1304  1886 E StatsCompanionService: 	at android.os.HandlerThread.run(HandlerThread.java:85)
05-24 16:06:21.001  1844  1844 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-24 16:06:21.001  1304  1304 I MR2ServiceImpl: switchUser | user: 0
05-24 16:06:21.001  1304  1304 I MmsServiceBroker: Delay connecting to MmsService until an API is called
05-24 16:06:21.020  1304  1304 I SystemServiceManager: Calling onStartUser 0
05-24 16:06:21.047  1304  1617 I BluetoothSystemServer: AirplaneModeListener: Init completed. isOn=false, isOnOverrode=false
05-24 16:06:21.047  1304  1617 I BluetoothSystemServer: SatelliteModeListener: Initialized successfully with state: false
05-24 16:06:21.068  1920  1920 I WebViewZygoteInit: Starting WebViewZygoteInit
05-24 16:06:21.069  1781  1781 E SharedConnectivityManager: To support shared connectivity service on this device, the service's package name and intent action strings must not be empty
05-24 16:06:21.077  1304  1331 I ServiceWatcher: [network] connected to {app.grapheneos.networklocation/app.grapheneos.networklocation.NetworkLocationService}
05-24 16:06:21.083  1304  1304 W VoiceInteractionManager: no available voice recognition services found for user 0
05-24 16:06:21.083  1304  1304 I AppLockManagerService: onUserStarting: userId = 0
05-24 16:06:21.111  1920  1920 I WebViewZygoteInit: Beginning application preload for com.android.webview
05-24 16:06:21.133  1304  1366 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.adservices/appid
05-24 16:06:21.135  1304  1366 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.permission/appid
05-24 16:06:21.135  1920  1920 I WebViewZygoteInit: Application preload done
05-24 16:06:21.137  1304  1366 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.btservices/appid
05-24 16:06:21.145  1304  1366 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.extservices/appid
05-24 16:06:21.148  1304  1366 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.nfcservices/appid
05-24 16:06:21.188  1781  1812 E AppWidgetManager: Notify service of inheritance info
05-24 16:06:21.188  1781  1812 E AppWidgetManager: 	at com.android.internal.appwidget.IAppWidgetService$Stub$Proxy.getInstalledProvidersForProfile(IAppWidgetService.java:1071)
05-24 16:06:21.223  1012  1083 I Codec2InfoBuilder: adding type 'audio/mp4a-latm'
05-24 16:06:21.227  1012  1083 I Codec2InfoBuilder: adding type 'audio/mp4a-latm'
05-24 16:06:21.230  1012  1083 I Codec2InfoBuilder: adding type 'audio/3gpp'
05-24 16:06:21.232  1012  1083 I Codec2InfoBuilder: adding type 'audio/3gpp'
05-24 16:06:21.233  1304  1304 I SystemServiceManager: Starting com.android.server.scheduling.RebootReadinessManagerService$Lifecycle
05-24 16:06:21.233  1304  1304 I SystemServiceManager: Not starting an already started service com.android.server.scheduling.RebootReadinessManagerService$Lifecycle
05-24 16:06:21.235  1012  1083 I Codec2InfoBuilder: adding type 'audio/amr-wb'
05-24 16:06:21.236  1012  1083 I Codec2InfoBuilder: adding type 'audio/amr-wb'
05-24 16:06:21.243  1941  1941 W ContextImpl: Failed to ensure /data/user/0/com.android.se/cache: mkdir failed: ENOENT (No such file or directory)
05-24 16:06:21.243  1941  1941 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-24 16:06:21.254  1012  1083 I Codec2InfoBuilder: adding type 'audio/flac'
05-24 16:06:21.254  1941  1941 I SecureElementService: main onCreate
05-24 16:06:21.256  1941  1941 I SecureElementService: Check if terminal eSE1 is available.
05-24 16:06:21.256  1012  1083 I Codec2InfoBuilder: adding type 'audio/flac'
05-24 16:06:21.259  1012  1083 I Codec2InfoBuilder: adding type 'audio/g711-alaw'
05-24 16:06:21.261  1012  1083 I Codec2InfoBuilder: adding type 'audio/g711-mlaw'
05-24 16:06:21.270   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.secure_element@1.2::ISecureElement/eSE1 in either framework or device VINTF manifest.
05-24 16:06:21.271   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.secure_element@1.1::ISecureElement/eSE1 in either framework or device VINTF manifest.
05-24 16:06:21.272   574   574 I hwservicemanager: getTransport: Cannot find entry android.hardware.secure_element@1.0::ISecureElement/eSE1 in either framework or device VINTF manifest.
05-24 16:06:21.272  1941  1941 I SecureElementService: No HAL implementation for eSE1
05-24 16:06:21.272  1941  1941 I SecureElementService: Check if terminal SIM1 is available.
05-24 16:06:21.272  1781  1813 I CameraManagerGlobal: Connecting to camera service
05-24 16:06:21.277   574   574 I hwservicemanager: Since android.hardware.secure_element@1.2::ISecureElement/SIM1 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 16:06:21.278  1012  1083 I Codec2InfoBuilder: adding type 'audio/mpeg'
05-24 16:06:21.278   574   574 I hwservicemanager: Since android.hardware.secure_element@1.2::ISecureElement/SIM1 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 16:06:21.278  1941  1941 I HidlServiceManagement: getService: Trying again for android.hardware.secure_element@1.2::ISecureElement/SIM1...
05-24 16:06:21.281   574  1993 I hwservicemanager: Tried to start android.hardware.secure_element@1.2::ISecureElement/SIM1 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 16:06:21.282   574  1995 I hwservicemanager: Tried to start android.hardware.secure_element@1.2::ISecureElement/SIM1 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 16:06:21.285  1304  1331 I ServiceWatcher: [fused] connected to {com.android.location.fused/com.android.location.fused.FusedLocationService}
05-24 16:06:21.286  1304  1304 I ExplicitHealthCheckController: Service not ready to get health check supported packages. Binding...
05-24 16:06:21.287  1304  1304 I ExplicitHealthCheckController: Explicit health check service is bound
05-24 16:06:21.288  1012  1083 I Codec2InfoBuilder: adding type 'audio/opus'
05-24 16:06:21.290  1012  1083 I Codec2InfoBuilder: adding type 'audio/opus'
05-24 16:06:21.291  1006  1167 I AttributionAndPermissionUtils: checkPermission checkPermission (forDataDelivery 0 startDataDelivery 0): Permission soft denied for client attribution [uid 10226, pid 1781, packageName "<unknown>"]
05-24 16:06:21.293  1012  1083 I Codec2InfoBuilder: adding type 'audio/raw'
05-24 16:06:21.294  1304  1304 W TrustManagerService: EXTRA_USER_HANDLE missing or invalid, value=0
05-24 16:06:21.295  1304  1331 I ServiceWatcher: [GeocoderProxy] chose new implementation null
05-24 16:06:21.295  1304  1331 I ServiceWatcher: [HardwareActivityRecognitionProxy] chose new implementation null
05-24 16:06:21.295  1304  1331 I ServiceWatcher: [GeofenceProxy] chose new implementation null
05-24 16:06:21.295  1304  1331 I ServiceWatcher: [TrustManagerService] chose new implementation null
05-24 16:06:21.296  1304  1304 I ConnectivityModuleConnector: Networking module service connected
05-24 16:06:21.296  1304  1304 I NetworkStackClient: Network stack service connected
05-24 16:06:21.298  1304  1654 W BroadcastLoopers: Found previously unknown looper Thread[AudioService Broadcast,5,main]
05-24 16:06:21.304  1012  1083 I Codec2InfoBuilder: adding type 'audio/vorbis'
05-24 16:06:21.319  1304  1625 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-dvi-ima
05-24 16:06:21.320  1304  1625 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-ms
05-24 16:06:21.320  1304  1625 W AudioCapabilities: Unsupported mediaType audio/alac
05-24 16:06:21.320  1304  1625 W AudioCapabilities: Unsupported mediaType audio/ape
05-24 16:06:21.324  1304  1786 I Codec2Client: Available Codec2 services: "default" "software"
05-24 16:06:21.332  1304  1304 I Telecom : SystemStateHelper: Registering broadcast receiver: android.content.IntentFilter@3908757: TS.init@AAA
05-24 16:06:21.332  1304  1304 I Telecom : SystemStateHelper: Registering broadcast receiver: android.content.IntentFilter@2105244: TS.init@AAA
05-24 16:06:21.346  1968  1968 E CarrierIdProvider: read carrier list from ota pb failure: java.io.FileNotFoundException: /data/misc/carrierid/carrier_list.pb: open failed: ENOENT (No such file or directory)
05-24 16:06:21.353  1304  1625 W AudioCapabilities: Unsupported mime audio/x-adpcm-dvi-ima
05-24 16:06:21.354  1304  1625 W AudioCapabilities: Unsupported mime audio/x-adpcm-dvi-ima
05-24 16:06:21.355  1304  1625 W AudioCapabilities: Unsupported mime audio/x-adpcm-ms
05-24 16:06:21.355  1304  1625 W AudioCapabilities: Unsupported mime audio/x-adpcm-ms
05-24 16:06:21.355  1304  1625 W AudioCapabilities: Unsupported mime audio/alac
05-24 16:06:21.355  1304  1625 W AudioCapabilities: Unsupported mime audio/alac
05-24 16:06:21.355  1304  1625 W AudioCapabilities: Unsupported mime audio/ape
05-24 16:06:21.355  1304  1625 W AudioCapabilities: Unsupported mime audio/ape
05-24 16:06:21.359  1304  1304 I Telecom : CallAudioRouteController: calculateSupportedRouteMaskInit: is wired headset plugged in - false: TS.init@AAA
05-24 16:06:21.359  1304  1304 I Telecom : AudioRoute$Factory: createRetry; type=TYPE_SPEAKER, address=null, retryCount=2: TS.init@AAA
05-24 16:06:21.359  1781  1781 I SystemUIService: Found SurfaceFlinger's GPU Priority: 13143
05-24 16:06:21.359  1781  1781 I SystemUIService: Setting SysUI's GPU Context priority to: 12545
05-24 16:06:21.359  1304  1304 I Telecom : AudioRoute$Factory: type: 1: TS.init@AAA
05-24 16:06:21.360  1304  1304 I Telecom : AudioRoute$Factory: type: 2: TS.init@AAA
05-24 16:06:21.360  1304  1304 I Telecom : AudioRoute$Factory: createRetry; type=TYPE_EARPIECE, address=null, retryCount=2: TS.init@AAA
05-24 16:06:21.360  1304  1304 I Telecom : AudioRoute$Factory: type: 1: TS.init@AAA
05-24 16:06:21.379  1304  2038 I Telecom : CallAudioModeStateMachine: Audio focus entering UNFOCUSED state
05-24 16:06:21.380  1304  2038 I Telecom : CallAudioModeStateMachine: Message received: null.: TS.init->CAMSM.pM_1@AAA
05-24 16:06:21.384  1304  1304 I Telecom : MissedCallNotifierImpl: reloadFromDatabase: Boot not yet complete -- call log db may not be available. Deferring loading until boot complete for user 0: TS.init@AAA
05-24 16:06:21.390  1304  1304 I ContentSuggestionsManagerService: Updating for user 0: disabled=false
05-24 16:06:21.390  1304  1304 E ContentSuggestionsPerUserService: Bad service name: com.google.android.as/com.google.android.apps.miphone.aiai.app.AiAiContentSuggestionsService
05-24 16:06:21.391  1304  1304 I AutofillManagerService: Updating for user 0: disabled=false
05-24 16:06:21.391  1304  1304 E AutofillManagerServiceImpl: Bad service name: proton.android.pass/proton.android.pass.autofill.ProtonPassAutofillService
05-24 16:06:21.393  1304  1304 W ActivityManager: Unable to start service Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener } U=0: not found
05-24 16:06:21.394  1304  1304 W NotificationManagerService.NotificationListeners: Unable to bind notification listener service: Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener (has extras) } in user 0
05-24 16:06:21.410  1304  1346 I ActivityManager: Start proc 2051:com.android.permissioncontroller/u0a266 for broadcast {com.android.permissioncontroller/com.android.permissioncontroller.privacysources.SafetyCenterReceiver}
05-24 16:06:21.412  1304  1331 W PermissionService: getPermissionFlags: Unknown user -1
05-24 16:06:21.412  1304  1331 W PermissionService: getPermissionFlags: Unknown user -1
05-24 16:06:21.414  1304  1304 I ConnectivityModuleConnector: Networking module service connected
05-24 16:06:21.429  1304  1818 W WifiService: Attempt to retrieve WifiConfiguration with invalid scanResult List
05-24 16:06:21.437  1304  1988 W WifiService: Attempt to retrieve WifiConfiguration with invalid scanResult List
05-24 16:06:21.438  1304  1988 E WifiService: Attempt to retrieve passpoint with invalid scanResult List
05-24 16:06:21.438  1304  1323 W WifiService: Attempt to retrieve OsuProviders with invalid scanResult List
05-24 16:06:21.472  1304  1346 I ActivityManager: Start proc 2080:com.android.launcher3/u0a230 for service {com.android.launcher3/com.android.quickstep.TouchInteractionService}
05-24 16:06:21.477  1968  1968 E RILJ    : Feature android.hardware.telephony.ims is declared, but service IMS is missing [PHONE0]
05-24 16:06:21.478  1968  1968 E RILJ    : Feature android.hardware.telephony.messaging is declared, but service MESSAGING is missing [PHONE0]
05-24 16:06:21.478  1968  1968 E RILJ    : Feature android.hardware.telephony.data is declared, but service DATA is missing [PHONE0]
05-24 16:06:21.478  1968  1968 E RILJ    : Feature android.hardware.telephony.calling is declared, but service VOICE is missing [PHONE0]
05-24 16:06:21.504  1781  2071 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 16:06:21.549   905  2099 W gpuservice: AIBinder_linkToDeath is being called with a non-null cookie and no onUnlink callback set. This might not be intended. AIBinder_DeathRecipient_setOnUnlinked should be called first.
05-24 16:06:21.554  1401  1406 E RILC-OplusAppRadio: oplusAppRadioService: oplusAppRadioService[0]->mRadioIndicationOplus == NULL
05-24 16:06:21.554  1401  1406 E RILC-OplusRadioAIDL: oplusRadioServiceAIDL: oplusRadioServiceAIDL[0]->mRadioIndicationOplus == NULL
05-24 16:06:21.555  1968  1968 E RILJ    : Feature android.hardware.telephony.ims is declared, but service IMS is missing [PHONE1]
05-24 16:06:21.557  1968  1968 E RILJ    : Feature android.hardware.telephony.messaging is declared, but service MESSAGING is missing [PHONE1]
05-24 16:06:21.558  1968  1968 E RILJ    : Feature android.hardware.telephony.data is declared, but service DATA is missing [PHONE1]
05-24 16:06:21.560  1968  1968 E RILJ    : Feature android.hardware.telephony.calling is declared, but service VOICE is missing [PHONE1]
05-24 16:06:21.567  2080  2080 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-24 16:06:21.570  1781  2071 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 16:06:21.572  1986  1986 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 16:06:21.595  2080  2080 I QuickstepProtoLogGroup: Initializing ProtoLog.
05-24 16:06:21.626  1304  1334 W KeyguardServiceDelegate: onScreenTurningOn(): no keyguard service!
05-24 16:06:21.655  1968  1968 E SatelliteController: SatelliteController was not yet initialized.
05-24 16:06:21.661  2080  2153 E FileLog : java.io.FileNotFoundException: /data/user/0/com.android.launcher3/files/log-0: open failed: ENOENT (No such file or directory)
05-24 16:06:21.661  2080  2153 E FileLog : 	at java.io.FileOutputStream.<init>(FileOutputStream.java:259)
05-24 16:06:21.661  2080  2153 E FileLog : 	at java.io.FileWriter.<init>(FileWriter.java:113)
05-24 16:06:21.661  2080  2153 E FileLog : Caused by: android.system.ErrnoException: open failed: ENOENT (No such file or directory)
05-24 16:06:21.669  1304  1346 I ActivityManager: Start proc 2155:com.android.smspush/u0a237 for service {com.android.smspush/com.android.smspush.WapPushManager}
05-24 16:06:21.683  1781  1781 E SharedConnectivityManager: To support shared connectivity service on this device, the service's package name and intent action strings must not be empty
05-24 16:06:21.697   574   574 I hwservicemanager: Notifying android.hardware.wifi@1.5::IWifi/default they have clients: 1
05-24 16:06:21.767  1968  1968 E EmergencyNumberTracker: [0]Cache ota emergency database IOException: java.io.FileNotFoundException: /data/misc/emergencynumberdb/emergency_number_db: open failed: ENOENT (No such file or directory)
05-24 16:06:21.792  1304  1324 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-24 16:06:21.794  1304  1324 I DefaultPermGrantPolicy: Granting permissions to enabled data services for user:0
05-24 16:06:21.795  2155  2155 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-24 16:06:21.800  1304  2064 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-24 16:06:21.833  1304  2025 W Telecom : BluetoothDeviceManager: getBluetoothHeadset: Acquire BluetoothHeadset service failed due to: java.util.concurrent.TimeoutException
05-24 16:06:21.833  1304  2025 I Telecom : BluetoothRouteManager: getBluetoothAudioConnectedDevice: no service available.
05-24 16:06:21.834  1304  2032 I Telecom : CallAudioRouteController: Message received: BT_AUDIO_DISCONNECTED=1301, arg1=0
05-24 16:06:21.839  2165  2195 E CellBroadcastConfigService: fails to setCellBroadcastRanges
05-24 16:06:21.850  2165  2195 E CellBroadcastConfigService: fails to setCellBroadcastRanges
05-24 16:06:21.867  1968  1968 E SatelliteController: SatelliteController was not yet initialized.
05-24 16:06:21.883  1304  1892 I MR2ServiceImpl: registerManager | callerUid: 10226, callerPid: 1781, callerPackage: com.android.systemui, targetPackageName: null, targetUserId: UserHandle{0}, hasMediaRoutingControl: false
05-24 16:06:21.887  1304  1734 I MR2ServiceImpl: addProviderRoutes | provider: com.android.server.media/.SystemMediaRoute2Provider, routes: [ROUTE_ID_BUILTIN_SPEAKER | Phone]
05-24 16:06:21.892  1401  1406 E RILC-OplusAppRadio: oplusAppRadioService: oplusAppRadioService[0]->mRadioIndicationOplus == NULL
05-24 16:06:21.892  1401  1406 E RILC-OplusRadioAIDL: oplusRadioServiceAIDL: oplusRadioServiceAIDL[0]->mRadioIndicationOplus == NULL
05-24 16:06:21.897  1304  1734 I AS.AudioService: removePreferredDevicesForStrategy strat:5
05-24 16:06:21.918  1968  1968 E EmergencyNumberTracker: [1]Cache ota emergency database IOException: java.io.FileNotFoundException: /data/misc/emergencynumberdb/emergency_number_db: open failed: ENOENT (No such file or directory)
05-24 16:06:21.930  1304  1988 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-24 16:06:21.930  1304  1988 I DefaultPermGrantPolicy: Granting permissions to enabled data services for user:0
05-24 16:06:21.950  1304  1988 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-24 16:06:22.083  1968  2213 I ImsResolver: Initializing cache.
05-24 16:06:22.087  1781  2211 I Codec2Client: Available Codec2 services: "default" "software"
05-24 16:06:22.094  1968  1968 E SatelliteModemInterface: Unable to bind to the satellite service because the package is undefined.
05-24 16:06:22.140  1304  2064 W Binder  : java.lang.SecurityException: Need REGISTER_STATS_PULL_ATOM permission.: Neither user 10226 nor current process has android.permission.REGISTER_STATS_PULL_ATOM.
05-24 16:06:22.140  1304  2064 W Binder  : 	at android.app.ContextImpl.enforceCallingOrSelfPermission(ContextImpl.java:2630)
05-24 16:06:22.140  1304  2064 W Binder  : 	at com.android.server.stats.StatsManagerService.enforceRegisterStatsPullAtomPermission(StatsManagerService.java:678)
05-24 16:06:22.140  1304  2064 W Binder  : 	at com.android.server.stats.StatsManagerService.registerPullAtomCallback(StatsManagerService.java:219)
05-24 16:06:22.140  1304  2064 W Binder  : 	at android.os.IStatsManagerService$Stub.onTransact(IStatsManagerService.java:434)
05-24 16:06:22.151  1304  1892 I StatusBarManagerService: registerStatusBar bar=com.android.internal.statusbar.IStatusBar$Stub$Proxy@45ce88
05-24 16:06:22.172  1781  1781 I KeyguardSecurityView: Switching mode from Uninitialized to Default
05-24 16:06:22.182  1781  1781 E OverviewProxyService: Failed to get overview proxy for disable flags.
05-24 16:06:22.197  1781  1781 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 16:06:22.215  1781  1781 E OverviewProxyService: Failed to get overview proxy for disable flags.
05-24 16:06:22.226  1968  1968 I TelephonyRcsService: updateFeatureControllers: oldSlots=0, newNumSlots=2
05-24 16:06:22.238  1781  1781 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 16:06:22.252  1781  1781 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 16:06:22.279  1941  1941 W HidlServiceManagement: Waited one second for android.hardware.secure_element@1.2::ISecureElement/SIM1
05-24 16:06:22.279   574   574 I hwservicemanager: Since android.hardware.secure_element@1.2::ISecureElement/SIM1 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 16:06:22.280  1941  1941 I HidlServiceManagement: getService: Trying again for android.hardware.secure_element@1.2::ISecureElement/SIM1...
05-24 16:06:22.281  1304  1304 I AS.AudioService: onSubscriptionsChanged()
05-24 16:06:22.282   574  2236 I hwservicemanager: Tried to start android.hardware.secure_element@1.2::ISecureElement/SIM1 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 16:06:22.286  1304  2064 W UserManagerService: Requested status bar icon for non-badged user 0
05-24 16:06:22.287  1304  1650 I VcnManagementService: new snapshot: TelephonySubscriptionSnapshot{ mActiveDataSubId=2, mSubIdToInfoMap={}, mSubIdToCarrierConfigMap={}, mPrivilegedPackages={} }
05-24 16:06:22.300  1304  1304 I AS.AudioService: onSubscriptionsChanged()
05-24 16:06:22.309  1968  1968 E RILJ    : getImei not supported on service MODEM < 2.1. [PHONE0]
05-24 16:06:22.314  1968  1968 E RILJ    : setNullCipherAndIntegrityEnabled not supported on service NETWORK < 2.1. [PHONE0]
05-24 16:06:22.317  1968  1968 E RILJ    : setCellularIdentifierTransparencyEnabled not supported on service NETWORK < 2.2. [PHONE0]
05-24 16:06:22.317  1968  1968 E RILJ    : setSecurityAlgorithmsUpdatedEnabled not supported on service NETWORK < 2.2. [PHONE0]
05-24 16:06:22.322  1304  1344 I StatsPullAtomService: subId 2 added into historical sub list
05-24 16:06:22.327  1968  1968 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-24 16:06:22.335  1781  1781 I SystemUIService: Topological CoreStartables completed in 2 iterations
05-24 16:06:22.335  1781  1781 E OverviewProxyService: Failed to get overview proxy for disable flags.
05-24 16:06:22.338  1968  1968 E NRM-C-0 : service not connected. Domain = PS
05-24 16:06:22.338  1968  1968 E NRM-C-0 : service not connected. Domain = CS
05-24 16:06:22.338  1968  1968 E NRM-I-0 : service not connected. Domain = PS
05-24 16:06:22.346  1968  1968 E NRM-C-0 : service not connected. Domain = PS
05-24 16:06:22.346  1968  1968 E NRM-C-0 : service not connected. Domain = CS
05-24 16:06:22.346  1968  1968 E NRM-I-0 : service not connected. Domain = PS
05-24 16:06:22.351  1781  1781 W SimLog  : invalid subId in handleServiceStateChange()
05-24 16:06:22.354  1304  1344 I StatsPullAtomService: subId 1 added into historical sub list
05-24 16:06:22.366  1304  1324 W UserManagerService: Requested status bar icon for non-badged user 0
05-24 16:06:22.378  1968  1968 E RILJ    : getImei not supported on service MODEM < 2.1. [PHONE1]
05-24 16:06:22.381  1968  1968 E RILJ    : setNullCipherAndIntegrityEnabled not supported on service NETWORK < 2.1. [PHONE1]
05-24 16:06:22.381  1223  1223 E bootanimation: === MALI DEBUG ===eglp_check_display_valid_and_initialized_and_retain retun EGL_NOT_INITIALIZED
05-24 16:06:22.381  1968  1968 E RILJ    : setCellularIdentifierTransparencyEnabled not supported on service NETWORK < 2.2. [PHONE1]
05-24 16:06:22.382  1968  1968 E RILJ    : setSecurityAlgorithmsUpdatedEnabled not supported on service NETWORK < 2.2. [PHONE1]
05-24 16:06:22.388  1968  1968 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-24 16:06:22.393  1968  1968 E NRM-C-1 : service not connected. Domain = PS
05-24 16:06:22.393  1968  1968 E NRM-C-1 : service not connected. Domain = CS
05-24 16:06:22.393  1968  1968 E NRM-I-1 : service not connected. Domain = PS
05-24 16:06:22.396  1968  1968 E NRM-C-1 : service not connected. Domain = PS
05-24 16:06:22.396  1968  1968 E NRM-C-1 : service not connected. Domain = CS
05-24 16:06:22.396  1968  1968 E NRM-I-1 : service not connected. Domain = PS
05-24 16:06:22.491  1968  1968 I Telephony: TelecomAccountRegistry$AccountEntry: registerPstnPhoneAccount: Registering account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with Telecom. subId=-1
05-24 16:06:22.493  1304  1323 I Telecom : TelecomServiceImpl$1: registerPhoneAccount: account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@AAs
05-24 16:06:22.494  1304  1323 I Telecom : PhoneAccountRegistrar: New phone account registered: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@AAs
05-24 16:06:22.497  1304  1323 I Telecom : CallsManager: Sending phone-account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} registered intent as user: TSI.rPA(cap)@AAs
05-24 16:06:22.500  1304  1323 I Telecom : PhoneAccountRegistrar: Notifying telephony of voice service override change for 1 SIMs, hasService = false: TSI.rPA(cap)@AAs
05-24 16:06:22.500  1304  1323 I Telecom : PhoneAccountRegistrar: maybeNotifyTelephonyForVoiceServiceState: simTm is null.: TSI.rPA(cap)@AAs
05-24 16:06:22.500  1968  1968 I Telephony: TelecomAccountRegistry$AccountEntry: Registered phoneAccount: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with handle: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}
05-24 16:06:22.501  1968  1968 I Telephony: TelecomAccountRegistry: Unregistering phone account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 2, UserHandle{0}.
05-24 16:06:22.503  1304  1323 I Telecom : CallsManager: Sending phone-account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 2, UserHandle{0} unregistered intent as user: TSI.uPA(cap)@AA0
05-24 16:06:22.504  1968  1968 I Telephony: TelecomAccountRegistry: Unregistering phone account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 1, UserHandle{0}.
05-24 16:06:22.505  1304  2078 I Telecom : CallsManager: Sending phone-account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 1, UserHandle{0} unregistered intent as user: TSI.uPA(cap)@AA4
05-24 16:06:22.516  1304  1304 I AS.AudioService: onSubscriptionsChanged()
05-24 16:06:22.544  1304  1334 I SystemServiceManager: Starting phase 1000
05-24 16:06:22.545  1304  1334 E PowerStatsService: Failed to start PowerStatsService loggers
05-24 16:06:22.545   915   915 I PowerHalLoader: Successfully connected to Power HAL AIDL service.
05-24 16:06:22.550  1304  1334 I TransparencyService: Boot completed. Getting boot integrity data.
05-24 16:06:22.550  1304  1334 I TransparencyService: Boot completed. Collecting biometric system properties.
05-24 16:06:22.551  1304  1334 I TransparencyService: Scheduling measurements to be taken.
05-24 16:06:22.551  1304  1334 I TransparencyService: Scheduling binary content-digest computation job
05-24 16:06:22.564  1304  1625 I StorageSessionController: Started resetting external storage service...
05-24 16:06:22.564  1304  1625 I StorageSessionController: Finished resetting external storage service
05-24 16:06:22.564  1304  1625 I StorageManagerService: Resetting vold...
05-24 16:06:22.565  1304  1625 I StorageManagerService: Reset vold
