05-24 19:06:25.115   578   578 I auditd  : SELinux: Loaded service context from:
05-24 19:06:25.115   578   578 I auditd  : 		/system/etc/selinux/plat_service_contexts
05-24 19:06:25.115   578   578 I auditd  : 		/system_ext/etc/selinux/system_ext_service_contexts
05-24 19:06:25.115   578   578 I auditd  : 		/product/etc/selinux/product_service_contexts
05-24 19:06:25.115   578   578 I auditd  : 		/vendor/etc/selinux/vendor_service_contexts
05-24 19:06:25.115   578   578 I auditd  : 		/odm/etc/selinux/odm_service_contexts
05-24 19:06:24.200     1     1 I auditd  : type=1107 audit(0.0:4): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_cam_stereo_camera_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-24 19:06:24.200     1     1 W /system/bin/init: type=1107 audit(0.0:4): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_cam_stereo_camera_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-24 19:06:24.200     1     1 I auditd  : type=1107 audit(0.0:5): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_cam_dualzoom_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-24 19:06:24.200     1     1 W /system/bin/init: type=1107 audit(0.0:5): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_cam_dualzoom_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-24 19:06:24.200     1     1 I auditd  : type=1107 audit(0.0:6): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_key_manager_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-24 19:06:24.200     1     1 W /system/bin/init: type=1107 audit(0.0:6): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_key_manager_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-24 19:06:24.204     1     1 I auditd  : type=1107 audit(0.0:7): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.build.fingerprint pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:fingerprint_prop:s0 tclass=property_service permissive=0'
05-24 19:06:24.204     1     1 W /system/bin/init: type=1107 audit(0.0:7): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.build.fingerprint pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:fingerprint_prop:s0 tclass=property_service permissive=0'
05-24 19:06:25.118   580   580 I hwservicemanager: getFrameworkHalManifest: Reading VINTF information.
05-24 19:06:25.124   580   580 I hwservicemanager: Loaded APEX Infos from /bootstrap-apex/apex-info-list.xml
05-24 19:06:25.125   580   580 I hwservicemanager: getDeviceHalManifest: Reading VINTF information.
05-24 19:06:25.144   580   580 I hwservicemanager: Loaded APEX Infos from /bootstrap-apex/apex-info-list.xml
05-24 19:06:25.147   580   580 I hwservicemanager: getDeviceHalManifest: Successfully processed VINTF information
05-24 19:06:25.147   580   580 I hwservicemanager: getFrameworkHalManifest: Successfully processed VINTF information
05-24 19:06:25.152   580   580 I hwservicemanager: hwservicemanager is ready now.
05-24 19:06:25.283     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvdata" to "5": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-24 19:06:25.290     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvdata" to "6": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-24 19:06:25.301     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvcfg" to "9": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-24 19:06:25.309     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvcfg" to "8": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-24 19:06:25.452   633   633 I HidlServiceManagement: Registered android.system.suspend@1.0::ISystemSuspend/default
05-24 19:06:25.452   633   633 I HidlServiceManagement: Removing namespace from process name android.system.suspend-service to suspend-service.
05-24 19:06:25.463   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.boot@1.0::IBootControl/default in either framework or device VINTF manifest.
05-24 19:06:25.466   635   635 I <EMAIL>: Trustonic Keymaster 4.1 Service starts
05-24 19:06:25.475   635   635 I HidlServiceManagement: Registered android.hardware.keymaster@4.1::IKeymasterDevice/default
05-24 19:06:25.475   635   635 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 19:06:25.475   635   635 I <EMAIL>: Trustonic Keymaster 4.1 Service registered
05-24 19:06:25.499   634   634 I keystore2: system/security/keystore2/src/keystore2_main.rs:154 - Successfully registered Keystore 2.0 service.
05-24 19:06:25.764   578   642 W libc    : Unable to set property "ctl.interface_start" to "aidl/android.system.keystore2.IKeystoreService/default": PROP_ERROR_HANDLE_CONTROL_MESSAGE (0x20)
05-24 19:06:25.791   653   653 I libperfmgr: Pixel Power HAL AIDL Service with Extension is starting with config: /vendor/etc/powerhint.json
05-24 19:06:25.793   653   653 I libperfmgr: Failed to read Node[18]'s ResetOnInit, set to 'false'
05-24 19:06:25.793   653   653 I libperfmgr: Failed to read Node[19]'s ResetOnInit, set to 'false'
05-24 19:06:25.793   653   653 I libperfmgr: Failed to read Node[20]'s ResetOnInit, set to 'false'
05-24 19:06:25.793   653   653 I libperfmgr: Failed to read Node[21]'s ResetOnInit, set to 'false'
05-24 19:06:25.795   653   653 I libperfmgr: PowerHint AUDIO_STREAMING_LOW_LATENCY has 3 node actions, and 0 hint actions parsed
05-24 19:06:25.795   653   653 I libperfmgr: Initialized HintManager from JSON config: /vendor/etc/powerhint.json
05-24 19:06:25.796   653   653 I powerhal-libperfmgr: Initialize PowerHAL
05-24 19:06:25.798   653   653 I powerhal-libperfmgr: Pixel Power HAL AIDL Service with Extension is started.
05-24 19:06:25.874   591   591 I vold    : fscrypt_initialize_systemwide_keys
05-24 19:06:25.938   663   663 I tombstoned: tombstoned successfully initialized
05-24 19:06:25.949   591   591 I incfs   : Initial API level of the device: 30
05-24 19:06:25.971   634   648 E keystore2:     1: system/security/keystore2/src/globals.rs:264: Trying to get Legacy wrapper. Attempt to get keystore compat service for security level r#STRONGBOX
05-24 19:06:25.971   591   591 E vold    : keystore2 Keystore earlyBootEnded returned service specific error: -68
05-24 19:06:26.525   803   803 I derive_sdk: extension ad_services version is 15
05-24 19:06:26.656   807   807 I derive_classpath: ReadClasspathFragment /apex/com.android.adservices/etc/classpaths/bootclasspath.pb
05-24 19:06:26.657   807   807 I derive_classpath: ReadClasspathFragment /apex/com.android.btservices/etc/classpaths/bootclasspath.pb
05-24 19:06:26.659   807   807 I derive_classpath: ReadClasspathFragment /apex/com.android.nfcservices/etc/classpaths/bootclasspath.pb
05-24 19:06:26.660   807   807 I derive_classpath: ReadClasspathFragment /apex/com.android.permission/etc/classpaths/bootclasspath.pb
05-24 19:06:26.664   807   807 I derive_classpath: ReadClasspathFragment /apex/com.android.adservices/etc/classpaths/systemserverclasspath.pb
05-24 19:06:26.665   807   807 I derive_classpath: ReadClasspathFragment /apex/com.android.btservices/etc/classpaths/systemserverclasspath.pb
05-24 19:06:26.668   807   807 I derive_classpath: ReadClasspathFragment /apex/com.android.permission/etc/classpaths/systemserverclasspath.pb
05-24 19:06:26.670   807   807 I derive_classpath: export BOOTCLASSPATH /apex/com.android.art/javalib/core-oj.jar:/apex/com.android.art/javalib/core-libart.jar:/apex/com.android.art/javalib/okhttp.jar:/apex/com.android.art/javalib/bouncycastle.jar:/apex/com.android.art/javalib/apache-xml.jar:/system/framework/framework.jar:/system/framework/framework-graphics.jar:/system/framework/framework-location.jar:/system/framework/framework-connectivity-b.jar:/system/framework/ext.jar:/system/framework/telephony-common.jar:/system/framework/voip-common.jar:/system/framework/ims-common.jar:/system/framework/framework-platformcrashrecovery.jar:/system/framework/framework-ondeviceintelligence-platform.jar:/system/framework/mediatek-common.jar:/system/framework/mediatek-framework.jar:/system/framework/mediatek-ims-base.jar:/system/framework/mediatek-ims-common.jar:/system/framework/mediatek-telecom-common.jar:/system/framework/mediatek-telephony-base.jar:/system/framework/mediatek-telephony-common.jar:/apex/com.android.i18n/javalib/core-icu4j.jar:/apex/com.android.adservices/javalib/framework-adservices.jar:/apex/com.android.adservices/javalib/framework-sdksandbox.jar:/apex/com.android.appsearch/javalib/framework-appsearch.jar:/apex/com.android.btservices/javalib/framework-bluetooth.jar:/apex/com.android.configinfrastructure/javalib/framework-configinfrastructure.jar:/apex/com.android.conscrypt/javalib/conscrypt.jar:/apex/com.android.devicelock/javalib/framework-devicelock.jar:/apex/com.android.healthfitness/javalib/framework-healthfitness.jar:/apex/com.android.ipsec/javalib/android.net.ipsec.ike.jar:/apex/com.android.media/javalib/updatable-media.jar:/apex/com.android.mediaprovider/javalib/framework-mediaprovider.jar:/apex/com.android.mediaprovider/javalib/framework-pdf.jar:/apex/com.android.mediaprovider/javalib/framework-pdf-v.jar:/apex/com.android.mediaprovider/javalib/framework-photopicker.jar:/apex/com.android.nfcservices/javalib/framework-nfc.jar:/apex/com.android.ondevicepersonalization/javalib/framework-ondevicepersonalization.jar:/apex/com.android.os.statsd/javalib/framework-statsd.jar:/apex/com.android.permission/javalib/framework-permission.jar:/apex/com.android.permission/javalib/framework-permission-s.jar:/apex/com.android.profiling/javalib/framework-profiling.jar:/apex/com.android.scheduling/javalib/framework-scheduling.jar:/apex/com.android.sdkext/javalib/framework-sdkextensions.jar:/apex/com.android.tethering/javalib/framework-connectivity.jar:/apex/com.android.tethering/javalib/framework-connectivity-t.jar:/apex/com.android.tethering/javalib/framework-tethering.jar:/apex/com.android.uwb/javalib/framework-uwb.jar:/apex/com.android.virt/javalib/framework-virtualization.jar:/apex/com.android.wifi/javalib/framework-wifi.jar
05-24 19:06:26.670   807   807 I derive_classpath: export SYSTEMSERVERCLASSPATH /system/framework/com.android.location.provider.jar:/system/framework/services.jar:/apex/com.android.adservices/javalib/service-adservices.jar:/apex/com.android.adservices/javalib/service-sdksandbox.jar:/apex/com.android.appsearch/javalib/service-appsearch.jar:/
05-24 19:06:26.705   808   808 I art_boot: Property persist.device_config.runtime_native_boot.useartservice not set
05-24 19:06:26.732   809   809 W odsign  : Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.system.keystore2.IKeystoreService/default
05-24 19:06:26.742   809   809 I odsign  : Initialized Keystore key.
05-24 19:06:28.572   871   871 I netdClient: Skipping libnetd_client init since *we* are netd
05-24 19:06:28.576   578   578 I auditd  : SELinux: Loaded service context from:
05-24 19:06:28.576   578   578 I auditd  : 		/system/etc/selinux/plat_service_contexts
05-24 19:06:28.576   578   578 I auditd  : 		/system_ext/etc/selinux/system_ext_service_contexts
05-24 19:06:28.576   578   578 I auditd  : 		/product/etc/selinux/product_service_contexts
05-24 19:06:28.576   578   578 I auditd  : 		/vendor/etc/selinux/vendor_service_contexts
05-24 19:06:28.576   578   578 I auditd  : 		/odm/etc/selinux/odm_service_contexts
05-24 19:06:28.588   871   871 I NetdUpdatable: libnetd_updatable_init: Initializing
05-24 19:06:28.588   871   871 I NetdUpdatable: initMaps successfully
05-24 19:06:28.588   871   871 I netd    : libnetd_updatable_init success
05-24 19:06:28.592   881   881 E zygisk-core32: Retrying to connect to zygiskd, sleep 1s failed with 2: No such file or directory
05-24 19:06:28.609   580   580 I hwservicemanager: getFrameworkHalManifest: Reloading VINTF information.
05-24 19:06:28.609   580   580 I hwservicemanager: getFrameworkHalManifest: Reading VINTF information.
05-24 19:06:28.614   580   580 I hwservicemanager: Loaded APEX Infos from /apex/apex-info-list.xml
05-24 19:06:28.615   580   580 I hwservicemanager: getDeviceHalManifest: Reloading VINTF information.
05-24 19:06:28.615   580   580 I hwservicemanager: getDeviceHalManifest: Reading VINTF information.
05-24 19:06:28.633   580   580 I hwservicemanager: Loaded APEX Infos from /apex/apex-info-list.xml
05-24 19:06:28.634   580   580 I hwservicemanager: getDeviceHalManifest: Successfully processed VINTF information
05-24 19:06:28.634   580   580 I hwservicemanager: getFrameworkHalManifest: Successfully processed VINTF information
05-24 19:06:28.636   871   871 I netd    : Initializing RouteController: 1270us
05-24 19:06:28.638   890   890 I mtk.hal.bt@1.0-impl: Init IBluetoothHCI
05-24 19:06:28.653   894   894 I HidlServiceManagement: Registered android.hardware.cas@1.2::IMediaCasService/default
05-24 19:06:28.654   894   894 I HidlServiceManagement: Removing namespace from process name android.hardware.cas@1.2-service to cas@1.2-service.
05-24 19:06:28.657   888   888 I HidlServiceManagement: Registered android.hidl.allocator@1.0::IAllocator/ashmem
05-24 19:06:28.657   888   888 I HidlServiceManagement: Removing namespace from process name android.hidl.allocator@1.0-service to allocator@1.0-service.
05-24 19:06:28.658   890   890 I HidlServiceManagement: Registered android.hardware.bluetooth@1.1::IBluetoothHci/default
05-24 19:06:28.659   890   890 I HidlServiceManagement: Removing namespace from process name android.hardware.bluetooth@1.1-service-mediatek to bluetooth@1.1-service-mediatek.
05-24 19:06:28.661   897   897 I HidlServiceManagement: Registered android.hardware.drm@1.4::IDrmFactory/widevine
05-24 19:06:28.662   897   897 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 19:06:28.663   911   911 I vtservice_hidl: [VT][SRV]before VTService_HiDL_instantiate
05-24 19:06:28.663   902   902 I android.hardware.health@2.1-service: default instance initializing with healthd_config...
05-24 19:06:28.666   902   902 I HidlServiceManagement: Registered android.hardware.health@2.1::IHealth/default
05-24 19:06:28.666   902   902 I HidlServiceManagement: Removing namespace from process name android.hardware.health@2.1-service to health@2.1-service.
05-24 19:06:28.666   897   897 I HidlServiceManagement: Registered android.hardware.drm@1.4::ICryptoFactory/widevine
05-24 19:06:28.666   897   897 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 19:06:28.666   902   902 I android.hardware.health@2.1-service: default: Hal init done
05-24 19:06:28.672   899   899 E android.hardware.gnss-service.mediatek: safe_sendto: safe_sendto() sendto() failed path=[mtk_hal2mnl] ret=-1 reason=[Connection refused]111
05-24 19:06:28.672   899   899 E android.hardware.gnss-service.mediatek: gps_device__get_gps_interface: hal2mnl_hal_reboot failed because of safe_sendto fail ,strerror:Connection refused 
05-24 19:06:28.672   911   911 I HidlServiceManagement: Registered vendor.mediatek.hardware.videotelephony@1.0::IVideoTelephony/default
05-24 19:06:28.673   915   915 W <EMAIL>: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.hardware.power.IPower/default
05-24 19:06:28.673   915   915 I <EMAIL>: Connected to power AIDL HAL
05-24 19:06:28.675   871   871 I netd    : Initializing XfrmController: 38987us
05-24 19:06:28.675   915   915 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkpower@1.2::IMtkPower/default
05-24 19:06:28.678   909   909 W <EMAIL>: ThermalHelper:tz_map_version 1
05-24 19:06:28.678   909   909 I <EMAIL>: ThermalWatcherThread started
05-24 19:06:28.678   899   899 E android.hardware.gnss-service.mediatek: safe_sendto: safe_sendto() sendto() failed path=[mtk_hal2mnl] ret=-1 reason=[Connection refused]111
05-24 19:06:28.678   899   899 E android.hardware.gnss-service.mediatek: gps_device__get_gps_interface: hal2mnl_hal_reboot failed because of safe_sendto fail ,strerror:Connection refused 
05-24 19:06:28.679   871   871 I resolv  : resolv_init: Initializing resolver
05-24 19:06:28.679   915   915 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 19:06:28.681   899   899 I HidlServiceManagement: Registered android.hardware.gnss@2.1::IGnss/default
05-24 19:06:28.681   899   899 I HidlServiceManagement: Removing namespace from process name android.hardware.gnss-service.mediatek to gnss-service.mediatek.
05-24 19:06:28.682   915   915 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkpower@1.2::IMtkPerf/default
05-24 19:06:28.683   915   915 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 19:06:28.686   909   909 I HidlServiceManagement: Registered android.hardware.thermal@2.0::IThermal/default
05-24 19:06:28.686   927   927 E vendor.oplus.hardware.charger@1.0-service: notifyScreenStatus: 0
05-24 19:06:28.686   909   909 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 19:06:28.688   927   927 E vendor.oplus.hardware.charger@1.0-service: setChgStatusToBcc: 0
05-24 19:06:28.691   926   926 I HidlServiceManagement: Registered vendor.oplus.hardware.cammidasservice@1.0::IMIDASService/default
05-24 19:06:28.691   926   926 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.cammidasservice@1.0-service to cammidasservice@1.0-service.
05-24 19:06:28.692   926   926 I vendor.oplus.hardware.cammidasservice@1.0-service: midasservice register successfully
05-24 19:06:28.692   871   871 I netd    : Registering NetdNativeService: 309us
05-24 19:06:28.692   871   871 I netd    : Registering MDnsService: 249us
05-24 19:06:28.693   918   918 I HidlServiceManagement: Registered vendor.mediatek.hardware.nvram@1.1::INvram/default
05-24 19:06:28.694   918   918 I HidlServiceManagement: Removing namespace from process name vendor.mediatek.hardware.nvram@1.1-service to nvram@1.1-service.
05-24 19:06:28.695   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_agps
05-24 19:06:28.696   871   871 I HidlServiceManagement: Registered android.system.net.netd@1.1::INetd/default
05-24 19:06:28.696   871   871 I netd    : Registering NetdHwService: 4081us
05-24 19:06:28.698   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_wlan
05-24 19:06:28.699   927   927 I HidlServiceManagement: Registered vendor.oplus.hardware.charger@1.0::ICharger/default
05-24 19:06:28.699   927   927 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.charger@1.0-service to charger@1.0-service.
05-24 19:06:28.701   927   927 E vendor.oplus.hardware.charger@1.0-service: ERR:Failed to get bms heating config file
05-24 19:06:28.701   927   927 E vendor.oplus.hardware.charger@1.0-service: can't parse config, rc=-1
05-24 19:06:28.702   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_bt
05-24 19:06:28.703   910   910 I android.hardware.usb@1.3-service-mediatekv2: UsbGadget
05-24 19:06:28.703   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_sensor
05-24 19:06:28.704   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_network
05-24 19:06:28.705   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_ipaddr
05-24 19:06:28.706   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_lbs
05-24 19:06:28.707   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_framework2agps
05-24 19:06:28.708   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_agps2framework
05-24 19:06:28.709   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mnld2nlputils
05-24 19:06:28.710   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mnld2debugService
05-24 19:06:28.711   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_debugService2mnld
05-24 19:06:28.713   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_meta2mnld
05-24 19:06:28.715   932   932 W MTK_FG_FUEL: fd < 0, init first!
05-24 19:06:28.715   932   932 W MTK_FG_FUEL: init failed, return!
05-24 19:06:28.715   932   932 W MTK_FG  : fd < 0, init first!
05-24 19:06:28.715   932   932 E MTK_FG  : init failed, return!
05-24 19:06:28.715   932   932 W MTK_FG  : fd < 0, init first!
05-24 19:06:28.715   931   931 I HidlServiceManagement: Registered vendor.oplus.hardware.performance@1.0::IPerformance/default
05-24 19:06:28.715   932   932 E MTK_FG  : init failed, return!
05-24 19:06:28.715   932   932 W MTK_FG  : fd < 0, init first!
05-24 19:06:28.715   932   932 E MTK_FG  : init failed, return!
05-24 19:06:28.716   931   931 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.performance@1.0-service to performance@1.0-service.
05-24 19:06:28.717   910   910 I HidlServiceManagement: Registered android.hardware.usb@1.3::IUsb/default
05-24 19:06:28.718   910   910 I HidlServiceManagement: Removing namespace from process name android.hardware.usb@1.3-service-mediatekv2 to usb@1.3-service-mediatekv2.
05-24 19:06:28.720   901   901 W hwcomposer: [DRMDEV] 0xcccccccc[64] property[MSYNC2_0_ENABLE] does not do initialize  
05-24 19:06:28.720   901   901 W hwcomposer: [DRMDEV] 0xcccccccc[64] property[SKIP_CONFIG] does not do initialize  
05-24 19:06:28.720   901   901 W hwcomposer: [DRMDEV] failed to initialize crtc[0]: 64  
05-24 19:06:28.720   901   901 W hwcomposer: [DRMDEV] 0xcccccccc[88] property[MSYNC2_0_ENABLE] does not do initialize  
05-24 19:06:28.720   901   901 W hwcomposer: [DRMDEV] 0xcccccccc[88] property[SKIP_CONFIG] does not do initialize  
05-24 19:06:28.720   901   901 W hwcomposer: [DRMDEV] failed to initialize crtc[1]: 88  
05-24 19:06:28.720   901   901 W hwcomposer: [DRMDEV] 0xcccccccc[99] property[MSYNC2_0_ENABLE] does not do initialize  
05-24 19:06:28.720   901   901 W hwcomposer: [DRMDEV] 0xcccccccc[99] property[SKIP_CONFIG] does not do initialize  
05-24 19:06:28.720   901   901 W hwcomposer: [DRMDEV] failed to initialize crtc[2]: 99  
05-24 19:06:28.720   901   901 W hwcomposer: [DRMDEV] failed to initialize all crtc: -19  
05-24 19:06:28.720   889   889 I HidlServiceManagement: Registered android.hardware.audio@7.1::IDevicesFactory/default
05-24 19:06:28.720   889   889 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-24 19:06:28.721   901   901 E hwcomposer: [DRMDEV] failed to initialize drm resource  
05-24 19:06:28.721   889   889 I LegacySupport: Registration complete for android.hardware.audio@7.1::IDevicesFactory/default.
05-24 19:06:28.723   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_agpsd2debugService
05-24 19:06:28.724   908   908 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/acc_cali.json) open failed: -2 (No such file or directory)
05-24 19:06:28.724   901   901 I hwcomposer: [PqXmlParser] [PQ_SERVICE] mDeviceVersion = AMS644VA04_MTK04_20615  
05-24 19:06:28.724   901   901 I hwcomposer: [PqXmlParser] [PQ_SERVICE] mDeviceManufacture = samsung1024  
05-24 19:06:28.725   901   901 I hwcomposer: [PqXmlParser] [PQ_SERVICE] prjName:20662  
05-24 19:06:28.725   901   901 I hwcomposer: [PqXmlParser] init: failed to open file: /vendor/etc/cust_pq.xml  
05-24 19:06:28.725   910   910 I HidlServiceManagement: Registered android.hardware.usb.gadget@1.1::IUsbGadget/default
05-24 19:06:28.725   910   910 I android.hardware.usb@1.3-service-mediatekv2: USB HAL Ready.
05-24 19:06:28.729   900   900 I HidlServiceManagement: Registered android.hardware.graphics.allocator@4.0::IAllocator/default
05-24 19:06:28.730   900   900 I HidlServiceManagement: Removing namespace from process name android.hardware.graphics.allocator@4.0-service-mediatek to allocator@4.0-service-mediatek.
05-24 19:06:28.730   908   908 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/gyro_cali.json) open failed: -2 (No such file or directory)
05-24 19:06:28.732   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_debugService2agpsd
05-24 19:06:28.732   580   580 I hwservicemanager: Since vendor.mediatek.hardware.pq@2.14::IPictureQuality/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 19:06:28.733   901   901 E hwcomposer: [IPqDevice] Can't get PQ service tried (0) times  
05-24 19:06:28.733   928   928 I HidlServiceManagement: Registered vendor.oplus.hardware.oplusSensor@1.0::ISensorFeature/default
05-24 19:06:28.733   928   928 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.oplusSensor@1.0-service to oplusSensor@1.0-service.
05-24 19:06:28.736   924   924 I HidlServiceManagement: Registered vendor.trustonic.tee@1.1::ITee/default
05-24 19:06:28.736   924   924 I HidlServiceManagement: Removing namespace from process name vendor.trustonic.tee@1.1-service to tee@1.1-service.
05-24 19:06:28.737   908   908 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/als_cali.json) open failed: -2 (No such file or directory)
05-24 19:06:28.737   908   908 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/ps_cali.json) open failed: -2 (No such file or directory)
05-24 19:06:28.737   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/AgpsInterface
05-24 19:06:28.737   908   908 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/sar_cali.json) open failed: -2 (No such file or directory)
05-24 19:06:28.737   908   908 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/ois_cali.json) open failed: -2 (No such file or directory)
05-24 19:06:28.737   951   951 I credstore: Registered binder service
05-24 19:06:28.739   901   901 I HidlServiceManagement: Registered vendor.mediatek.hardware.composer_ext@1.0::IComposerExt/default
05-24 19:06:28.739   901   901 I hwcomposer: [HWC] IComposerExt service registration completed.  
05-24 19:06:28.740   924   924 I HidlServiceManagement: Registered vendor.trustonic.tee.tui@1.0::ITui/default
05-24 19:06:28.740   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/AgpsDebugInterface
05-24 19:06:28.741   901   901 I HidlServiceManagement: Registered android.hardware.graphics.composer@2.3::IComposer/default
05-24 19:06:28.742   901   901 I HidlServiceManagement: Removing namespace from process name android.hardware.graphics.composer@2.3-service to composer@2.3-service.
05-24 19:06:28.742   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mnld2mtklogger
05-24 19:06:28.743   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mtklogger2mnld
05-24 19:06:28.744   963   963 E ccci_mdinit: (1):main, fail to open ccci_dump, err(Permission denied)
05-24 19:06:28.745   908   908 I HidlServiceManagement: Registered android.hardware.sensors@2.0::ISensors/default
05-24 19:06:28.745   908   908 I HidlServiceManagement: Removing namespace from <NAME_EMAIL>6893 to sensors@2.0-service-multihal.mt6893.
05-24 19:06:28.745   966   966 E ccci_mdinit: (3):main, fail to open ccci_dump, err(Permission denied)
05-24 19:06:28.746   912   912 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lbs_log_v2s
05-24 19:06:28.747   963   963 I ccci_mdinit: (1):[main] drv_ver: 2
05-24 19:06:28.747   963   963 I ccci_mdinit: (1):[main] ccci_create_md_status_listen_thread
05-24 19:06:28.747   963   963 I ccci_mdinit: (1):md_init ver:2.30, sub:0, 1
05-24 19:06:28.748   963   963 I NVRAM   : MD1 set status: vendor.mtk.md1.status=init 
05-24 19:06:28.749   963   963 I ccci_mdinit: (1):MD0 set status: mtk.md0.status=init 
05-24 19:06:28.749   963   963 I NVRAM   : MD0 set status: mtk.md0.status=init 
05-24 19:06:28.749   963   963 E ccci_mdinit: (1):get property fail: ro.vendor.mtk_mipc_support
05-24 19:06:28.749   963   963 I ccci_mdinit: (1):service names: [init.svc.vendor.gsm0710muxd][init.svc.vendor.ril-daemon-mtk][init.svc.emdlogger1][init.svc.vendor.ccci_fsd]
05-24 19:06:28.749   963   963 I ccci_mdinit: (1):md_img_exist 0 0 0 0
05-24 19:06:28.751   963   963 I ccci_mdinit: (1):MD1 set status: mtk.md1.status=reset 
05-24 19:06:28.751   963   963 E ccci_mdinit: (1):[get_mdini_killed_state] error: get mdinit killed: 25(-1)
05-24 19:06:28.751   580   974 I hwservicemanager: Tried to start vendor.mediatek.hardware.pq@2.14::IPictureQuality/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 19:06:28.751   963   963 I ccci_mdinit: (1):MD_LOG_MEMDUMP_WAIT:ro.vendor.md_log_memdump_wait not exist, using default value
05-24 19:06:28.751   963   963 I ccci_mdinit: (1):MD_LOG_MEMDUMP_WAIT value: 0
05-24 19:06:28.751   963   963 I ccci_mdinit: (1):md0: mdl_mode=0
05-24 19:06:28.751   963   963 I ccci_mdinit: (1):check_nvram_ready(), property_get("vendor.service.nvram_init") = , read_nvram_ready_retry = 1
05-24 19:06:28.752   889   889 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-24 19:06:28.753   889   889 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-24 19:06:28.757   980   980 I TeeMcDaemon: Initialise Secure World [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/MobiCoreDriverDaemon.cpp:260]
05-24 19:06:28.757   980   980 W TeeMcDaemon: Cannot open key SO  (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:445]
05-24 19:06:28.757   980   980 I TeeMcDaemon: Start services [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/MobiCoreDriverDaemon.cpp:325]
05-24 19:06:28.765   980   996 W TeeMcDaemon: Cannot open trustlet /data/vendor/mcRegistry/07050501000000000000000000000020.tlbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.771   653   655 W powerhal-libperfmgr: Connecting to PPS daemon failed (No such file or directory)
05-24 19:06:28.773   898   898 I HidlServiceManagement: Registered android.hardware.gatekeeper@1.0::IGatekeeper/default
05-24 19:06:28.773   898   898 I HidlServiceManagement: Removing namespace from process name android.hardware.gatekeeper@1.0-service to gatekeeper@1.0-service.
05-24 19:06:28.779   997   997 I gsid    : no DSU: No such file or directory
05-24 19:06:28.780   980   996 W TeeMcDaemon: Cannot open trustlet /data/vendor/mcRegistry/08050500000000000000000000000000.tlbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.792   980   996 W TeeMcDaemon: Cannot open trustlet /data/vendor/mcRegistry/08050500000000000000000000000000.tlbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.833   889   889 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 19:06:28.837   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: failed to get path of fd 3: No such file or directory
05-24 19:06:28.837   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(15): previous definition here
05-24 19:06:28.837   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(38): previous definition here
05-24 19:06:28.838   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(69): previous definition here
05-24 19:06:28.838   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(70): previous definition here
05-24 19:06:28.838   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(26): previous definition here
05-24 19:06:28.838   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(37): previous definition here
05-24 19:06:28.838   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(45): previous definition here
05-24 19:06:28.838   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(46): previous definition here
05-24 19:06:28.838   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(53): previous definition here
05-24 19:06:28.838   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(56): previous definition here
05-24 19:06:28.838   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(59): previous definition here
05-24 19:06:28.838   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(60): previous definition here
05-24 19:06:28.838   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(61): previous definition here
05-24 19:06:28.838   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(74): previous definition here
05-24 19:06:28.838   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(15): previous definition here
05-24 19:06:28.838   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(88): previous definition here
05-24 19:06:28.838   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(89): previous definition here
05-24 19:06:28.838   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(90): previous definition here
05-24 19:06:28.838   903   903 W android.hardware.media.c2@1.2-mediatek: libminijail[903]: compile_file: <fd>(91): previous definition here
05-24 19:06:28.843   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.configstore@1.0::ISurfaceFlingerConfigs/default in either framework or device VINTF manifest.
05-24 19:06:28.847   903   903 I HidlServiceManagement: Registered android.hardware.media.c2@1.1::IComponentStore/default
05-24 19:06:28.848   903   903 I HidlServiceManagement: Removing namespace from process name android.hardware.media.c2@1.2-mediatek to c2@1.2-mediatek.
05-24 19:06:28.849   960   960 I SurfaceFlinger: Using HWComposer service: default
05-24 19:06:28.857   960   960 I SurfaceFlinger: SurfaceFlinger's main thread ready to run. Initializing graphics H/W...
05-24 19:06:28.865   946   946 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 19:06:28.865   946   946 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 19:06:28.866   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 19:06:28.878   946   946 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 19:06:28.879   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 19:06:28.879   946   946 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 19:06:28.885   946   946 W BatteryNotifier: batterystats service unavailable!
05-24 19:06:28.886   946   946 I ServiceManagerCppClient: Waiting for service 'media.metrics' on '/dev/binder'...
05-24 19:06:28.899  1039  1039 I bootstat: Service started: /system/bin/bootstat --set_system_boot_reason 
05-24 19:06:28.910   980   980 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/05160000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.910   980   980 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/020b0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.912   980   980 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/030b0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.912   980   980 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/03100000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.914  1050  1050 I perfetto:           probes.cc:104 Starting /system/bin/traced_probes service
05-24 19:06:28.916   980   980 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/031c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.916   980   980 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/033c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.916   980   980 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/035c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.916   980   980 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/037c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.916   980   980 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/031c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.916   980   980 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/032c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.916   980   980 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/033c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.916   980   980 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/034c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.916   980   980 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/035c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.916   980   980 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/036c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.916   980   980 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/037c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.918   980   980 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/070c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.918   980   980 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/090b0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.918   980   980 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/0f5eed3c3b5a47afacca69a84bf0efad.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.918   980   980 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/07060000000000000000000000007169.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.918   980   980 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/4be4f7dc1f2c11e5b5f7727283247c7f.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.918   980   980 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/08070000000000000000000000008270.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.918   980   980 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/07070000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.918   980   980 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/07407000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.920  1050  1050 I perfetto:  probes_producer.cc:373 Disconnected from tracing service
05-24 19:06:28.920   980   980 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/6b3f5fa0f8cf55a7be2582587d62d63a.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 19:06:28.921  1051  1051 W perfetto:          service.cc:232 Started traced, listening on /dev/socket/traced_producer /dev/socket/traced_consumer
05-24 19:06:28.938  1054  1054 W MTK_FG_NVRAM: fd < 0, init first!
05-24 19:06:28.938  1054  1054 W MTK_FG_NVRAM: init failed, return!
05-24 19:06:28.952  1054  1054 W MTK_FG_NVRAM: fd < 0, init first!
05-24 19:06:28.952  1054  1054 W MTK_FG_NVRAM: init failed, return!
05-24 19:06:28.952  1054  1054 W MTK_FG_NVRAM: fd < 0, init first!
05-24 19:06:28.953  1054  1054 W MTK_FG_NVRAM: init failed, return!
05-24 19:06:28.960  1054  1054 W MTK_FG_NVRAM: fd < 0, init first!
05-24 19:06:28.960  1054  1054 W MTK_FG_NVRAM: init failed, return!
05-24 19:06:28.960  1054  1054 W MTK_FG_NVRAM: fd < 0, init first!
05-24 19:06:28.960  1054  1054 W MTK_FG_NVRAM: init failed, return!
05-24 19:06:28.960  1054  1054 W MTK_FG_NVRAM: fd < 0, init first!
05-24 19:06:28.960  1054  1054 W MTK_FG_NVRAM: init failed, return!
05-24 19:06:28.960  1054  1054 W MTK_FG_NVRAM: fd < 0, init first!
05-24 19:06:28.960  1054  1054 W MTK_FG_NVRAM: init failed, return!
05-24 19:06:28.960  1054  1054 W MTK_FG_NVRAM: fd < 0, init first!
05-24 19:06:28.960  1054  1054 W MTK_FG_NVRAM: init failed, return!
05-24 19:06:28.960  1054  1054 W MTK_FG_NVRAM: fd < 0, init first!
05-24 19:06:28.960  1054  1054 W MTK_FG_NVRAM: init failed, return!
05-24 19:06:28.960  1054  1054 W MTK_FG_NVRAM: fd < 0, init first!
05-24 19:06:28.960  1054  1054 W MTK_FG_NVRAM: init failed, return!
05-24 19:06:28.986   946   946 I ServiceManagerCppClient: Waiting for service 'media.metrics' on '/dev/binder' successful after waiting 100ms
05-24 19:06:28.987   946   946 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-24 19:06:28.991  1064  1068 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder'...
05-24 19:06:29.018  1063  1063 I main_extractorservice: enable media.extractor memory limits
05-24 19:06:29.027   946   946 E libxml2 : I/O warning : failed to load "/odm/etc/virtual_audio_policy_configuration.xml": No such file or directory
05-24 19:06:29.027   946   946 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-24 19:06:29.027   946   946 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-24 19:06:29.034  1052  1052 I vtservice: [VT][SRV]ServiceManager: 0xb40000769e63fc10
05-24 19:06:29.034  1052  1052 I vtservice: [VT][SRV]before VTService_instantiate
05-24 19:06:29.036  1063  1063 W mediaextractor: libminijail[1063]: failed to get path of fd 5: No such file or directory
05-24 19:06:29.036  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(38): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(18): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(6): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(27): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(29): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(28): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(4): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(32): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(12): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(9): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(8): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(23): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(41): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(25): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(56): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(5): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(14): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(13): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(11): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(15): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(16): previous definition here
05-24 19:06:29.037  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(10): previous definition here
05-24 19:06:29.038  1063  1063 W mediaextractor: libminijail[1063]: compile_file: /apex/com.android.media/etc/seccomp_policy/crash_dump.arm64.policy(7): previous definition here
05-24 19:06:29.038  1063  1063 W mediaextractor: libminijail[1063]: compile_file: <fd>(56): previous definition here
05-24 19:06:29.038  1063  1063 W mediaextractor: libminijail[1063]: compile_file: /apex/com.android.media/etc/seccomp_policy/crash_dump.arm64.policy(6): previous definition here
05-24 19:06:29.043  1074  1074 I android.hardware.media.omx@1.0-service: mediacodecservice starting
05-24 19:06:29.044  1055  1055 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 19:06:29.044  1055  1055 E mnld_pwr_interface: mnld_pwr_init: mnld_pwr_open failed, No such file or directory
05-24 19:06:29.044  1055  1055 E MNL2AGPS: bind_udp_socket: bind failed path=[/data/agps_supl/agps_to_mnl] reason=[No such file or directory]
05-24 19:06:29.044  1055  1055 E mtk_lbs_utility: init_timer_id_alarm: timerfd_create  CLOCK_BOOTTIME_ALARM 
05-24 19:06:29.044  1055  1055 E MNLD    : mnld_init: mnl2hal_release_wakelock failed because of safe_sendto fail ,strerror:Connection refused 
05-24 19:06:29.045  1055  1055 E MNLD    : mnld_init: mnl2hal_mnld_reboot failed because of safe_sendto fail ,strerror:Connection refused 
05-24 19:06:29.045  1055  1055 E mnld    : mtk_socket_connect_local: mtk_socket_connect_local() connect() failed reason=[Connection refused]111 for path=[mtk_mnld2debugService]
05-24 19:06:29.045  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: failed to get path of fd 5: No such file or directory
05-24 19:06:29.045  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: allowing syscall: clock_gettime
05-24 19:06:29.045  1055  1102 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 19:06:29.045  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: allowing syscall: connect
05-24 19:06:29.045  1055  1102 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:0
05-24 19:06:29.045  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: allowing syscall: fcntl64
05-24 19:06:29.045  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: allowing syscall: socket
05-24 19:06:29.045  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: allowing syscall: writev
05-24 19:06:29.045  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(59): syscall getrandom redefined here
05-24 19:06:29.045  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(15): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(1): syscall read redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(9): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(2): syscall write redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(5): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(3): syscall exit redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(40): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(4): syscall rt_sigreturn redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(45): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(6): syscall exit_group redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(44): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(7): syscall clock_gettime redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(7): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(8): syscall gettimeofday redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(47): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(9): syscall futex redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(3): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(10): syscall getrandom redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(15): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(13): syscall ppoll redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(13): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(14): syscall pipe2 redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(46): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(15): syscall openat redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(30): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(16): syscall dup redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(12): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(17): syscall close redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(10): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(18): syscall lseek redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(50): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(19): syscall getdents64 redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(58): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(20): syscall faccessat redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(38): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(27): syscall rt_sigprocmask redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(41): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(30): syscall prctl redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(6): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(31): syscall madvise redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(29): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(32): syscall mprotect redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(28): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(33): syscall munmap redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(27): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(34): syscall getuid32 redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(34): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(35): syscall fstat64 redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(35): previous definition here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(36): syscall mmap2 redefined here
05-24 19:06:29.046  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(14): previous definition here
05-24 19:06:29.047  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(66): syscall getpid redefined here
05-24 19:06:29.047  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(11): previous definition here
05-24 19:06:29.047  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(67): syscall gettid redefined here
05-24 19:06:29.047  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(12): previous definition here
05-24 19:06:29.047  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(74): syscall recvfrom redefined here
05-24 19:06:29.047  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(22): previous definition here
05-24 19:06:29.047  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(77): syscall sched_getaffinity redefined here
05-24 19:06:29.047  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(75): previous definition here
05-24 19:06:29.047  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(82): syscall sysinfo redefined here
05-24 19:06:29.047  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(24): previous definition here
05-24 19:06:29.047  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: <fd>(83): syscall setsockopt redefined here
05-24 19:06:29.047  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(23): previous definition here
05-24 19:06:29.047  1074  1074 W android.hardware.media.omx@1.0-service: libminijail[1074]: logging seccomp filter failures
05-24 19:06:29.049  1080  1080 I thermal_repeater: RilRPC_init 
05-24 19:06:29.051  1080  1080 I thermal_repeater: RilRPC_init dlopen fail: dlopen failed: library "librpcril.so" not found 
05-24 19:06:29.051  1083  1083 I thermal_src1: ta_daemon_init
05-24 19:06:29.064  1083  1083 I thermal_src: ta_catm_init_flow
05-24 19:06:29.064  1083  1083 I thermal_src: u_CATM_ON == -1, get catm init val
05-24 19:06:29.071  1087  1087 I VPUD    : vdec_codec_service_init() block mode
05-24 19:06:29.071  1087  1087 I VPUD    : venc_codec_service_init()
05-24 19:06:29.071  1087  1087 I VPUD    : -- send_init_fin
05-24 19:06:29.071  1087  1119 I VPUD    : vdec_service_entry()
05-24 19:06:29.075  1087  1120 I VPUD    : venc service entry TID = 1120
05-24 19:06:29.082  1066  1066 I storaged: Unable to get AIDL health service, trying HIDL...
05-24 19:06:29.086  1115  1115 I KernelSU Next: ksud::cli: command: Services
05-24 19:06:29.086  1115  1115 I KernelSU Next: ksud::init_event: on_services triggered!
05-24 19:06:29.086  1115  1115 I KernelSU Next: ksud::module: /data/adb/service.d not exists, skip
05-24 19:06:29.086  1115  1115 I KernelSU Next: ksud::module: exec /data/adb/modules/bindhosts/service.sh
05-24 19:06:29.087  1115  1115 I KernelSU Next: ksud::module: exec /data/adb/modules/zygisk_lsposed/service.sh
05-24 19:06:29.088   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.088   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.090  1115  1115 I KernelSU Next: ksud::module: exec /data/adb/modules/zygisksu/service.sh
05-24 19:06:29.094  1115  1115 I KernelSU Next: ksud::module: exec /data/adb/modules/yt_rvx/service.sh
05-24 19:06:29.101   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.102   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.102   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.102   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.102   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.102   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.102   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.102   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.102   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.102   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.103   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.103   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.103   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.103   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.103   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.103   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.108   873   873 I zygote64: Initializing ART runtime metrics
05-24 19:06:29.109  1085  1139 I MtkAgpsNative: Enter mtk_agps_up_init
05-24 19:06:29.109  1056  1056 I cameraserver: ServiceManager: 0xb40000753d741fd0
05-24 19:06:29.110  1056  1056 I CameraService: CameraService started (pid=1056)
05-24 19:06:29.110  1056  1056 I CameraService: CameraService process starting
05-24 19:06:29.110  1085  1139 E agps    : [agps] ERR: [MNL] bind failed path=[/data/agps_supl/mnl_to_agps] reason=[No such file or directory]
05-24 19:06:29.111  1085  1139 E agps    : [agps] ERR: [Default] mtk_expat_xml_load2() failed [fopen() No such file or directory] file [/data/vendor/agps_supl/agps_profiles_conf2.xml]
05-24 19:06:29.111  1085  1139 E agps    : [agps] ERR: [Default] mtk_expat_xml_load2() failed [fopen() No such file or directory] file [/data/agps_supl/agps_profiles_conf2.xml]
05-24 19:06:29.111  1085  1139 E agps    : [agps] ERR: [Default] mtk_expat_xml_load2() failed [fopen() No such file or directory] file [/vendor/etc/agps_profiles_conf2.xml]
05-24 19:06:29.112  1100  1100 E android.hardware.biometrics.fingerprint@2.1-service: fingerprint hwbinder service starting
05-24 19:06:29.112  1100  1100 I android.hardware.biometrics.fingerprint@2.1-service: read_proc_string_data sys_buffer_data=G_OPTICAL_G3S #end
05-24 19:06:29.112  1072  1072 I HidlServiceManagement: Registered android.system.wifi.keystore@1.0::IKeystore/default
05-24 19:06:29.113  1056  1056 W BatteryNotifier: batterystats service unavailable!
05-24 19:06:29.113  1056  1056 W BatteryNotifier: batterystats service unavailable!
05-24 19:06:29.113  1074  1074 I HidlServiceManagement: Registered android.hardware.media.omx@1.0::IOmx/default
05-24 19:06:29.114  1085  1139 E mtk_socket: ERR: mtk_socket_connect_local() connect() failed reason=[Connection refused]111 for path=[mtk_agpsd2debugService]
05-24 19:06:29.114  1074  1074 I HidlServiceManagement: Removing namespace from process name android.hardware.media.omx@1.0-service to omx@1.0-service.
05-24 19:06:29.114  1085  1139 E agps    : [agps] ERR: [CP] get_ccci_uart  open failed node=[/dev/ccci2_tty2] reason=[No such file or directory]
05-24 19:06:29.114  1085  1139 E agps    :  ERR: [AGPS2WIFI] bind failed path=[/data/agps_supl/wifi_2_agps] reason=[No such file or directory]
05-24 19:06:29.114  1085  1139 E agps    : [agps] ERR: [WIFI] wifi_mgr_init  create_wifi2agps_fd failed
05-24 19:06:29.114  1100  1100 E android.hardware.biometrics.fingerprint@2.1-service: fp read fp_id_string = G_OPTICAL_G3S
05-24 19:06:29.114  1074  1074 I android.hardware.media.omx@1.0-service: IOmx HAL service created.
05-24 19:06:29.115  1100  1100 E android.hardware.biometrics.fingerprint@2.1-service: optical goodix fingerprint
05-24 19:06:29.115  1100  1100 I android.hardware.biometrics.fingerprint@2.1-service: fpSettingsInit project name:0
05-24 19:06:29.115  1100  1100 I android.hardware.biometrics.fingerprint@2.1-service: read_proc_string_data sys_buffer_data=Device version:		AMS644VA04_MTK04_20615
05-24 19:06:29.115  1100  1100 I android.hardware.biometrics.fingerprint@2.1-service: Device manufacture:		samsung1024
05-24 19:06:29.115  1100  1100 I android.hardware.biometrics.fingerprint@2.1-service:  #end
05-24 19:06:29.115  1100  1100 I android.hardware.biometrics.fingerprint@2.1-service: fpSettingsInit lcd type:1
05-24 19:06:29.116  1100  1100 I android.hardware.biometrics.fingerprint@2.1-service: fpSettingsInit select config index is :0
05-24 19:06:29.117  1100  1100 I android.hardware.biometrics.fingerprint@2.1-service: do nothing
05-24 19:06:29.121  1074  1074 E MediaCodecsXmlParser: Cannot find the role for a decoder of type audio/x-adpcm-dvi-ima
05-24 19:06:29.121  1074  1074 E MediaCodecsXmlParser: Cannot find the role for a decoder of type audio/x-adpcm-ms
05-24 19:06:29.121  1074  1074 E MediaCodecsXmlParser: Cannot find the role for a decoder of type audio/alac
05-24 19:06:29.121  1074  1074 E MediaCodecsXmlParser: Cannot find the role for a decoder of type audio/ape
05-24 19:06:29.121  1074  1074 I OmxStore: node [OMX.MTK.AUDIO.DECODER.GSM] not found in IOmx
05-24 19:06:29.121  1074  1074 I OmxStore: node [OMX.MTK.AUDIO.DECODER.MP3] not found in IOmx
05-24 19:06:29.122  1050  1050 I perfetto:  probes_producer.cc:332 Connected to the service
05-24 19:06:29.125  1100  1100 E [GF_HAL][HalContext]: [init], init with G3 HAL.
05-24 19:06:29.126   910   910 I android.hardware.usb@1.3-service-mediatekv2: setCurrentUsbFunctions: skip first time for usbd
05-24 19:06:29.126   910   910 I android.hardware.usb@1.3-service-mediatekv2: Usb Gadget setcurrent functions failed
05-24 19:06:29.131   580   580 I hwservicemanager: Since vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 19:06:29.133   580   580 I hwservicemanager: Since vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 19:06:29.134  1092  1092 I HidlServiceManagement: getService: Trying again for vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default...
05-24 19:06:29.139  1077  1077 I ULog    : ULog initialized: mode=0x1  filters: req=0x0 func=0x0/0x0 details=0xfffff000 level=3
05-24 19:06:29.156   580   580 I hwservicemanager: Since android.hardware.camera.provider@2.4::ICameraProvider/internal/0 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 19:06:29.156  1056  1056 W CameraProviderManager: tryToInitializeHidlProviderLocked: HIDL Camera provider HAL 'internal/0' is not actually available
05-24 19:06:29.157  1074  1074 I HidlServiceManagement: Registered android.hardware.media.omx@1.0::IOmxStore/default
05-24 19:06:29.158  1074  1074 I HidlServiceManagement: Removing namespace from process name android.hardware.media.omx@1.0-service to omx@1.0-service.
05-24 19:06:29.158  1056  1056 W CameraProviderManager: tryToInitializeAidlProviderLocked: AIDL Camera provider HAL 'android.hardware.camera.provider.ICameraProvider/virtual/0' is not actually available
05-24 19:06:29.161  1056  1056 I HidlServiceManagement: Registered android.frameworks.cameraservice.service@2.2::ICameraService/default
05-24 19:06:29.162  1056  1056 I CameraService: CameraService pinged cameraservice proxy
05-24 19:06:29.162  1056  1056 I cameraserver: ServiceManager: 0xb40000753d741fd0 done instantiate
05-24 19:06:29.163   580  1201 I hwservicemanager: Tried to start vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 19:06:29.163   580  1207 I hwservicemanager: Tried to start android.hardware.camera.provider@2.4::ICameraProvider/internal/0 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 19:06:29.164   580  1198 I hwservicemanager: Tried to start vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 19:06:29.165  1066  1066 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder'...
05-24 19:06:29.166   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.atrace@1.0::IAtraceDevice/default in either framework or device VINTF manifest.
05-24 19:06:29.167  1086  1086 I HidlServiceManagement: Registered vendor.mediatek.hardware.pq@2.15::IPictureQuality/default
05-24 19:06:29.167  1086  1086 I HidlServiceManagement: Removing namespace from process name vendor.mediatek.hardware.pq@2.2-service to pq@2.2-service.
05-24 19:06:29.169   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.170   889   889 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.173   889   889 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 19:06:29.200   889   889 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x82bdb5f3)
05-24 19:06:29.207   889   889 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0xa3638a7f)
05-24 19:06:29.236   889   889 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 19:06:29.237   889   967 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-24 19:06:29.238   889   889 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xf54df517)
05-24 19:06:29.238   889   967 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-24 19:06:29.240   889   889 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0xa7523bc5)
05-24 19:06:29.242  1086  1212 E PQ      : [PQ_SERVICE] aisdr2hdr_pqindex is not found in cust_color.xml
05-24 19:06:29.243   889   967 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 19:06:29.251   963   963 E ccci_mdinit: (1):fail to open /mnt/vendor/nvdata/APCFG/APRDCL/CXP_SBP: 2
05-24 19:06:29.251   963   963 I ccci_mdinit: (1):get_cip_sbp_setting, file /custom/etc/firmware/CIP_MD_SBP NOT exists!
05-24 19:06:29.251   963   963 I ccci_mdinit: (1):PRJ_SBP_ID:ro.vendor.mtk_md_sbp_custom_value not exist, using default value
05-24 19:06:29.253   889   889 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-24 19:06:29.254   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 19:06:29.254   889   889 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-24 19:06:29.255   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 19:06:29.256   889   889 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-24 19:06:29.256   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 19:06:29.257   889   889 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-24 19:06:29.257   889   889 W audiohalservice: Could not register Bluetooth Audio API
05-24 19:06:29.257   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-24 19:06:29.258   889   889 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-24 19:06:29.258   889   889 W audiohalservice: Could not register Bluetooth Audio Offload API
05-24 19:06:29.260   963   963 I ccci_mdinit: (1):SBP_SUB_ID:persist.vendor.operator.subid not exist
05-24 19:06:29.260   963   963 I ccci_mdinit: (1):set md boot data:mdl=0 sbp=0 dbg_dump=-1 sbp_sub=0
05-24 19:06:29.261   963   963 E ccci_mdinit: [SYSENV]get_env_info():240 , env_buffer[0] : 0xb400006ef6c0a030
05-24 19:06:29.261   963   963 I ccci_mdinit: (1):get md_type (null)
05-24 19:06:29.262   963   963 I ccci_mdinit: (1):MD1 set status: mtk.md1.status=bootup 
05-24 19:06:29.262   963   963 I ccci_mdinit: (1):md_id = 0; mdstatusfd = -1
05-24 19:06:29.265  1086  1212 I vendor.mediatek.hardware.pq@2.2-service: transferAIOutput(), register trs callback
05-24 19:06:29.305  1053  1053 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-neuron
05-24 19:06:29.305  1053  1053 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-24 19:06:29.305   960   960 I HidlServiceManagement: Registered android.frameworks.displayservice@1.0::IDisplayService/default
05-24 19:06:29.306  1053  1053 I ANNService: Registered service for mtk-neuron
05-24 19:06:29.308  1053  1053 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-dsp
05-24 19:06:29.308  1053  1053 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-24 19:06:29.308  1053  1053 I ANNService: Registered service for mtk-dsp
05-24 19:06:29.310  1053  1053 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-mdla
05-24 19:06:29.310  1053  1053 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-24 19:06:29.313  1053  1053 I ANNService: Registered service for mtk-mdla
05-24 19:06:29.318  1093  1093 I NetdagentFirewall: setupIptablesHooks done in oem_iptables_init
05-24 19:06:29.318  1093  1093 I NetdagentController: Initializing iptables: 231.3ms
05-24 19:06:29.318  1093  1093 I Netdagent:  Create CommandService  successfully
05-24 19:06:29.328  1053  1053 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-gpu
05-24 19:06:29.329  1053  1053 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-24 19:06:29.330  1053  1053 I ANNService: Registered service for mtk-gpu
05-24 19:06:29.331   889   889 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-24 19:06:29.332   889   889 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-24 19:06:29.332   889   889 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-24 19:06:29.334  1093  1291 I HidlServiceManagement: Registered vendor.mediatek.hardware.netdagent@1.0::INetdagent/default
05-24 19:06:29.343  1053  1053 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.apusys@2.1::INeuronApusys/default
05-24 19:06:29.350  1053  1053 I apuware_server: Start NeuronXrp 2.0 service 
05-24 19:06:29.352  1053  1053 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.xrp@2.0::INeuronXrp/default
05-24 19:06:29.354  1065  1065 I mediaserver: ServiceManager: 0xf3f03130
05-24 19:06:29.355  1065  1065 W BatteryNotifier: batterystats service unavailable!
05-24 19:06:29.356  1065  1065 W BatteryNotifier: batterystats service unavailable!
05-24 19:06:29.357  1053  1053 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.hmp@1.0::IApuwareHmp/default
05-24 19:06:29.362  1053  1053 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.utils@2.0::IApuwareUtils/default
05-24 19:06:29.370   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.370   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.371   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.371   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.371   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.371   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.371   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.371   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.371   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.371   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.371   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.372   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.372   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.372   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.372   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.372   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.372   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.372   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.406  1191  1191 W ziparchive: Unable to open '/data/adb/modules/zygisk_lsposed/daemon.dm': No such file or directory
05-24 19:06:29.429   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.429   889   967 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:29.432   889   967 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 19:06:29.455   889   967 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xe8d0b3d7)
05-24 19:06:29.456   889  1312 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-24 19:06:29.457  1191  1191 I LSPosedService: starting server...
05-24 19:06:29.457  1191  1191 I LSPosedService: version 1.10.1 (7178)
05-24 19:06:29.458   889  1312 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0xe8d0b3d7)
05-24 19:06:29.459   889  1312 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0xe8d0b3d7)
05-24 19:06:29.484  1089  1089 I mediaswcodec: media swcodec service starting
05-24 19:06:29.486  1089  1089 W mediaswcodec: libminijail[1089]: failed to get path of fd 5: No such file or directory
05-24 19:06:29.486  1089  1089 W mediaswcodec: libminijail[1089]: compile_file: <fd>(39): previous definition here
05-24 19:06:29.489  1089  1089 I CodecServiceRegistrant: Creating software Codec2 service...
05-24 19:06:29.500  1089  1089 I HidlServiceManagement: Registered android.hardware.media.c2@1.2::IComponentStore/software
05-24 19:06:29.502  1089  1089 I CodecServiceRegistrant: Preferred Codec2 HIDL store is set to "default".
05-24 19:06:29.502  1089  1089 I CodecServiceRegistrant: Software Codec2 service created and registered.
05-24 19:06:29.518   889   967 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-24 19:06:29.523   889   967 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 19:06:29.536  1052  1052 W AVSync  : initFD done, g_fd_name: /dev/ccci_imsdc
05-24 19:06:29.537   578   578 I auditd  : avc:  denied  { find } for pid=1052 uid=1000 name=vendor.mediatek.hardware.videotelephony.IVideoTelephony/default scontext=u:r:vtservice:s0 tcontext=u:object_r:default_android_service:s0 tclass=service_manager permissive=0
05-24 19:06:29.537  1052  1328 W ServiceManagerCppClient: Failed to get isDeclared for vendor.mediatek.hardware.videotelephony.IVideoTelephony/default: Status(-1, EX_SECURITY): 'SELinux denied for service.'
05-24 19:06:29.548   889   967 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-24 19:06:29.564   889   967 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 19:06:29.565   889   967 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 19:06:29.565   889   967 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 19:06:29.570   889   967 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 19:06:29.570   889   967 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 19:06:29.572   889   967 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-24 19:06:29.573   963   988 E ccci_fsd(1): FS_OTP_init:otp_get_size:1048576, status=0, type=0!
05-24 19:06:29.580   889   967 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-24 19:06:29.581   889   967 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-24 19:06:29.584   946   946 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 19:06:29.584   946   946 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 19:06:29.585   889   889 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-24 19:06:29.585   889   889 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-24 19:06:29.586   889   889 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 19:06:29.587   946   946 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-24 19:06:29.588   946   946 I AudioFlinger: openOutput() this 0xb400006f56d69900, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-24 19:06:29.590   946   946 I AudioFlinger: operator(): Using 3000 ms as standby time
05-24 19:06:29.591   946   946 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 19:06:29.592   873   873 E ActivityRecognitionHardware: activity_recognition HAL is deprecated. class_init is effectively a no-op
05-24 19:06:29.594   946   946 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 19:06:29.594   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 19:06:29.596   946   946 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 19:06:29.622   946  1345 I AudioFlinger: AudioFlinger's thread 0xb40000714e7ce760 tid=1345 ready to run
05-24 19:06:29.679   889  1324 W audio_messenger_ipi: read_ipi_thread(), read dsp fail!! length_read: 0, errno: 0
05-24 19:06:29.688  1350  1350 E DEBUG   : failed to read process info: failed to open /proc/889: No such file or directory
05-24 19:06:29.702   873   873 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlags
05-24 19:06:29.702   873   873 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlagsImpl
05-24 19:06:29.702   873   873 W Zygote  : Class not found for preloading: android.media.audio.Flags
05-24 19:06:29.722   963   988 W ccci_mdinit: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.system.suspend.ISystemSuspend/default
05-24 19:06:29.740  1077  1077 I mtkcam-devicemgr: [initialize] +
05-24 19:06:29.869  1350  1350 F DEBUG   : Cmdline: /vendor/bin/hw/android.hardware.audio.service
05-24 19:06:29.869  1350  1350 F DEBUG   : pid: 889, tid: 965, name: HwBinder:889_1  >>> /vendor/bin/hw/android.hardware.audio.service <<<
05-24 19:06:29.869  1350  1350 F DEBUG   :       #01 pc 0002651d  /vendor/lib/hw/<EMAIL> (android::hardware::audio::V7_1::implementation::StreamOut::setLatencyModeCallback(android::sp<android::hardware::audio::V7_1::IStreamOutLatencyModeCallback> const&)+36) (BuildId: 544a581a8a5464266e14c74f5aea9a1c)
05-24 19:06:29.869  1350  1350 F DEBUG   :       #02 pc 00043c0f  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::_hidl_setLatencyModeCallback(android::hidl::base::V1_0::BnHwBase*, android::hardware::Parcel const&, android::hardware::Parcel*, std::__1::function<void (android::hardware::Parcel&)>)+198) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 19:06:29.869  1350  1350 F DEBUG   :       #03 pc 00044703  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::onTransact(unsigned int, android::hardware::Parcel const&, android::hardware::Parcel*, unsigned int, std::__1::function<void (android::hardware::Parcel&)>)+1926) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 19:06:29.917   946  1043 E HalDeathHandler: HAL server crashed, audio server is restarting
05-24 19:06:29.925  1100  1100 E [GF_HAL][ShenzhenSensor]: [init] gainvalue: 150/100
05-24 19:06:29.925  1100  1100 E [GF_HAL][ShenzhenSensor]: [init] expotime 38
05-24 19:06:29.925  1100  1100 E [GF_HAL][ShenzhenSensor]: [init] @@@@@ mQRCode=Z918095013A0061493,len=18
05-24 19:06:29.926  1100  1100 E [GF_HAL][ShenzhenSensor]: [init] module_type = 0x6
05-24 19:06:29.926  1100  1100 E [GF_HAL][ShenzhenSensor]: [init] lens_type = 0xa
05-24 19:06:29.959  1378  1378 W idmap2  : failed to find resource 'bool/grant_location_permission_enabled'
05-24 19:06:30.022   876   876 I zygote  : Initializing ART runtime metrics
05-24 19:06:30.045  1055  1094 E mnld    : thread_adc_capture_init: set IOCTL_EMI_MEMORY_INIT failed,(Success)
05-24 19:06:30.045  1055  1102 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 19:06:30.045  1055  1102 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:1
05-24 19:06:30.124  1191  1191 I LSPosedService: service package is not started, wait 1s.
05-24 19:06:30.135  1092  1092 W HidlServiceManagement: Waited one second for vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default
05-24 19:06:30.135   580   580 I hwservicemanager: Since vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 19:06:30.136  1092  1092 I HidlServiceManagement: getService: Trying again for vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default...
05-24 19:06:30.150   580  1398 I hwservicemanager: Tried to start vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 19:06:30.439  1400  1400 W system_server: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: serial
05-24 19:06:30.440  1400  1409 W system_server: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: stats
05-24 19:06:30.440  1400  1409 W BpBinder: Linking to death on android.os.IStatsd but there are no threads (yet?) listening to incoming transactions. See ProcessState::startThreadPool and ProcessState::setThreadPoolMaxThreadCount. Generally you should setup the binder threadpool before other initialization steps.
05-24 19:06:30.472  1100  1100 E [GF_HAL][FingerprintCore]: [init_report_data] algo version is V03.02.02.230.005
05-24 19:06:30.472  1100  1100 E [GF_HAL][FingerprintCore]: [init_report_data] lcdtype_prop = SDC
05-24 19:06:30.472  1100  1100 E [GF_HAL][FingerprintCore]: [init_report_data] type = V03.02.02.230.005_S_SDC
05-24 19:06:30.504  1100  1100 I HidlServiceManagement: Registered vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default
05-24 19:06:30.504  1100  1100 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.biometrics.fingerprint@2.1-service to fingerprint@2.1-service.
05-24 19:06:30.506   580   580 W hwservicemanager: Detected instance of android.hardware.biometrics.fingerprint@2.3::IBiometricsFingerprint (pid: 1092) registering over instance of or with base of android.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint (pid: 1100).
05-24 19:06:30.507  1092  1092 I HidlServiceManagement: Registered android.hardware.biometrics.fingerprint@2.3::IBiometricsFingerprint/default
05-24 19:06:30.507  1092  1092 I HidlServiceManagement: Removing namespace from <NAME_EMAIL>6893 to fingerprint@2.3-service.mt6893.
05-24 19:06:30.515  1400  1400 W BpBinder: Linking to death on org.lsposed.lspd.service.ILSPApplicationService but there are no threads (yet?) listening to incoming transactions. See ProcessState::startThreadPool and ProcessState::setThreadPoolMaxThreadCount. Generally you should setup the binder threadpool before other initialization steps.
05-24 19:06:31.045  1055  1102 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 19:06:31.045  1055  1102 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:2
05-24 19:06:31.064   963   963 I ccci_mdinit: (1):MD1 set status: mtk.md1.status=ready 
05-24 19:06:31.065   963   963 I ccci_mdinit: (1):start_service init.svc.emdlogger1, but returned 0, maybe has no this property
05-24 19:06:31.066   963   963 I ccci_mdinit: (1):wait_for_property: is_stop_wait: 0
05-24 19:06:31.077   963   963 I ccci_mdinit: (1):start_service init.svc.vendor.gsm0710muxd, but returned 0, maybe has no this property
05-24 19:06:31.085   963   963 I ccci_mdinit: (1):wait_for_property: is_stop_wait: 0
05-24 19:06:31.085   963   963 I ccci_mdinit: (1):wait_for_property:success(init.svc.vendor.gsm0710muxd=running), loop:600
05-24 19:06:31.125  1191  1191 I LSPosedService: service package is not started, wait 1s.
05-24 19:06:31.292   872   884 I ServiceManagerCppClient: Waiting for service 'statscompanion' on '/dev/binder'...
05-24 19:06:31.432  1400  1400 E system_server: memevent listener failed to initialize, not supported kernel
05-24 19:06:31.537  1052  1052 I AVSync  : avInit, st 254d526d6, int=a, frac=3616c31
05-24 19:06:31.537  1052  1052 I vtservice: [VT][SRV]after VTService_instantiate
05-24 19:06:31.538  1052  1459 I AVSync  : avInit, st 254ea2f73, int=a, frac=3bbde08
05-24 19:06:31.586  1458  1458 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/slot1
05-24 19:06:31.587  1458  1458 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSlot1
05-24 19:06:31.590  1458  1458 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/imsAospSlot1
05-24 19:06:31.591  1458  1463 E SchedPolicy: open of /dev/cpuctl/bg_non_interactive/tasks failed: No such file or directory
05-24 19:06:31.591  1458  1458 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/imsSlot1
05-24 19:06:31.592  1458  1458 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/se1
05-24 19:06:31.593  1458  1474 I RmcVsim : [1] RmcVsimUrcHandler init slot: 1, ch id 0
05-24 19:06:31.593  1458  1458 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSe1
05-24 19:06:31.594  1458  1458 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/em1
05-24 19:06:31.595  1458  1472 I RmcVsim : [0] RmcVsimUrcHandler init slot: 0, ch id 0
05-24 19:06:31.595  1458  1458 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkEm1
05-24 19:06:31.597  1458  1458 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkAssist1
05-24 19:06:31.600  1458  1458 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRcs1
05-24 19:06:31.603  1458  1458 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkCap1
05-24 19:06:31.605  1458  1458 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSmartRatSwitch1
05-24 19:06:31.606  1458  1493 I RmcDcImsDc2ReqHandler: [0][handleImsBearerNotify] urc=+EIMSPDN: "init"
05-24 19:06:31.606  1458  1458 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRsu1
05-24 19:06:31.609  1458  1458 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/slot2
05-24 19:06:31.611  1458  1458 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSlot2
05-24 19:06:31.613  1458  1515 I RmcDcImsDc2ReqHandler: [1][handleImsBearerNotify] urc=+EIMSPDN: "init"
05-24 19:06:31.613  1458  1458 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/imsAospSlot2
05-24 19:06:31.614  1458  1465 I WpfaCppUtils: initialRuleContainer!
05-24 19:06:31.615  1458  1458 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/imsSlot2
05-24 19:06:31.617  1458  1465 I WpfaCppUtils: initialA2MRingBuffer!
05-24 19:06:31.619  1458  1458 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/se2
05-24 19:06:31.625  1458  1465 W mtkfusionrild: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.system.suspend.ISystemSuspend/default
05-24 19:06:31.625  1458  1458 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSe2
05-24 19:06:31.627  1458  1458 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/em2
05-24 19:06:31.632  1458  1458 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkEm2
05-24 19:06:31.641  1458  1458 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkAssist2
05-24 19:06:31.642  1458  1458 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRcs2
05-24 19:06:31.644  1458  1458 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkCap2
05-24 19:06:31.647  1458  1458 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSmartRatSwitch2
05-24 19:06:31.648  1458  1458 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRsu2
05-24 19:06:31.649  1458  1458 I RILC-OplusRadioAIDL: radio::registerService starting IOplusRadio vendor.oplus.hardware.radio.IRadioStable/OplusRadio0
05-24 19:06:31.650  1458  1458 I RILC-OplusRadioAIDL: radio::registerService IOplusRadio status: 0
05-24 19:06:31.650  1458  1458 I RILC-OplusRadioAIDL: radio::registerService starting IOplusRadio vendor.oplus.hardware.radio.IRadioStable/OplusRadio1
05-24 19:06:31.652  1458  1458 I RILC-OplusRadioAIDL: radio::registerService IOplusRadio status: 0
05-24 19:06:31.653  1458  1458 I RILC-OplusAppRadio: radio::registerService starting IOplusAppRadio oplus_app_slot1
05-24 19:06:31.655  1458  1458 I HidlServiceManagement: Registered vendor.oplus.hardware.appradio@1.0::IOplusAppRadio/oplus_app_slot1
05-24 19:06:31.656  1458  1458 I RILC-OplusAppRadio: radio::registerService IOplusAppRadio status: 0
05-24 19:06:31.656  1458  1458 I RILC-OplusAppRadio: radio::registerService starting IOplusAppRadio oplus_app_slot2
05-24 19:06:31.658  1458  1458 I HidlServiceManagement: Registered vendor.oplus.hardware.appradio@1.0::IOplusAppRadio/oplus_app_slot2
05-24 19:06:31.658  1458  1458 I RILC-OplusAppRadio: radio::registerService IOplusAppRadio status: 0
05-24 19:06:31.659  1458  1458 I HidlServiceManagement: Registered android.hardware.radio.config@1.3::IRadioConfig/default
05-24 19:06:31.662  1458  1458 I HidlServiceManagement: Registered android.hardware.radio@1.2::ISap/slot1
05-24 19:06:31.667  1458  1458 I HidlServiceManagement: Registered android.hardware.radio@1.2::ISap/slot2
05-24 19:06:31.718  1458  1529 E libmnlUtils: No action: deInitReaderLoop can't get mMnlsocket
05-24 19:06:31.718  1458  1529 I wpfa    : initReaderLoop() done, buf_size=67583
05-24 19:06:31.718  1458  1529 I wpfa    : WPFA_DL initialized
05-24 19:06:32.045   911  1331 I VT HIDL : [IVT] [VT THREAD] [VT_Bind] des = volte_imsvt1 initialize communication
05-24 19:06:32.046  1055  1102 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 19:06:32.046  1055  1102 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:3
05-24 19:06:32.126  1191  1191 I LSPosedService: service package is not started, wait 1s.
05-24 19:06:32.185  1400  1400 I SystemServerInitThreadPool: Creating instance with 8 threads
05-24 19:06:32.225  1400  1400 E LSPosed-Bridge: java.lang.NoSuchMethodError: com.android.server.pm.PackageManagerServiceUtils#checkDowngrade(com.android.server.pm.pkg.AndroidPackage,android.content.pm.PackageInfoLite)#exact
05-24 19:06:32.225  1400  1400 E LSPosed-Bridge: 	at LSPHooker_.startBootstrapServices(Unknown Source:11)
05-24 19:06:32.225  1400  1400 E LSPosed-Bridge: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:597)
05-24 19:06:32.225  1400  1400 E LSPosed-Bridge: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:889)
05-24 19:06:32.234  1458  1465 E RILC-OplusRadioAIDL: oplusRadioServiceAIDL: oplusRadioServiceAIDL[0]->mRadioIndicationOplus == NULL
05-24 19:06:32.249  1400  1561 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-com.android.providers.media.module.xml
05-24 19:06:32.252  1400  1561 I SystemConfig: Reading permissions from /system/etc/sysconfig/app.grapheneos.gmscompat.xml
05-24 19:06:32.253  1400  1561 I SystemConfig: Reading permissions from /system/etc/sysconfig/app.grapheneos.networklocation.xml
05-24 19:06:32.253  1400  1561 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-platform-full-base.xml
05-24 19:06:32.253  1400  1561 I SystemConfig: Reading permissions from /system/etc/sysconfig/framework-sysconfig.xml
05-24 19:06:32.254  1400  1561 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-asl-files.xml
05-24 19:06:32.254  1400  1561 I SystemConfig: Reading permissions from /system/etc/sysconfig/hiddenapi-whitelist-co.aospa.sense.xml
05-24 19:06:32.254  1400  1561 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-strict-signature.xml
05-24 19:06:32.255  1400  1561 I SystemConfig: Reading permissions from /system/etc/sysconfig/backup.xml
05-24 19:06:32.255  1400  1561 I SystemConfig: Reading permissions from /system/etc/sysconfig/hiddenapi-package-whitelist.xml
05-24 19:06:32.255  1400  1561 I SystemConfig: Reading permissions from /system/etc/sysconfig/enhanced-confirmation.xml
05-24 19:06:32.256  1400  1561 I SystemConfig: Reading permissions from /system/etc/sysconfig/package-shareduid-allowlist.xml
05-24 19:06:32.256  1400  1561 I SystemConfig: Reading permissions from /system/etc/sysconfig/initial-package-stopped-states.xml
05-24 19:06:32.256  1400  1561 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-platform-handheld-system.xml
05-24 19:06:32.257  1400  1561 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-platform.xml
05-24 19:06:32.257  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/DigitalWellbeing.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.257  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/GoogleContacts.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.257  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/GoogleContactsSyncAdapter.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.257  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/Drive.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.257  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/GoogleMaps.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.257  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.intentresolver.xml
05-24 19:06:32.258  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/GoogleCalendar.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.258  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/CarrierServices.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.258  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/GoogleDialer.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.258  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/GoogleOneTimeInitializer.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.258  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.documentsui.xml
05-24 19:06:32.259  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/app.grapheneos.networklocation.xml
05-24 19:06:32.259  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/DeviceHealthServices.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.259  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/ExtraFiles.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.259  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/org.apache.http.legacy.xml
05-24 19:06:32.259  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.live_wallpaper.xml
05-24 19:06:32.260  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/GooglePlayStore.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.260  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/privapp-permissions-mediatek.xml
05-24 19:06:32.260  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/GBoard.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.260  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/GoogleKeep.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.260  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/GoogleRestore.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.261  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.nfc_extras.xml
05-24 19:06:32.261  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.networkstack.xml
05-24 19:06:32.261  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.credentials.xml
05-24 19:06:32.261  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/privapp-permissions-platform.xml
05-24 19:06:32.263  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/android.test.mock.xml
05-24 19:06:32.264  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/android.test.base.xml
05-24 19:06:32.264  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.webview.xml
05-24 19:06:32.264  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/android.hardware.biometrics.face.xml
05-24 19:06:32.264  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/GoogleMessages.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.265  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/app.grapheneos.logviewer.xml
05-24 19:06:32.265  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/GoogleServicesFramework.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.265  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/GoogleCalendarSyncAdapter.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.265  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/javax.obex.xml
05-24 19:06:32.265  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.location.provider.xml
05-24 19:06:32.266  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/GoogleLocationHistory.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.266  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/android.test.runner.xml
05-24 19:06:32.266  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/GooglePhotos.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.266  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.media.remotedisplay.xml
05-24 19:06:32.266  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.sip.voip.xml
05-24 19:06:32.267  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.window_magnification.xml
05-24 19:06:32.267  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/GoogleClock.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.267  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/GoogleCalculator.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.267  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.future.usb.accessory.xml
05-24 19:06:32.267  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.mediadrm.signer.xml
05-24 19:06:32.268  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/privapp_whitelist_co.aospa.sense.xml
05-24 19:06:32.268  1400  1561 I SystemConfig: Non-xml file /system/etc/permissions/GmsCore.prop in /system/etc/permissions directory, ignoring
05-24 19:06:32.268  1400  1561 I SystemConfig: Reading permissions from /system/etc/permissions/platform.xml
05-24 19:06:32.270  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.ese.xml
05-24 19:06:32.270  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.xml
05-24 19:06:32.270  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.location.gps.xml
05-24 19:06:32.271  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.multitouch.distinct.xml
05-24 19:06:32.271  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.verified_boot.xml
05-24 19:06:32.271  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.mediatek.hardware.vow.xml
05-24 19:06:32.271  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.accelerometer.xml
05-24 19:06:32.272  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.bluetooth_le.xml
05-24 19:06:32.272  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.flash-autofocus.xml
05-24 19:06:32.272  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.stepcounter.xml
05-24 19:06:32.272  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.gyroscope.xml
05-24 19:06:32.273  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.wifi.passpoint.xml
05-24 19:06:32.273  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.telephony.ims.xml
05-24 19:06:32.273  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.bluetooth.xml
05-24 19:06:32.274  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.raw.xml
05-24 19:06:32.274  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.android.nfc_extras.xml
05-24 19:06:32.274  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.vulkan.version.xml
05-24 19:06:32.275  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.front.xml
05-24 19:06:32.275  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.hifi_sensors.xml
05-24 19:06:32.275  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.opengles.deqp.level.xml
05-24 19:06:32.276  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.xml
05-24 19:06:32.276  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/nfc_features.xml
05-24 19:06:32.276  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.usb.accessory.xml
05-24 19:06:32.277  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.telephony.gsm.xml
05-24 19:06:32.277  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.wifi.xml
05-24 19:06:32.277  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.freeform_window_management.xml
05-24 19:06:32.277  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.hcef.xml
05-24 19:06:32.278  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.midi.xml
05-24 19:06:32.278  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.usb.host.xml
05-24 19:06:32.278  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.fingerprint.xml
05-24 19:06:32.278  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.multitouch.jazzhand.xml
05-24 19:06:32.279  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.multitouch.xml
05-24 19:06:32.279  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.ipsec_tunnels.xml
05-24 19:06:32.279  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.se.omapi.uicc.xml
05-24 19:06:32.279  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.opengles.aep.xml
05-24 19:06:32.280  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.se.omapi.ese.xml
05-24 19:06:32.280  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/handheld_core_hardware.xml
05-24 19:06:32.280  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.hce.xml
05-24 19:06:32.281  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.faketouch.xml
05-24 19:06:32.281  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.light.xml
05-24 19:06:32.281  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.audio.low_latency.xml
05-24 19:06:32.281  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.vulkan.deqp.level.xml
05-24 19:06:32.281  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.vulkan.level.xml
05-24 19:06:32.282  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.xml
05-24 19:06:32.282  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.vulkan.compute.xml
05-24 19:06:32.282  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.wifi.direct.xml
05-24 19:06:32.282  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.nxp.mifare.xml
05-24 19:06:32.283  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.stepdetector.xml
05-24 19:06:32.283  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.compass.xml
05-24 19:06:32.283  1400  1400 I SystemServiceManager: Starting com.android.server.security.FileIntegrityService
05-24 19:06:32.283  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.proximity.xml
05-24 19:06:32.284  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.mediatek.hardware.vow_dsp.xml
05-24 19:06:32.284  1400  1561 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.full.xml
05-24 19:06:32.285  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-overlays.xml
05-24 19:06:32.286  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/GoogleCamera_6gb_or_more_ram.xml
05-24 19:06:32.286  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/nga.xml
05-24 19:06:32.287  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-org.lineageos.glimpse.xml
05-24 19:06:32.287  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-google-install-constraints-package-allowlist.xml
05-24 19:06:32.287  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/quick_tap.xml
05-24 19:06:32.288  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/initial-package-stopped-states-org.lineageos.etar.xml
05-24 19:06:32.288  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/google.xml
05-24 19:06:32.288  1400  1400 I SystemServiceManager: Starting com.android.server.pm.Installer
05-24 19:06:32.288  1400  1561 I SystemConfig: Adding association: com.google.android.as <- com.android.bluetooth.services
05-24 19:06:32.288  1400  1561 I SystemConfig: Adding association: com.google.android.as <- com.google.android.bluetooth.services
05-24 19:06:32.289  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/pixel_experience_2017.xml
05-24 19:06:32.290  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-voltage-product.xml
05-24 19:06:32.290  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/pixel_2016_exclusive.xml
05-24 19:06:32.291  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/adaptivecharging.xml
05-24 19:06:32.291  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-handheld-product.xml
05-24 19:06:32.291  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/com.android.messaging.allowlist.xml
05-24 19:06:32.292  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-google-hiddenapi-package-whitelist.xml
05-24 19:06:32.292  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/voltage-component-overrides.xml
05-24 19:06:32.293  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-google-staged-installer-whitelist.xml
05-24 19:06:32.293  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/google_build.xml
05-24 19:06:32.293  1400  1400 I SystemServiceManager: Starting com.android.server.os.DeviceIdentifiersPolicyService
05-24 19:06:32.294  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-org.lineageos.etar.xml
05-24 19:06:32.294  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/com.android.deskclock_allowlist.xml
05-24 19:06:32.294  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/dreamliner.xml
05-24 19:06:32.294  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/initial-package-stopped-states-org.lineageos.glimpse.xml
05-24 19:06:32.295  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/initial-package-stopped-states-org.lineageos.aperture.xml
05-24 19:06:32.295  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-preinstalled-packages-product-pixel-2017-and-newer.xml
05-24 19:06:32.295  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/d2d_cable_migration_feature.xml
05-24 19:06:32.296  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-telephony-product.xml
05-24 19:06:32.296  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/org.lineageos.etar.allowlist.xml
05-24 19:06:32.296  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-org.lineageos.aperture.xml
05-24 19:06:32.296  1400  1561 I SystemConfig: Reading permissions from /product/etc/sysconfig/nexus.xml
05-24 19:06:32.297  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.maps.xml
05-24 19:06:32.298  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.widevine.software.drm.xml
05-24 19:06:32.298  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/privapp-permissions-hotword.xml
05-24 19:06:32.298  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/android.software.freeform_window_management.xml
05-24 19:06:32.299  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.gms.xml
05-24 19:06:32.300  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/privapp-permissions-googleapps-turbo.xml
05-24 19:06:32.300  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.wellbeing.xml
05-24 19:06:32.301  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/privapp-permissions-xhotword.xml
05-24 19:06:32.301  1400  1400 I SystemServiceManager: Starting com.android.server.flags.FeatureFlagsService
05-24 19:06:32.301  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.onetimeinitializer.xml
05-24 19:06:32.302  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.dialer.support.xml
05-24 19:06:32.302  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.keep.xml
05-24 19:06:32.302  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-split-permissions-google.xml
05-24 19:06:32.303  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.dialer.xml
05-24 19:06:32.303  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-privapp-permissions-google-p.xml
05-24 19:06:32.303  1400  1400 I SystemServiceManager: Starting com.android.server.uri.UriGrantsManagerService$Lifecycle
05-24 19:06:32.305  1400  1400 I SystemServiceManager: Starting com.android.server.powerstats.PowerStatsService
05-24 19:06:32.306  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.turbo.xml
05-24 19:06:32.307  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.vending.xml
05-24 19:06:32.308  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/privapp_whitelist_com.android.dialer-ext.xml
05-24 19:06:32.308  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.dialer.xml
05-24 19:06:32.309  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.media.effects.xml
05-24 19:06:32.309  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.ims.xml
05-24 19:06:32.310  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.messaging.xml
05-24 19:06:32.310  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/android.software.angle.xml
05-24 19:06:32.311  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.imsserviceentitlement.xml
05-24 19:06:32.311  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.settings.intelligence.xml
05-24 19:06:32.312  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-privapp-permissions-google-se.xml
05-24 19:06:32.313   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.power.stats@1.0::IPowerStats/default in either framework or device VINTF manifest.
05-24 19:06:32.313  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-privapp-permissions-google.xml
05-24 19:06:32.313  1400  1400 E PowerStatsService: Unable to get power.stats HAL service.
05-24 19:06:32.313  1400  1400 E PowerStatsService: nativeInit failed to connect to power.stats HAL
05-24 19:06:32.314  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.contacts.xml
05-24 19:06:32.314  1400  1561 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.restore.xml
05-24 19:06:32.315  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/sysconfig/allowlist_com.stevesoltys.seedvault.xml
05-24 19:06:32.315  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/sysconfig/android.telephony.satellite.xml
05-24 19:06:32.316  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.emergency.xml
05-24 19:06:32.316  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_whitelist_com.android.launcher3-ext.xml
05-24 19:06:32.317  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_allowlist_com.libremobileos.freeform.xml
05-24 19:06:32.317  1400  1400 I HidlServiceManagement: Registered android.frameworks.stats@1.0::IStats/default
05-24 19:06:32.317  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.hotwordenrollment.common.util.xml
05-24 19:06:32.317  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/permissions_com.stevesoltys.seedvault.xml
05-24 19:06:32.318  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_whitelist_org.lineageos.setupwizard.xml
05-24 19:06:32.318  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/androidx.window.sidecar.xml
05-24 19:06:32.318  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.bluetooth.bthelper.xml
05-24 19:06:32.319  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_whitelist_io.chaldeaprjkt.gamespace.xml
05-24 19:06:32.320  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/android.software.theme_picker.xml
05-24 19:06:32.320  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.systemui.xml
05-24 19:06:32.321  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.settings.xml
05-24 19:06:32.321  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.carrierconfig.xml
05-24 19:06:32.321  1400  1400 I SystemServiceManager: Starting com.android.server.permission.access.AccessCheckingService
05-24 19:06:32.322  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/androidx.window.extensions.xml
05-24 19:06:32.322  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_allowlist_com.libremobileos.sidebar.xml
05-24 19:06:32.322  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp-permissions-custom.xml
05-24 19:06:32.322  1400  1400 I SystemServiceManager: Starting com.android.server.wm.ActivityTaskManagerService$Lifecycle
05-24 19:06:32.323  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.launcher3.xml
05-24 19:06:32.323  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.google.android.gsf.xml
05-24 19:06:32.324  1400  1561 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.storagemanager.xml
05-24 19:06:32.325  1400  1561 I SystemConfig: Reading permissions from /apex/com.android.tethering/etc/permissions/permissions.xml
05-24 19:06:32.325  1400  1561 I SystemConfig: Reading permissions from /apex/com.android.cellbroadcast/etc/permissions/com.android.cellbroadcastreceiver.module.xml
05-24 19:06:32.326  1400  1561 I SystemConfig: Reading permissions from /apex/com.android.cellbroadcast/etc/permissions/com.android.cellbroadcastservice.xml
05-24 19:06:32.326  1400  1400 I SystemServiceManager: Starting com.android.server.am.ActivityManagerService$Lifecycle
05-24 19:06:32.327  1400  1561 I SystemConfig: Reading permissions from /apex/com.android.adservices/etc/permissions/com.android.adservices.api.xml
05-24 19:06:32.328  1400  1561 I SystemConfig: Reading permissions from /apex/com.android.nfcservices/etc/permissions/com.android.nfc.xml
05-24 19:06:32.328  1400  1561 I SystemConfig: Reading permissions from /apex/com.android.permission/etc/permissions/com.android.permissioncontroller.xml
05-24 19:06:32.329  1400  1561 I SystemConfig: Reading permissions from /apex/com.android.mediaprovider/etc/permissions/com.android.photopicker.xml
05-24 19:06:32.330  1400  1561 I SystemConfig: Reading permissions from /apex/com.android.mediaprovider/etc/permissions/com.android.providers.media.module.xml
05-24 19:06:32.331  1400  1561 I SystemConfig: Reading permissions from /apex/com.android.extservices/etc/permissions/android.ext_sminus.services.xml
05-24 19:06:32.332  1400  1561 I SystemConfig: Reading permissions from /apex/com.android.devicelock/etc/permissions/com.android.devicelockcontroller.xml
05-24 19:06:32.334  1400  1561 I SystemConfig: Reading permissions from /apex/com.android.ipsec/etc/permissions/android.net.ipsec.ike.xml
05-24 19:06:32.334  1400  1561 I SystemConfig: Reading permissions from /apex/com.android.apex.cts.shim/etc/permissions/signature-permission-allowlist.xml
05-24 19:06:32.336  1400  1561 I SystemConfig: Reading permissions from /apex/com.android.healthfitness/etc/permissions/com.android.healthconnect.controller.xml
05-24 19:06:32.337  1400  1561 I SystemConfig: Reading permissions from /apex/com.android.btservices/etc/permissions/com.android.bluetooth.xml
05-24 19:06:32.338  1400  1561 I incfs   : Initial API level of the device: 30
05-24 19:06:32.340  1400  1568 E system_server: memevent deregister all events failed, failure to initialize
05-24 19:06:32.340  1400  1568 E OomConnection: failed waiting for OOM events: java.lang.RuntimeException: Failed to initialize memevents listener
05-24 19:06:32.422  1458  1465 E RILC-OplusRadioAIDL: oplusRadioServiceAIDL: oplusRadioServiceAIDL[1]->mRadioIndicationOplus == NULL
05-24 19:06:32.481  1400  1428 W android.permission.PermissionManager: Missing ActivityManager; assuming 1047 does not hold android.permission.MANAGE_APP_OPS_MODES
05-24 19:06:32.482  1400  1400 I SystemServiceManager: Starting com.android.server.pm.DataLoaderManagerService
05-24 19:06:32.486  1400  1400 I SystemServiceManager: Starting com.android.server.power.PowerManagerService
05-24 19:06:32.493  1077  1077 I KEYFILE : [INFO   ] CustomCommon.cpp:161 init() ro.vendor.config.oplus.low_ram = 0
05-24 19:06:32.494  1077  1077 I KEYFILE : [INFO   ] CustomCommon.cpp:162 init() vendor.debug.camera.bss.aishutter.weighting = 100,98,96,94,92,90,90,90
05-24 19:06:32.498  1400  1400 I PowerHalLoader: Successfully connected to Power HAL AIDL service.
05-24 19:06:32.499  1077  1077 I KEYFILE : [INFO   ] CustomCommon.cpp:163 init() vendor.debug.tpi.s.semi.run = 0
05-24 19:06:32.499  1077  1077 I KEYFILE : [INFO   ] CustomCommon.cpp:164 init() vendor.debug.camera.FDAsync = true
05-24 19:06:32.499  1077  1077 E KEYFILE : [ERROR   ] CustomMetadata.cpp:375 init() PROP_SYS_CAM_PACKNAME err 0!
05-24 19:06:32.499  1077  1077 I KEYFILE : [INFO   ] CustomerData.cpp:93 init() 0xb400007cf9e33080, size: 288 byte
05-24 19:06:32.501  1400  1400 I SystemServiceManager: Starting com.android.server.power.ThermalManagerService
05-24 19:06:32.506  1400  1400 I SystemServiceManager: Starting com.android.server.recoverysystem.RecoverySystemService$Lifecycle
05-24 19:06:32.508  1400  1400 I SystemServiceManager: Starting com.android.server.lights.LightsService
05-24 19:06:32.512  1400  1400 I SystemServiceManager: Starting com.android.server.display.DisplayManagerService
05-24 19:06:32.523  1400  1400 I SystemServiceManager: Starting phase 100
05-24 19:06:32.529  1400  1557 E DisplayManagerService: Default display is null for info request from uid 1000
05-24 19:06:32.579  1400  1400 I UserManagerService: Upgrading users from userVersion 11 to 11
05-24 19:06:32.715  1400  1400 W android.permission.PermissionManager: Missing ActivityManager; assuming 1000 holds android.permission.SET_PREFERRED_APPLICATIONS
05-24 19:06:32.751  1400  1400 W PackageManager: No package known for package restrictions com.android.adservices
05-24 19:06:32.768  1400  1400 W PackageManager: No package known for package restrictions com.android.permission
05-24 19:06:32.811  1400  1400 W PackageManager: No package known for package restrictions com.android.btservices
05-24 19:06:32.879  1400  1400 W PackageManager: No package known for package restrictions com.android.extservices
05-24 19:06:32.893  1400  1400 W PackageManager: No package known for package restrictions com.android.nfcservices
05-24 19:06:32.899  1400  1400 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.noCutout on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.font.sanfrancisco on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package org.omnirom.omnijaws on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.systemui.clocks.metro on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package in.zeta.android on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.cts.priv.ctsshim on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package org.voltage.theme.font.dosis on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.google.android.youtube on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.uwb.resources on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.nikgapps.overlay.messages on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.corner on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.adservices.api on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.double on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.themepicker on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.rifsxd.ksunext on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package app.grapheneos.gmscompat.config on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.settings on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.truecaller on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.healthconnect.controller on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.manhwabuddy on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.luascans on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.xperia.settings on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.gradicon.android on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.google.android.onetimeinitializer on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.health.connect.backuprestore on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.virtualmachine.res on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.mxtech.videoplayer.pro on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.systemui on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.managedprovisioning.auto_generated_rro_product__ on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package org.omnirom.omnijaws.auto_generated_rro_product__ on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.apkupdater on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.settings on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.narrow on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.systemui on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.systemui on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.settings on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.android on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.documentsui on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.externalstorage on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.server.deviceconfig.resources on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.settings on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.nikgapps.overlay.googlelocationhistory on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.whatsapp on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.android.companiondevicemanager on user 0
05-24 19:06:32.900  1400  1400 W PackageSettings: Missing permission state for package com.coderstory.toolkit on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package io.github.jica98 on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.gourmetscans on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package app.grapheneos.logviewer on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.systemui on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.systemui.auto_generated_rro_product__ on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.google.android.apps.messaging on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.mediatek.engineermode on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.federatedcompute.services on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.android on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.carrierconfig.mt6893 on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package io.chaldeaprjkt.gamespace.auto_generated_rro_product__ on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.lonelycatgames.Xplore on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.system.monet.snowpaintdrop on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.scyllascans on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.nikgapps.overlay.googlephotos on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.xperia.systemui on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package app.komikku on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package jp.pxv.android on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.systemui on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.themepicker on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package net.thunderbird.android on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.systemui.clocks.bignum on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.avatarpicker on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.xayah.databackup.foss on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.systemui on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.font.rookery on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.snowmtl on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.systemui.plugin.globalactions.wallet on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.safetycenter.resources on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package org.zwanoo.android.speedtest on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.system.monet.vivid on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.vending on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.pacprocessor on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.simappdialog on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package org.protonaosp.deviceconfig.auto_generated_rro_product__ on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package eu.darken.sdmse on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.systemui on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.systemui.clocks.growth on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.connectivity.resources on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.hole on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.tall on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.wide on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.ancient.telephonyoverlay on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.networkstack.overlay on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package org.lineageos.glimpse.frameworksbaseoverlay on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.modulemetadata on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.certinstaller on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.carrierconfig on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.launcher on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.lorn.android on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.internal.systemui.navbar.threebutton on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.brave.browser on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.aurorascans on user 0
05-24 19:06:32.901  1400  1400 W PackageSettings: Missing permission state for package com.android.talkback on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.wifi.dialog on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.nikgapps.overlay.gmscore on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.hotwordenrollment.xgoogle on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.oneplusparts.overlay.rm on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package ru.mike.updatelocker on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.launcher on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.launcher on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.philiascans on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package co.aospa.sense.auto_generated_rro_product__ on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.kewnscans on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.shojoscans on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.settings on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package org.lineageos.cupida.frameworkresoverlay on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.android on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package ru.andr7e.deviceinfohw on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.egg on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.launcher3 on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package livio.pack.lang.en_US on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.google.android.trichromelibrary_710306033 on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package org.lineageos.settings.doze.overlay on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.backupconfirm on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.font.fluidsans on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.axiel7.anihyou on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.settings.auto_generated_rro_vendor__ on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package org.voltage.theme.font.opposans on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.systemui.auto_generated_rro_vendor__ on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.google.android.deskclock on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package org.protonaosp.deviceconfig on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.wmods.wppenhacer on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.systemui.clocks.numoverlap on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.statementservice on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.xperia.android on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.google.android.gm on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package org.calyxos.backup.contacts on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.launcher on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.voltageos.colorstub on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.webtoons on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.oplus.doze.overlay_system on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.settings on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.settings.intelligence on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.systemui.clocks.calligraphy on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.systemui.accessibility.accessibilitymenu on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package org.voltage.theme.font.linotte on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.oplus.doze.overlay_systemui on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.themepicker on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package org.adaway on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.zeptoconsumerapp on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.f0x1d.logfox on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.ancient.frameworkresoverlay.mt6893 on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package ru.tech.imageresizershrinker on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.mangademon on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.ancient.systemuioverlay.mt6893 on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package org.lineageos.setupwizard.auto_generated_rro_product__ on user 0
05-24 19:06:32.902  1400  1400 W PackageSettings: Missing permission state for package com.android.sharedstoragebackup on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.launcher on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.printspooler on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.hotwordenrollment.okgoogle on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.emergency.auto_generated_rro_product__ on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.settings on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.dreams.basic on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.settings.overlay.oplus.target on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.launcher on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.providers.settings.auto_generated_rro_product__ on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package org.mozilla.focus on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.photopicker on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.systemui on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.webview on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.permissioncontroller.overlay on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package app.grapheneos.networklocation on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.google.android.apps.wellbeing on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.coffeemanga on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.rkpdapp on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.google.android.dialer on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.launcher on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.bips on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.themepicker on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.settings on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.intentresolver.auto_generated_rro_product__ on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.android on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package org.eu.droid_ng.jellyfish on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.musicfx on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package app.vitune.android on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.google.android.apps.docs on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package ellipi.messenger on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.systemui on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.asurascans on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package app.grapheneos.gmscompat.lib on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package duy.com.text_converter on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.customization.themes on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.font.googlesans on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package net.one97.paytm on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.cellbroadcastreceiver on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.google.android.webview on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package android.ext.shared on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.bluetooth.bthelper.auto_generated_rro_product__ on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.google.android.contactkeys on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.google.android.contacts on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.google.android.syncadapters.contacts on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.system.monet.expresso on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.nikgapps.overlay.googleclock on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package org.calyxos.datura on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.themepicker on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.systemui.clocks.inflate on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.google.android.calculator on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.adultwebtoon on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package org.voltage.theme.font.manrope on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.printservice.recommendation on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package app.grapheneos.AppCompatConfig on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package me.jmh.authenticatorpro on user 0
05-24 19:06:32.903  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.systemui on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.mangadex on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.kaiscans on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.google.android.gms on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.google.android.ims on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.system.theme.black on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package android.ext.services on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.wifi.resources on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.plumpy.systemui on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.cameraextensions on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.packageinstaller on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.carrierdefaultapp on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.magusmanga on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.gradicon.systemui on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.necroscans on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.font.opsans on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.batoto on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.credentialmanager on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.android on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.font.notoserifsource on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.android on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.proxyhandler on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.launcher3.auto_generated_rro_product__ on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.waterfall on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.intentresolver on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.systemui on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package io.github.muntashirakon.AppManager on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.internal.systemui.navbar.transparent on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.providers.settings.overlay on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.android on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.google.android.apps.photos on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.android on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.managedprovisioning on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.aeinscans on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package io.github.dovecoteescapee.byedpi on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.lorn.systemui on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.dreams.phototable on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package org.lineageos.settings.doze.auto_generated_rro_product__ on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.networkstack.tethering.mt6893 on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.launcher on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package org.voltage.theme.font.recursive_casual on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package air.kukulive.mailnow on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.looker.droidify on user 0
05-24 19:06:32.904  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.android on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.smspush on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.role.notes.enabled on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.berdik.letmedowngrade on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.wallpaper.livepicker on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package org.lineageos.aperture on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.cellbroadcastreceiver.module on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.systemui.clocks.flex on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.apps.tag on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.inputmethod.latin.auto_generated_rro_product__ on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.power.hub.udfps.icons on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.imsserviceentitlement on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.appsearch.apk on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.launcher on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.valirscans on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package org.voltage.theme.font.recursive_linear on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.avoidAppsInCutout on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package org.lineageos.cupida.wifioverlay on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package idm.internet.download.manager.plus on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.plumpy.android on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.arvenscans on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.melody on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.android on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.storagemanager on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.zerodha.kite3 on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.bookmarkprovider on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.fitbit.FitbitMobile on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.aurora.systemui on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.launcher on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package uk.akane.omni on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package org.protonaosp.theme.font.linotte on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.google.android.apps.googlecamera.fishfood on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.voltage.overlay.customization.keyboard.nonavbar on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.google.android.apps.turbo on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.enryumanga on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.google.android.safetycore on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.whalemanga on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package app.grapheneos.gmscompat on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.themepicker on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.wifi.resources.mt6893 on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package proton.android.pass on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.launcher on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.wallpaper on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.nikgapps.overlay.turbo on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.android.vpndialogs on user 0
05-24 19:06:32.905  1400  1400 W PackageSettings: Missing permission state for package com.goping.user on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.nyxscans on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.google.android.keep on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.angle on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.mangareadorg on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.linkbox.plus.android on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.themepicker on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.sdksandbox on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.wallpaperbackup on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.stevesoltys.seedvault.auto_generated_rro_product__ on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.settings.auto_generated_rro_product__ on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.voltageos.Covers on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.providers.media.module on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.power.hub.udfps.animations on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package in.swiggy.android on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.hotspot2.osulogin on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.solarmtl on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.google.android.gms.location.history on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.intsig.camscanner on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.internal.systemui.navbar.gestural on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package co.aospa.sense.settings.overlay on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.themepicker on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.wstxda.viper4android on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.harimanga on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.mangadistrict on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.bluetoothmidiservice on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.ancient.settingsoverlay.mt6893 on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package org.akanework.gramophone on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.permissioncontroller on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.storagemanager.auto_generated_rro_product__ on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.zerodha.coin on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.dialer.auto_generated_rro_vendor__ on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package app.customerportal.tachyon1 on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.imsserviceentitlement.auto_generated_rro_product__ on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.phone.auto_generated_rro_product__ on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package android.auto_generated_rro_product__ on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.ezmanga on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.templescan on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.dialer.auto_generated_rro_product__ on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.settings on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.themepicker on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.ondevicepersonalization.services on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.documentsui.overlay on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.anisascans on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.captiveportallogin on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.aurora.android on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.devicelockcontroller on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.settings on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.tukann.confinedandhorny on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.settings on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.likemanga on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.nikgapps.overlay.wellbeing on user 0
05-24 19:06:32.906  1400  1400 W PackageSettings: Missing permission state for package com.nikgapps.overlay.dialer on user 0
05-24 19:06:32.907  1400  1400 W PackageSettings: Missing permission state for package com.google.android.inputmethod.latin on user 0
05-24 19:06:32.907  1400  1400 W PackageSettings: Missing permission state for package org.lineageos.aperture.frameworksbaseoverlay on user 0
05-24 19:06:32.907  1400  1400 W PackageSettings: Missing permission state for package com.nikgapps.overlay.contacts on user 0
05-24 19:06:32.907  1400  1400 W PackageSettings: Missing permission state for package android.auto_generated_rro_vendor__ on user 0
05-24 19:06:32.907  1400  1400 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.android on user 0
05-24 19:06:32.907  1400  1400 W PackageSettings: Missing permission state for package com.google.android.apps.restore on user 0
05-24 19:06:32.907  1400  1400 W PackageSettings: Missing permission state for shared user: android.uid.log
05-24 19:06:32.907  1400  1400 W PackageSettings: Missing permission state for shared user: android.uid.uwb
05-24 19:06:33.021  1400  1400 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-24 19:06:33.035  1400  1400 I PackageManager: /system/apex/com.android.btservices.apex changed; collecting certs
05-24 19:06:33.046  1055  1102 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 19:06:33.046  1055  1102 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:4
05-24 19:06:33.050  1400  1400 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-24 19:06:33.070  1400  1400 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-24 19:06:33.079  1400  1400 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-24 19:06:33.127  1191  1191 I LSPosedService: service package is not started, wait 1s.
05-24 19:06:33.148  1400  1423 I system_server: Compiler allocated 4688KB to compile com.android.server.pm.ScanResult com.android.server.pm.ScanPackageUtils.scanPackageOnly(com.android.server.pm.ScanRequest, com.android.server.pm.PackageManagerServiceInjector, boolean, long)
05-24 19:06:33.377  1400  1589 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 19:06:33.377  1400  1589 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 19:06:33.378  1400  1589 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 19:06:33.397  1400  1589 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 19:06:33.407  1400  1589 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 19:06:33.407  1400  1589 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 19:06:33.436  1400  1590 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.CAMERA in package: com.mediatek.engineermode at: Binary XML file line #30
05-24 19:06:33.487  1458  1465 E RadioConfig_service: radioConfigService[0] or mRadioConfigIndication is NULL
05-24 19:06:33.516  1400  1589 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.SATELLITE_COMMUNICATION in package: com.android.shell at: Binary XML file line #775
05-24 19:06:33.518  1400  1589 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE in package: com.android.shell at: Binary XML file line #874
05-24 19:06:33.518  1400  1589 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE_SYSTEM_EXEMPTED in package: com.android.shell at: Binary XML file line #875
05-24 19:06:33.547  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.POST_NOTIFICATIONS in package: com.android.phone at: Binary XML file line #171
05-24 19:06:33.635  1596  1596 I HidlServiceManagement: Registered android.hardware.audio@7.1::IDevicesFactory/default
05-24 19:06:33.635  1596  1596 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-24 19:06:33.636  1596  1596 I LegacySupport: Registration complete for android.hardware.audio@7.1::IDevicesFactory/default.
05-24 19:06:33.643  1596  1596 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-24 19:06:33.643  1596  1596 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-24 19:06:33.652  1596  1596 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 19:06:33.733  1400  1400 W PackageManager: Failed to scan /product/priv-app/CarrierServices: Package com.google.android.ims at /product/priv-app/CarrierServices ignored: updated version 31144015 better than this 30939330
05-24 19:06:33.750  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.750  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.750  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.750  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.750  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.750  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.751  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.751  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.751  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.751  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.751  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.751  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.751  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.751  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.751  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.751  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.751  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.752  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.788  1603  1603 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 19:06:33.789  1603  1603 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 19:06:33.790   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 19:06:33.803  1603  1603 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 19:06:33.804   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 19:06:33.805  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.805  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.805  1603  1603 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 19:06:33.809  1596  1596 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 19:06:33.811  1400  1428 W android.permission.PermissionManager: Missing ActivityManager; assuming 1041 does not hold android.permission.UPDATE_DEVICE_STATS
05-24 19:06:33.812  1400  1428 W Binder  : java.lang.SecurityException: Access denied, requires: android.permission.UPDATE_DEVICE_STATS
05-24 19:06:33.812  1400  1428 W Binder  : 	at android.os.PermissionEnforcer.enforcePermission(PermissionEnforcer.java:146)
05-24 19:06:33.812  1400  1428 W Binder  : 	at com.android.internal.app.IBatteryStats$Stub.noteResetAudio_enforcePermission(IBatteryStats.java:3472)
05-24 19:06:33.812  1400  1428 W Binder  : 	at com.android.server.am.BatteryStatsService.noteResetAudio(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:1)
05-24 19:06:33.813  1603  1603 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-24 19:06:33.830  1596  1596 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x477c59a7)
05-24 19:06:33.836  1596  1596 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x394351e1)
05-24 19:06:33.838  1596  1596 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 19:06:33.839  1596  1596 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xb2e50abb)
05-24 19:06:33.841  1596  1596 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0xc0753df7)
05-24 19:06:33.842  1603  1603 E libxml2 : I/O warning : failed to load "/odm/etc/virtual_audio_policy_configuration.xml": No such file or directory
05-24 19:06:33.842  1603  1603 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-24 19:06:33.842  1603  1603 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-24 19:06:33.843  1596  1596 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-24 19:06:33.851  1596  1602 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-24 19:06:33.852  1596  1602 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-24 19:06:33.853   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 19:06:33.854  1596  1596 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-24 19:06:33.855   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 19:06:33.855  1596  1596 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-24 19:06:33.855  1596  1602 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 19:06:33.856   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 19:06:33.856  1596  1596 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-24 19:06:33.856  1596  1596 W audiohalservice: Could not register Bluetooth Audio API
05-24 19:06:33.857   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-24 19:06:33.857  1596  1596 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-24 19:06:33.857  1596  1596 W audiohalservice: Could not register Bluetooth Audio Offload API
05-24 19:06:33.867  1596  1596 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-24 19:06:33.868  1596  1596 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-24 19:06:33.868  1596  1596 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-24 19:06:33.919  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.919  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.919  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.919  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.919  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.919  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.919  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.919  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.920  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.920  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.920  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.920  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.920  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.920  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.920  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.921  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.921  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.921  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.991  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.991  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:33.994  1596  1602 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 19:06:34.020  1596  1602 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x57e8a167)
05-24 19:06:34.022  1596  1613 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-24 19:06:34.024  1064  1068 W ServiceManagerCppClient: Service package_native didn't start. Returning NULL
05-24 19:06:34.025  1064  1068 E ServiceUtilities: getCachedInfo: Cannot find package_native
05-24 19:06:34.025  1596  1613 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0x57e8a167)
05-24 19:06:34.026  1596  1613 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0x57e8a167)
05-24 19:06:34.046  1055  1102 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 19:06:34.046  1055  1102 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:5
05-24 19:06:34.089  1596  1602 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-24 19:06:34.095  1596  1602 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 19:06:34.114  1596  1602 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-24 19:06:34.115  1077  1077 I mtkcam-devicemgr: [initialize] -
05-24 19:06:34.115  1077  1077 I mtkcam-camprovider: [initialize] +
05-24 19:06:34.115  1077  1077 I mtkcam-camprovider: [initialize] -
05-24 19:06:34.117  1077  1077 I HidlServiceManagement: Registered android.hardware.camera.provider@2.6::ICameraProvider/internal/0
05-24 19:06:34.125  1056  1195 I CameraService: onDeviceStatusChanged: Status changed for cameraId=4, newStatus=1
05-24 19:06:34.125  1056  1195 I CameraService: onDeviceStatusChanged: Unknown camera ID 4, a new camera is added
05-24 19:06:34.126  1056  1195 I CameraService: onDeviceStatusChanged: Status changed for cameraId=3, newStatus=1
05-24 19:06:34.126  1056  1195 I CameraService: onDeviceStatusChanged: Unknown camera ID 3, a new camera is added
05-24 19:06:34.126  1056  1195 I CameraService: onDeviceStatusChanged: Status changed for cameraId=2, newStatus=1
05-24 19:06:34.126  1056  1195 I CameraService: onDeviceStatusChanged: Unknown camera ID 2, a new camera is added
05-24 19:06:34.126  1056  1195 I CameraService: onDeviceStatusChanged: Status changed for cameraId=1, newStatus=1
05-24 19:06:34.126  1056  1195 I CameraService: onDeviceStatusChanged: Unknown camera ID 1, a new camera is added
05-24 19:06:34.126  1056  1195 I CameraService: onDeviceStatusChanged: Status changed for cameraId=0, newStatus=1
05-24 19:06:34.126  1056  1195 I CameraService: onDeviceStatusChanged: Unknown camera ID 0, a new camera is added
05-24 19:06:34.129  1191  1191 I LSPosedService: service package is not started, wait 1s.
05-24 19:06:34.130  1596  1602 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 19:06:34.130  1596  1602 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 19:06:34.130  1596  1602 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 19:06:34.136  1596  1602 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 19:06:34.136  1596  1602 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 19:06:34.137  1596  1602 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-24 19:06:34.139  1077  1077 I HidlServiceManagement: Registered vendor.mediatek.hardware.camera.isphal@1.0::IISPModule/internal/0
05-24 19:06:34.140  1596  1602 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-24 19:06:34.140  1596  1602 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-24 19:06:34.141  1603  1603 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 19:06:34.141  1603  1603 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 19:06:34.142  1596  1596 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-24 19:06:34.142  1596  1596 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-24 19:06:34.142  1596  1596 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 19:06:34.142  1603  1603 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-24 19:06:34.143  1603  1603 I AudioFlinger: openOutput() this 0xb400007310a198a0, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-24 19:06:34.143  1603  1603 I AudioFlinger: operator(): Using 3000 ms as standby time
05-24 19:06:34.143  1603  1603 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 19:06:34.144  1603  1603 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 19:06:34.144  1077  1077 I MtkCam/BGService: IBGService  into HIDL_FETCH_IBGService
05-24 19:06:34.144   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 19:06:34.145  1603  1603 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 19:06:34.150  1077  1077 I HidlServiceManagement: Registered vendor.mediatek.hardware.camera.bgservice@1.1::IBGService/internal/0
05-24 19:06:34.150  1077  1077 I LegacySupport: Registration complete for vendor.mediatek.hardware.camera.bgservice@1.1::IBGService/internal/0.
05-24 19:06:34.158  1603  1635 I AudioFlinger: AudioFlinger's thread 0xb4000075157bb760 tid=1635 ready to run
05-24 19:06:34.166  1077  1077 I HidlServiceManagement: Registered vendor.mediatek.hardware.camera.atms@1.0::IATMs/default
05-24 19:06:34.180  1596  1616 W audio_messenger_ipi: read_ipi_thread(), read dsp fail!! length_read: 0, errno: 0
05-24 19:06:34.185  1639  1639 E DEBUG   : failed to read process info: failed to open /proc/1596: No such file or directory
05-24 19:06:34.220  1066  1066 W ServiceManagerCppClient: Service package_native didn't start. Returning NULL
05-24 19:06:34.220  1066  1066 E storaged: getService package_native failed
05-24 19:06:34.229  1066  1644 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder'...
05-24 19:06:34.238  1639  1639 F DEBUG   : Cmdline: /vendor/bin/hw/android.hardware.audio.service
05-24 19:06:34.238  1639  1639 F DEBUG   : pid: 1596, tid: 1632, name: HwBinder:1596_3  >>> /vendor/bin/hw/android.hardware.audio.service <<<
05-24 19:06:34.238  1639  1639 F DEBUG   :       #01 pc 0002651d  /vendor/lib/hw/<EMAIL> (android::hardware::audio::V7_1::implementation::StreamOut::setLatencyModeCallback(android::sp<android::hardware::audio::V7_1::IStreamOutLatencyModeCallback> const&)+36) (BuildId: 544a581a8a5464266e14c74f5aea9a1c)
05-24 19:06:34.238  1639  1639 F DEBUG   :       #02 pc 00043c0f  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::_hidl_setLatencyModeCallback(android::hidl::base::V1_0::BnHwBase*, android::hardware::Parcel const&, android::hardware::Parcel*, std::__1::function<void (android::hardware::Parcel&)>)+198) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 19:06:34.238  1639  1639 F DEBUG   :       #03 pc 00044703  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::onTransact(unsigned int, android::hardware::Parcel const&, android::hardware::Parcel*, unsigned int, std::__1::function<void (android::hardware::Parcel&)>)+1926) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 19:06:34.496  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.CONFIGURE_WIFI_DISPLAY in package: com.android.systemui at: Binary XML file line #174
05-24 19:06:34.497  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.GET_RUNTIME_PERMISSIONS in package: com.android.systemui at: Binary XML file line #252
05-24 19:06:34.499  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.READ_DEVICE_CONFIG in package: com.android.systemui at: Binary XML file line #372
05-24 19:06:34.499  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.MODIFY_AUDIO_SETTINGS in package: com.android.systemui at: Binary XML file line #405
05-24 19:06:34.500  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FORCE_STOP_PACKAGES in package: com.android.systemui at: Binary XML file line #426
05-24 19:06:34.678  1400  1400 I ApexManager: Registering com.android.ondevicepersonalization.services as apk-in-apex of com.android.ondevicepersonalization
05-24 19:06:34.686  1400  1400 I ApexManager: Registering com.android.federatedcompute.services as apk-in-apex of com.android.ondevicepersonalization
05-24 19:06:34.714  1400  1589 W PackageParsing: com.android.healthconnect.controller defines a background permission. Only the 'android' packages can do that.
05-24 19:06:34.714  1400  1589 W PackageParsing: com.android.healthconnect.controller defines a background permission. Only the 'android' packages can do that.
05-24 19:06:34.714  1400  1589 W PackageParsing: com.android.healthconnect.controller defines a background permission. Only the 'android' packages can do that.
05-24 19:06:34.747  1400  1400 I ApexManager: Registering com.android.safetycenter.resources as apk-in-apex of com.android.permission
05-24 19:06:34.790  1400  1400 I ApexManager: Registering com.android.permissioncontroller as apk-in-apex of com.android.permission
05-24 19:06:34.802  1400  1400 I ApexManager: Registering com.android.cellbroadcastservice as apk-in-apex of com.android.cellbroadcast
05-24 19:06:34.862  1400  1400 I ApexManager: Registering com.android.bluetooth as apk-in-apex of com.android.btservices
05-24 19:06:35.046  1055  1102 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 19:06:35.046  1055  1102 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:6
05-24 19:06:35.130  1191  1191 I LSPosedService: service package is not started, wait 1s.
05-24 19:06:35.211  1400  1590 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.RECEIVE_BOOT_COMPLETED in package: com.android.adservices.api at: Binary XML file line #159
05-24 19:06:35.219  1400  1400 I ApexManager: Registering com.android.adservices.api as apk-in-apex of com.android.adservices
05-24 19:06:35.231  1400  1400 I ApexManager: Registering com.android.sdksandbox as apk-in-apex of com.android.adservices
05-24 19:06:35.300  1400  1400 I ApexManager: Registering android.ext.services as apk-in-apex of com.android.extservices
05-24 19:06:35.354  1400  1590 W PackageParsing: Unknown element under <manifest>: meta-data at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #102
05-24 19:06:35.354  1400  1590 W PackageParsing: Unknown element under <manifest>: provider at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #106
05-24 19:06:35.354  1400  1590 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #116
05-24 19:06:35.354  1400  1590 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #122
05-24 19:06:35.354  1400  1590 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #129
05-24 19:06:35.354  1400  1590 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #135
05-24 19:06:35.354  1400  1590 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #140
05-24 19:06:35.354  1400  1590 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #147
05-24 19:06:35.354  1400  1590 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #153
05-24 19:06:35.354  1400  1590 W PackageParsing: Unknown element under <manifest>: receiver at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #158
05-24 19:06:35.354  1400  1590 W PackageParsing: Unknown element under <manifest>: service at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #166
05-24 19:06:35.354  1400  1590 W PackageParsing: Unknown element under <manifest>: provider at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #168
05-24 19:06:35.354  1400  1590 W PackageParsing: Unknown element under <manifest>: receiver at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #177
05-24 19:06:35.361  1400  1400 I ApexManager: Registering com.android.nfc as apk-in-apex of com.android.nfcservices
05-24 19:06:35.575  1400  1589 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.MANAGE_OWN_CALLS in package: com.truecaller at: Binary XML file line #141
05-24 19:06:35.587  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.READ_EXTERNAL_STORAGE in package: ellipi.messenger at: Binary XML file line #111
05-24 19:06:35.588  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WRITE_EXTERNAL_STORAGE in package: ellipi.messenger at: Binary XML file line #179
05-24 19:06:35.678  1400  1588 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.INTERNET in package: com.intsig.camscanner at: Binary XML file line #34
05-24 19:06:35.678  1400  1590 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #9
05-24 19:06:35.678  1400  1590 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #10
05-24 19:06:35.678  1400  1590 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #11
05-24 19:06:35.678  1400  1590 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #12
05-24 19:06:36.046  1055  1102 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 19:06:36.046  1055  1102 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:7
05-24 19:06:36.128  1085  1420 W mtk_agpsd: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.system.suspend.ISystemSuspend/default
05-24 19:06:36.131  1191  1191 I LSPosedService: service package is not started, wait 1s.
05-24 19:06:36.329   872   884 W ServiceManagerCppClient: Service statscompanion didn't start. Returning NULL
05-24 19:06:36.329   872   884 E statsd  : Uid 1000 does not have the android.permission.REGISTER_STATS_PULL_ATOM permission when registering atom 10205 (-1)
05-24 19:06:37.030  1400  1589 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 19:06:37.031  1400  1589 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 19:06:37.032  1400  1589 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 19:06:37.033  1400  1589 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #34
05-24 19:06:37.034  1400  1589 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 19:06:37.035  1400  1589 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 19:06:37.036  1400  1589 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 19:06:37.047  1055  1102 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 19:06:37.047  1055  1102 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:8
05-24 19:06:37.132  1191  1191 I LSPosedService: service package is not started, wait 1s.
05-24 19:06:37.220  1400  1588 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.USE_FINGERPRINT in package: org.mozilla.focus at: Binary XML file line #73
05-24 19:06:37.278  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.ACCESS_NETWORK_STATE in package: com.google.android.gm at: Binary XML file line #311
05-24 19:06:37.278  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE in package: com.google.android.gm at: Binary XML file line #314
05-24 19:06:37.278  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE_DATA_SYNC in package: com.google.android.gm at: Binary XML file line #315
05-24 19:06:37.278  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.INTERNET in package: com.google.android.gm at: Binary XML file line #316
05-24 19:06:37.278  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.RECEIVE_BOOT_COMPLETED in package: com.google.android.gm at: Binary XML file line #317
05-24 19:06:37.279  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.VIBRATE in package: com.google.android.gm at: Binary XML file line #318
05-24 19:06:37.279  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WAKE_LOCK in package: com.google.android.gm at: Binary XML file line #319
05-24 19:06:37.279  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WRITE_EXTERNAL_STORAGE in package: com.google.android.gm at: Binary XML file line #321
05-24 19:06:37.279  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.POST_NOTIFICATIONS in package: com.google.android.gm at: Binary XML file line #322
05-24 19:06:37.279  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.c2dm.permission.RECEIVE in package: com.google.android.gm at: Binary XML file line #324
05-24 19:06:37.279  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.GET_PACKAGE_SIZE in package: com.google.android.gm at: Binary XML file line #325
05-24 19:06:37.279  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.providers.gsf.permission.READ_GSERVICES in package: com.google.android.gm at: Binary XML file line #326
05-24 19:06:37.279  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.providers.gsf.permission.READ_GSERVICES in package: com.google.android.gm at: Binary XML file line #331
05-24 19:06:37.279  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.GET_PACKAGE_SIZE in package: com.google.android.gm at: Binary XML file line #332
05-24 19:06:37.280  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.ACCESS_NETWORK_STATE in package: com.google.android.gm at: Binary XML file line #334
05-24 19:06:37.280  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.CAMERA in package: com.google.android.gm at: Binary XML file line #345
05-24 19:06:37.281  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE in package: com.google.android.gm at: Binary XML file line #360
05-24 19:06:37.281  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.INTERNET in package: com.google.android.gm at: Binary XML file line #361
05-24 19:06:37.281  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.RECORD_AUDIO in package: com.google.android.gm at: Binary XML file line #363
05-24 19:06:37.281  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WAKE_LOCK in package: com.google.android.gm at: Binary XML file line #364
05-24 19:06:37.282  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.POST_NOTIFICATIONS in package: com.google.android.gm at: Binary XML file line #373
05-24 19:06:37.282  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE_DATA_SYNC in package: com.google.android.gm at: Binary XML file line #379
05-24 19:06:37.283  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.gm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION in package: com.google.android.gm at: Binary XML file line #385
05-24 19:06:37.283  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.VIBRATE in package: com.google.android.gm at: Binary XML file line #386
05-24 19:06:37.283  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.READ_CONTACTS in package: com.google.android.gm at: Binary XML file line #387
05-24 19:06:37.283  1400  1591 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.hangouts.START_HANGOUT in package: com.google.android.gm at: Binary XML file line #388
05-24 19:06:38.047  1055  1102 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 19:06:38.047  1055  1102 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:9
05-24 19:06:38.133  1191  1191 I LSPosedService: service package is not started, wait 1s.
05-24 19:06:38.645  1692  1692 I HidlServiceManagement: Registered android.hardware.audio@7.1::IDevicesFactory/default
05-24 19:06:38.646  1692  1692 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-24 19:06:38.646  1692  1692 I LegacySupport: Registration complete for android.hardware.audio@7.1::IDevicesFactory/default.
05-24 19:06:38.652  1692  1692 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-24 19:06:38.652  1692  1692 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-24 19:06:38.667  1692  1692 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 19:06:38.747  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.747  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.748  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.748  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.748  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.748  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.748  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.748  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.748  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.749  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.749  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.749  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.749  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.749  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.749  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.749  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.749  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.749  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.765  1699  1699 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 19:06:38.766  1699  1699 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 19:06:38.766   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 19:06:38.780  1699  1699 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 19:06:38.780   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 19:06:38.781  1699  1699 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 19:06:38.787  1400  1428 W android.permission.PermissionManager: Missing ActivityManager; assuming 1041 does not hold android.permission.UPDATE_DEVICE_STATS
05-24 19:06:38.788  1699  1699 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-24 19:06:38.788  1400  1428 W Binder  : java.lang.SecurityException: Access denied, requires: android.permission.UPDATE_DEVICE_STATS
05-24 19:06:38.788  1400  1428 W Binder  : 	at android.os.PermissionEnforcer.enforcePermission(PermissionEnforcer.java:146)
05-24 19:06:38.788  1400  1428 W Binder  : 	at com.android.internal.app.IBatteryStats$Stub.noteResetAudio_enforcePermission(IBatteryStats.java:3472)
05-24 19:06:38.788  1400  1428 W Binder  : 	at com.android.server.am.BatteryStatsService.noteResetAudio(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:1)
05-24 19:06:38.796  1699  1699 E libxml2 : I/O warning : failed to load "/odm/etc/virtual_audio_policy_configuration.xml": No such file or directory
05-24 19:06:38.796  1699  1699 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-24 19:06:38.796  1699  1699 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-24 19:06:38.799  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.799  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.803  1692  1692 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 19:06:38.824  1692  1692 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x649962d1)
05-24 19:06:38.826  1400  1400 W AppIdPermissionPolicy: Ignoring permission com.google.android.gtalkservice.permission.GTALK_SERVICE declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-24 19:06:38.826  1400  1400 W AppIdPermissionPolicy: Ignoring permission com.android.vending.INTENT_VENDING_ONLY declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-24 19:06:38.826  1400  1400 W AppIdPermissionPolicy: Ignoring permission com.google.android.providers.settings.permission.WRITE_GSETTINGS declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-24 19:06:38.826  1400  1400 W AppIdPermissionPolicy: Ignoring permission com.google.android.providers.gsf.permission.WRITE_GSERVICES declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-24 19:06:38.826  1400  1400 W AppIdPermissionPolicy: Ignoring permission lineageos.permission.MANAGE_REMOTE_PREFERENCES declared in system package com.android.settings: already declared in another system package io.chaldeaprjkt.gamespace
05-24 19:06:38.829  1400  1400 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_TOPICS declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 19:06:38.829  1400  1400 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_ATTRIBUTION declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 19:06:38.829  1400  1400 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 19:06:38.829  1400  1400 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_AD_SELECTION declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 19:06:38.829  1400  1400 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_PROTECTED_SIGNALS declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 19:06:38.829  1400  1400 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_AD_ID declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 19:06:38.830  1692  1692 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0xd275ee2d)
05-24 19:06:38.839  1692  1692 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 19:06:38.840  1692  1698 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-24 19:06:38.841  1692  1698 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-24 19:06:38.842  1692  1692 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x3dd68bdf)
05-24 19:06:38.843  1692  1692 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x74486f9f)
05-24 19:06:38.846  1692  1692 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-24 19:06:38.847   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 19:06:38.848  1692  1692 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-24 19:06:38.848   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 19:06:38.849  1692  1692 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-24 19:06:38.849  1692  1698 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 19:06:38.849   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 19:06:38.849  1692  1692 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-24 19:06:38.849  1692  1692 W audiohalservice: Could not register Bluetooth Audio API
05-24 19:06:38.850   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-24 19:06:38.850  1692  1692 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-24 19:06:38.850  1692  1692 W audiohalservice: Could not register Bluetooth Audio Offload API
05-24 19:06:38.863  1692  1692 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-24 19:06:38.863  1692  1692 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-24 19:06:38.863  1692  1692 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-24 19:06:38.932  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.932  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.932  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.932  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.932  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.932  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.932  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.932  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.932  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.933  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.933  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.933  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.933  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.933  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.933  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.933  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.933  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.934  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.940  1400  1400 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.adservices/appid
05-24 19:06:38.941  1400  1400 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.permission/appid
05-24 19:06:38.945  1400  1400 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.btservices/appid
05-24 19:06:38.953  1400  1400 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.extservices/appid
05-24 19:06:38.957  1400  1400 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.nfcservices/appid
05-24 19:06:38.984  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.984  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:38.988  1692  1698 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 19:06:39.011  1692  1698 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xdc360c03)
05-24 19:06:39.011  1692  1711 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-24 19:06:39.013  1692  1711 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0xdc360c03)
05-24 19:06:39.013  1692  1711 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0xdc360c03)
05-24 19:06:39.055  1400  1400 I SystemServiceManager: Starting com.android.server.pm.UserManagerService$LifeCycle
05-24 19:06:39.071  1066  1644 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder' successful after waiting 4841ms
05-24 19:06:39.077  1692  1698 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-24 19:06:39.081  1692  1698 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 19:06:39.101  1692  1698 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-24 19:06:39.115  1692  1698 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 19:06:39.115  1692  1698 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 19:06:39.115  1692  1698 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 19:06:39.120  1692  1698 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 19:06:39.121  1692  1698 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 19:06:39.121  1692  1698 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-24 19:06:39.127  1692  1698 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-24 19:06:39.127  1692  1698 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-24 19:06:39.129  1699  1699 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 19:06:39.129  1699  1699 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 19:06:39.131  1692  1692 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-24 19:06:39.131  1692  1692 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-24 19:06:39.131  1692  1692 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 19:06:39.132  1699  1699 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-24 19:06:39.133  1699  1699 I AudioFlinger: openOutput() this 0xb4000077aaee31f0, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-24 19:06:39.138  1699  1699 I AudioFlinger: operator(): Using 3000 ms as standby time
05-24 19:06:39.138  1699  1699 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 19:06:39.145  1699  1699 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 19:06:39.146   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 19:06:39.147  1699  1699 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 19:06:39.154  1699  1725 I AudioFlinger: AudioFlinger's thread 0xb4000079aceb0760 tid=1725 ready to run
05-24 19:06:39.155  1699  1725 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:39.155  1699  1699 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:39.157  1699  1725 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:39.157  1699  1699 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:39.157  1699  1699 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:39.157  1699  1699 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:39.157  1699  1699 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:39.157  1699  1699 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:39.157  1699  1699 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:39.157  1699  1699 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:39.157  1699  1699 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:39.157  1699  1699 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:39.157  1699  1699 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:39.157  1699  1699 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:39.157  1699  1699 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:39.157  1699  1699 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:39.157  1699  1699 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:39.157  1699  1699 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:39.157  1699  1699 W AudioFlinger: moveEffects() AUDIO_SESSION_OUTPUT_MIX not found in orphans, checking other mix
05-24 19:06:39.157  1699  1699 W AudioFlinger: moveEffects() bad srcIo 0
05-24 19:06:39.158  1699  1699 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 19:06:39.158  1191  1191 E LSPosedService: failed to change feature flags
05-24 19:06:39.158  1191  1191 E LSPosedService: java.lang.NoSuchFieldException: No field systemui_is_cached in class Landroid/app/FeatureFlagsImpl; (declaration of 'android.app.FeatureFlagsImpl' appears in /system/framework/framework.jar)
05-24 19:06:39.158  1191  1191 E LSPosedService: 	at java.lang.Class.getDeclaredField(Native Method)
05-24 19:06:39.158  1191  1191 E LSPosedService: 	at org.lsposed.lspd.Main.main(Unknown Source:411)
05-24 19:06:39.158  1191  1191 E LSPosedService: 	at com.android.internal.os.RuntimeInit.nativeFinishInit(Native Method)
05-24 19:06:39.158  1191  1191 E LSPosedService: 	at com.android.internal.os.RuntimeInit.main(RuntimeInit.java:375)
05-24 19:06:39.165  1191  1191 I LSPosedService: sent service to bridge
05-24 19:06:39.179  1692  1698 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x2, output_devices == current_output_devices(0x00000002), return
05-24 19:06:39.181  1699  1699 I AudioFlinger: openOutput() this 0xb4000077aaee31f0, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x8
05-24 19:06:39.181  1692  1698 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 19:06:39.185  1699  1699 I AudioFlinger: HAL output buffer size 2048 frames, normal sink buffer size 2048 frames
05-24 19:06:39.187  1699  1729 I AudioFlinger: AudioFlinger's thread 0xb4000079accf5760 tid=1729 ready to run
05-24 19:06:39.209  1692  1713 W audio_messenger_ipi: read_ipi_thread(), read dsp fail!! length_read: 0, errno: 0
05-24 19:06:39.210  1400  1709 W PackageManager: Skipping preparing app data for com.android.adservices
05-24 19:06:39.211  1400  1709 W PackageManager: Skipping preparing app data for com.android.permission
05-24 19:06:39.213  1400  1709 W PackageManager: Skipping preparing app data for com.android.btservices
05-24 19:06:39.217  1400  1709 W PackageManager: Skipping preparing app data for com.android.extservices
05-24 19:06:39.218  1733  1733 E DEBUG   : failed to read process info: failed to open /proc/1692: No such file or directory
05-24 19:06:39.219  1400  1709 W PackageManager: Skipping preparing app data for com.android.nfcservices
05-24 19:06:39.273  1733  1733 F DEBUG   : Cmdline: /vendor/bin/hw/android.hardware.audio.service
05-24 19:06:39.273  1733  1733 F DEBUG   : pid: 1692, tid: 1698, name: HwBinder:1692_2  >>> /vendor/bin/hw/android.hardware.audio.service <<<
05-24 19:06:39.273  1733  1733 F DEBUG   :       #01 pc 0002651d  /vendor/lib/hw/<EMAIL> (android::hardware::audio::V7_1::implementation::StreamOut::setLatencyModeCallback(android::sp<android::hardware::audio::V7_1::IStreamOutLatencyModeCallback> const&)+36) (BuildId: 544a581a8a5464266e14c74f5aea9a1c)
05-24 19:06:39.273  1733  1733 F DEBUG   :       #02 pc 00043c0f  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::_hidl_setLatencyModeCallback(android::hidl::base::V1_0::BnHwBase*, android::hardware::Parcel const&, android::hardware::Parcel*, std::__1::function<void (android::hardware::Parcel&)>)+198) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 19:06:39.273  1733  1733 F DEBUG   :       #03 pc 00044703  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::onTransact(unsigned int, android::hardware::Parcel const&, android::hardware::Parcel*, unsigned int, std::__1::function<void (android::hardware::Parcel&)>)+1926) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 19:06:39.309  1400  1400 I SystemServiceManager: Starting com.android.server.sensors.SensorService
05-24 19:06:39.309  1400  1400 I SystemServiceManager: Starting com.android.server.SystemConfigService
05-24 19:06:39.311  1400  1400 I SystemServiceManager: Starting com.android.server.BatteryService
05-24 19:06:39.320   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.sensors@2.1::ISensors/default in either framework or device VINTF manifest.
05-24 19:06:39.326   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.light@2.0::ILight/default in either framework or device VINTF manifest.
05-24 19:06:39.332  1400  1400 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 19:06:39.334  1400  1400 I HealthServiceWrapperHidl: health: HealthServiceWrapper listening to instance default
05-24 19:06:39.334  1400  1400 I BatteryService: health: Waited 0ms and received the update.
05-24 19:06:39.334  1400  1738 W SensorService: lsm6dso ACCELEROMETER's max range 78.453201293945 is not a multiple of the resolution 0.001200000057 - updated to 78.453605651855
05-24 19:06:39.334  1400  1738 I SensorService: lsm6dso ACCELEROMETER's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 W SensorService: mmc5603 MAGNETOMETER's max range 4912.000000000000 is not a multiple of the resolution 0.150000005960 - updated to 4912.050292968750
05-24 19:06:39.334  1400  1738 I SensorService: mmc5603 MAGNETOMETER's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 W SensorService: lsm6dso GYROSCOPE's max range 34.906600952148 is not a multiple of the resolution 0.001099999994 - updated to 34.906299591064
05-24 19:06:39.334  1400  1738 I SensorService: lsm6dso GYROSCOPE's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 I SensorService: tcs3701 PROXIMITY's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 W SensorService: UNCALI_MAG's max range 4912.000000000000 is not a multiple of the resolution 0.150000005960 - updated to 4912.050292968750
05-24 19:06:39.334  1400  1738 I SensorService: UNCALI_MAG's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 W SensorService: UNCALI_GYRO's max range 34.906600952148 is not a multiple of the resolution 0.001099999994 - updated to 34.906299591064
05-24 19:06:39.334  1400  1738 I SensorService: UNCALI_GYRO's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 I SensorService: SIGNIFICANT_MOTION's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 I SensorService: STEP_DETECTOR's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 I SensorService: STEP_COUNTER's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 I SensorService: DEVICE_ORIENTATION's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 I SensorService: STATIONARY_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 I SensorService: MOTION_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 W SensorService: UNCALI_ACC's max range 39.226600646973 is not a multiple of the resolution 0.001200000057 - updated to 39.226802825928
05-24 19:06:39.334  1400  1738 I SensorService: UNCALI_ACC's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 I SensorService: tcs3701 LIGHT's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 I SensorService: RAW_MAG's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 I SensorService: mn29005 rear_als's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 I SensorService: ai_shutter's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 I SensorService: STEP_DETECTOR_WAKEUP's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 I SensorService: PICKUP_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 I SensorService: FP_DISPLAY's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 I SensorService: LUX_AOD's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.334  1400  1738 I SensorService: PEDO_MINUTE's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.335  1400  1738 I SensorService: OPLUS_ACTIVITY_RECOGNITION's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.335  1400  1738 I SensorService: ELEVATOR_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-24 19:06:39.335  1400  1400 I SystemServiceManager: Starting com.android.server.usage.UsageStatsService
05-24 19:06:39.341  1400  1400 I SystemServiceManager: Starting com.android.server.webkit.WebViewUpdateService
05-24 19:06:39.343  1400  1400 I SystemServiceManager: Starting com.android.server.CachedDeviceStateService
05-24 19:06:39.343  1400  1400 I SystemServiceManager: Starting com.android.server.BinderCallsStatsService$LifeCycle
05-24 19:06:39.344  1400  1400 I SystemServiceManager: Starting com.android.server.LooperStatsService$Lifecycle
05-24 19:06:39.345  1400  1400 I SystemServiceManager: Starting com.android.server.rollback.RollbackManagerService
05-24 19:06:39.353  1400  1400 I SystemServiceManager: Starting com.android.server.os.NativeTombstoneManagerService
05-24 19:06:39.354  1400  1400 I SystemServiceManager: Starting com.android.server.os.BugreportManagerService
05-24 19:06:39.355  1400  1400 I SystemServiceManager: Starting com.android.server.gpu.GpuService
05-24 19:06:39.355  1400  1400 I SystemServiceManager: Starting com.android.server.security.rkp.RemoteProvisioningService
05-24 19:06:39.357  1400  1400 I SystemServiceManager: Starting com.android.server.security.KeyChainSystemService
05-24 19:06:39.357  1400  1400 I SystemServiceManager: Starting com.android.server.BinaryTransparencyService
05-24 19:06:39.358  1400  1400 I TransparencyService: Started BinaryTransparencyService
05-24 19:06:39.359  1400  1400 I SystemServiceManager: Starting com.android.server.telecom.TelecomLoaderService
05-24 19:06:39.362  1191  1387 I LSPosedService: manager is not installed
05-24 19:06:39.362  1400  1400 I SystemServiceManager: Starting com.android.server.accounts.AccountManagerService$Lifecycle
05-24 19:06:39.369  1400  1400 I SystemServiceManager: Starting com.android.server.content.ContentService$Lifecycle
05-24 19:06:39.373  1400  1752 I SchedulingPolicyService: Moving 1089 back to group default
05-24 19:06:39.466  1400  1400 I Freezer : Cannot open freezer path "/sys/fs/cgroup/uid_1000/pid_1400/frozen/freezer.state": No such file or directory
05-24 19:06:39.466  1400  1400 I SystemServiceManager: Starting com.android.server.deviceconfig.DeviceConfigInit$Lifecycle
05-24 19:06:39.468  1400  1400 I SystemServiceManager: Starting com.android.server.DropBoxManagerService
05-24 19:06:39.469  1400  1400 I SystemServiceManager: Starting com.android.ecm.EnhancedConfirmationService
05-24 19:06:39.473  1400  1400 I SystemServiceManager: Starting com.android.server.power.hint.HintManagerService
05-24 19:06:39.475  1400  1400 I SystemServiceManager: Starting com.android.role.RoleService
05-24 19:06:39.478  1400  1400 I SystemServiceManager: Starting com.android.server.vibrator.VibratorManagerService$Lifecycle
05-24 19:06:39.487   876   876 E ActivityRecognitionHardware: activity_recognition HAL is deprecated. class_init is effectively a no-op
05-24 19:06:39.488  1400  1400 I SystemServiceManager: Starting com.android.server.alarm.AlarmManagerService
05-24 19:06:39.516  1400  1400 I InputManager: Initializing input manager, mUseDevInputEventForAudioJack=true
05-24 19:06:39.517  1400  1400 I SystemServiceManager: Starting com.android.server.devicestate.DeviceStateManagerService
05-24 19:06:39.519  1400  1400 E DeviceStateManagerService: Cannot notify device state info change before the initial state has been committed.
05-24 19:06:39.519  1400  1400 I DeviceStateManagerService: Cannot notify device state info change when pending state is present.
05-24 19:06:39.521  1400  1400 I SystemServiceManager: Starting com.android.server.camera.CameraServiceProxy
05-24 19:06:39.523  1400  1400 I SystemServiceManager: Starting phase 200
05-24 19:06:39.588   876   876 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlags
05-24 19:06:39.588   876   876 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlagsImpl
05-24 19:06:39.588   876   876 W Zygote  : Class not found for preloading: android.media.audio.Flags
05-24 19:06:39.656  1400  1765 I HidlServiceManagement: Registered android.frameworks.sensorservice@1.0::ISensorManager/default
05-24 19:06:39.657  1400  1766 I HidlServiceManagement: Registered android.frameworks.schedulerservice@1.0::ISchedulingPolicyService/default
05-24 19:06:39.657  1400  1400 I SystemServiceManager: Starting com.android.server.bluetooth.BluetoothService
05-24 19:06:39.668  1400  1400 I SystemServiceManager: Starting com.android.server.connectivity.IpConnectivityMetrics
05-24 19:06:39.668  1400  1400 I SystemServiceManager: Starting com.android.server.net.watchlist.NetworkWatchlistService$Lifecycle
05-24 19:06:39.672  1400  1400 I SystemServiceManager: Starting com.android.server.pinner.PinnerService
05-24 19:06:39.675  1400  1400 I SystemServiceManager: Starting com.android.server.integrity.AppIntegrityManagerService
05-24 19:06:39.676  1400  1400 I SystemServiceManager: Starting com.android.server.logcat.LogcatManagerService
05-24 19:06:39.727  1400  1564 W PinnerService: Could not find pinlist.meta for "/system_ext/priv-app/SystemUI/SystemUI.apk": pinning as blob
05-24 19:06:39.748  1400  1400 I SystemServiceManager: Starting com.android.server.inputmethod.InputMethodManagerService$Lifecycle
05-24 19:06:39.757  1400  1400 I SystemServiceManager: Starting com.android.server.accessibility.AccessibilityManagerService$Lifecycle
05-24 19:06:39.770  1400  1400 I SystemServiceManager: Starting com.android.server.StorageManagerService$Lifecycle
05-24 19:06:39.780  1400  1400 I SystemServiceManager: Starting com.android.server.usage.StorageStatsService$Lifecycle
05-24 19:06:39.791  1400  1400 I SystemServiceManager: Starting com.android.server.UiModeManagerService
05-24 19:06:39.793  1400  1400 I SystemServiceManager: Starting com.android.server.locales.LocaleManagerService
05-24 19:06:39.795  1400  1400 I SystemServiceManager: Starting com.android.server.grammaticalinflection.GrammaticalInflectionService
05-24 19:06:39.796  1400  1400 I SystemServiceManager: Starting com.android.server.apphibernation.AppHibernationService
05-24 19:06:39.800  1400  1400 I SystemServiceManager: Starting com.android.server.locksettings.LockSettingsService$Lifecycle
05-24 19:06:39.809  1400  1400 I SystemServiceManager: Starting com.android.server.pdb.PersistentDataBlockService
05-24 19:06:39.810  1400  1400 I SystemServiceManager: Starting com.android.server.testharness.TestHarnessModeService
05-24 19:06:39.811  1400  1400 I SystemServiceManager: Starting com.android.server.oemlock.OemLockService
05-24 19:06:39.812   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.oemlock@1.0::IOemLock/default in either framework or device VINTF manifest.
05-24 19:06:39.814  1400  1400 I SystemServiceManager: Starting com.android.server.DeviceIdleController
05-24 19:06:39.820  1400  1561 I PersistentDataBlockService: FRP secret matched, FRP deactivated.
05-24 19:06:39.821  1400  1400 I SystemServiceManager: Starting com.android.server.devicepolicy.DevicePolicyManagerService$Lifecycle
05-24 19:06:39.835  1400  1400 I SystemServiceManager: Starting com.android.server.systemcaptions.SystemCaptionsManagerService
05-24 19:06:39.835  1400  1400 I SystemServiceManager: Starting com.android.server.texttospeech.TextToSpeechManagerService
05-24 19:06:39.836  1400  1400 I SystemServiceManager: Starting com.android.server.wearable.WearableSensingManagerService
05-24 19:06:39.838  1400  1400 I SystemServiceManager: Starting com.android.server.ondeviceintelligence.OnDeviceIntelligenceManagerService
05-24 19:06:39.839  1400  1400 I SystemServiceManager: Starting com.android.server.speech.SpeechRecognitionManagerService
05-24 19:06:39.841  1400  1400 I SystemServiceManager: Starting com.android.server.appprediction.AppPredictionManagerService
05-24 19:06:39.842  1400  1400 I SystemServiceManager: Starting com.android.server.contentsuggestions.ContentSuggestionsManagerService
05-24 19:06:39.843  1400  1400 I SystemServiceManager: Starting com.android.server.contextualsearch.ContextualSearchManagerService
05-24 19:06:39.848  1400  1400 I FontManagerService: Using optimized boot-time font loading.
05-24 19:06:39.848  1400  1400 I SystemServiceManager: Starting com.android.server.textservices.TextServicesManagerService$Lifecycle
05-24 19:06:39.849  1400  1400 I SystemServiceManager: Starting com.android.server.textclassifier.TextClassificationManagerService$Lifecycle
05-24 19:06:39.851  1400  1400 I SystemServiceManager: Starting com.android.server.NetworkScoreService$Lifecycle
05-24 19:06:39.852  1400  1400 I NetworkScoreService: Registering network_score
05-24 19:06:39.854  1400  1400 I SystemServiceManager: Starting com.android.server.NetworkStatsServiceInitializer
05-24 19:06:39.881  1400  1564 W PinnerService: Could not find pinlist.meta for "/system_ext/priv-app/Launcher3QuickStep/Launcher3QuickStep.apk": pinning as blob
05-24 19:06:39.891  1400  1400 I NetworkStatsServiceInitializer: Registering netstats
05-24 19:06:39.897  1400  1400 I SystemServiceManager: Starting com.android.server.wifi.WifiService
05-24 19:06:39.899  1400  1400 I WifiContext: Got resolveInfos for com.android.server.wifi.intent.action.SERVICE_WIFI_RESOURCES_APK: [ResolveInfo{2838788 com.android.wifi.resources/android.app.Activity m=0x108000 userHandle=UserHandle{0}}]
05-24 19:06:39.906  1400  1400 E WifiScoringParams: 	at com.android.server.wifi.ScoringParams.<init>(ScoringParams.java:262)
05-24 19:06:39.906  1400  1400 E WifiScoringParams: 	at com.android.server.wifi.WifiInjector.<init>(WifiInjector.java:319)
05-24 19:06:39.906  1400  1400 E WifiScoringParams: 	at com.android.server.wifi.WifiService.<init>(WifiService.java:44)
05-24 19:06:39.906  1400  1400 E WifiScoringParams: 	at com.android.server.SystemServiceManager.startService(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:9)
05-24 19:06:39.906  1400  1400 E WifiScoringParams: 	at com.android.server.SystemServiceManager.startServiceFromJar(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:88)
05-24 19:06:39.906  1400  1400 E WifiScoringParams: 	at com.android.server.SystemServer.startOtherServices(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:322)
05-24 19:06:39.906  1400  1400 E WifiScoringParams: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:597)
05-24 19:06:39.906  1400  1400 E WifiScoringParams: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:889)
05-24 19:06:39.912  1400  1400 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.2::IServiceManager/default
05-24 19:06:39.921  1400  1400 I SupplicantStaIfaceHal: Initializing SupplicantStaIfaceHal using AIDL implementation.
05-24 19:06:39.923  1400  1400 I SupplicantP2pIfaceHal: Initializing SupplicantP2pIfaceHal using AIDL implementation.
05-24 19:06:39.950  1400  1400 I WifiService: Registering wifi
05-24 19:06:39.951  1400  1400 I SystemServiceManager: Starting com.android.server.wifi.scanner.WifiScanningService
05-24 19:06:39.951  1400  1400 I WifiScanningService: Creating wifiscanner
05-24 19:06:39.953  1400  1400 I WifiScanningService: Publishing wifiscanner
05-24 19:06:39.953  1400  1400 I SystemServiceManager: Starting com.android.server.wifi.p2p.WifiP2pService
05-24 19:06:39.954  1400  1400 I WifiContext: Got resolveInfos for com.android.server.wifi.intent.action.SERVICE_WIFI_RESOURCES_APK: [ResolveInfo{164ec38 com.android.wifi.resources/android.app.Activity m=0x108000 userHandle=UserHandle{0}}]
05-24 19:06:39.958  1400  1400 I WifiP2pService: Registering wifip2p
05-24 19:06:39.960  1400  1400 I SystemServiceManager: Starting com.android.server.ConnectivityServiceInitializer
05-24 19:06:39.989  1400  1400 I MulticastRoutingCoordinatorService: socket created for multicast routing: java.io.FileDescriptor@6dbf95c
05-24 19:06:39.993  1400  1800 W BroadcastLoopers: Found previously unknown looper Thread[NsdService,5,main]
05-24 19:06:39.999  1400  1400 I ConnectivityServiceInitializer: Registering ethernet
05-24 19:06:40.000  1400  1400 I ConnectivityServiceInitializer: Registering connectivity
05-24 19:06:40.000  1400  1400 I ConnectivityServiceInitializer: Registering ipsec
05-24 19:06:40.000  1400  1400 I ConnectivityServiceInitializer: Registering connectivity_native
05-24 19:06:40.000  1400  1400 I ConnectivityServiceInitializer: Registering servicediscovery
05-24 19:06:40.001  1400  1400 I ConnectivityServiceInitializer: Registering nearby
05-24 19:06:40.004  1400  1400 I SystemServiceManager: Starting com.android.server.ConnectivityServiceInitializerB
05-24 19:06:40.006  1400  1400 I ConnectivityServiceInitializerB: Registering vcn_management
05-24 19:06:40.007  1400  1400 I SystemUpdateManagerService: No existing info file /data/system/system-update-info.xml
05-24 19:06:40.009  1400  1400 I SystemServiceManager: Starting com.android.server.notification.NotificationManagerService
05-24 19:06:40.056  1400  1400 I NotificationManagerService.NotificationListeners: Read notification listener permissions from xml
05-24 19:06:40.057  1400  1400 W ActivityManager: Unable to start service Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener } U=0: not found
05-24 19:06:40.057  1400  1400 W NotificationManagerService.NotificationListeners: Unable to bind notification listener service: Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener (has extras) } in user 0
05-24 19:06:40.057  1400  1400 I NotificationManagerService.NotificationAssistants: Read notification assistant permissions from xml
05-24 19:06:40.060  1400  1400 I ConditionProviders: Read condition provider permissions from xml
05-24 19:06:40.060  1400  1400 I ConditionProviders: Read condition provider permissions from xml
05-24 19:06:40.061  1400  1400 I ConditionProviders:  Allowing condition provider android.ext.services/android.ext.services.notification.Assistant (userSet: true)
05-24 19:06:40.068  1400  1400 W SystemServiceManager: Service com.android.server.notification.NotificationManagerService took 59 ms in onStart
05-24 19:06:40.069  1400  1400 I SystemServiceManager: Starting com.android.server.storage.DeviceStorageMonitorService
05-24 19:06:40.070  1400  1400 I SystemServiceManager: Starting com.android.server.timedetector.TimeDetectorService$Lifecycle
05-24 19:06:40.075  1400  1400 I SystemServiceManager: Starting com.android.server.location.LocationManagerService$Lifecycle
05-24 19:06:40.080  1400  1400 I SystemServiceManager: Starting com.android.server.timezonedetector.TimeZoneDetectorService$Lifecycle
05-24 19:06:40.083  1400  1400 I SystemServiceManager: Starting com.android.server.location.altitude.AltitudeService$Lifecycle
05-24 19:06:40.084  1400  1400 I SystemServiceManager: Starting com.android.server.timezonedetector.location.LocationTimeZoneManagerService$Lifecycle
05-24 19:06:40.084  1400  1400 I SystemServiceManager: Starting com.android.server.search.SearchManagerService$Lifecycle
05-24 19:06:40.086  1400  1400 I SystemServiceManager: Starting com.android.server.wallpaper.WallpaperManagerService$Lifecycle
05-24 19:06:40.088  1400  1400 I SystemServiceManager: Starting com.android.server.audio.AudioService$Lifecycle
05-24 19:06:42.763  1400  1586 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.adservices/appid
05-24 19:06:42.765  1400  1586 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.permission/appid
05-24 19:06:42.770  1400  1586 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.btservices/appid
05-24 19:06:42.800  1400  1586 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.extservices/appid
05-24 19:06:42.805  1400  1586 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.nfcservices/appid
05-24 19:06:43.633  1820  1820 I HidlServiceManagement: Registered android.hardware.audio@7.1::IDevicesFactory/default
05-24 19:06:43.633  1820  1820 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-24 19:06:43.633  1820  1820 I LegacySupport: Registration complete for android.hardware.audio@7.1::IDevicesFactory/default.
05-24 19:06:43.639  1820  1820 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-24 19:06:43.640  1820  1820 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-24 19:06:43.649  1820  1820 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 19:06:43.722  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.722  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.722  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.722  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.722  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.722  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.722  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.722  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.722  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.722  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.722  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.723  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.723  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.723  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.723  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.723  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.723  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.723  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.760  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.760  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.763  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 19:06:43.779  1820  1820 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x81b50005)
05-24 19:06:43.784  1820  1820 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x76b1a075)
05-24 19:06:43.786  1820  1820 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 19:06:43.786  1820  1820 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xd03d2d35)
05-24 19:06:43.787  1820  1820 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x284b4bdd)
05-24 19:06:43.790  1820  1820 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-24 19:06:43.790   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 19:06:43.791  1820  1820 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-24 19:06:43.791   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 19:06:43.791  1820  1820 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-24 19:06:43.791   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 19:06:43.792  1820  1820 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-24 19:06:43.792  1820  1820 W audiohalservice: Could not register Bluetooth Audio API
05-24 19:06:43.792   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-24 19:06:43.792  1820  1820 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-24 19:06:43.792  1820  1820 W audiohalservice: Could not register Bluetooth Audio Offload API
05-24 19:06:43.799  1820  1820 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-24 19:06:43.799  1820  1820 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-24 19:06:43.799  1820  1820 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-24 19:06:43.820  1827  1827 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 19:06:43.821  1827  1827 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 19:06:43.822   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 19:06:43.833  1827  1827 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 19:06:43.833   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 19:06:43.835  1827  1827 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 19:06:43.841  1827  1827 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-24 19:06:43.851  1827  1827 E libxml2 : I/O warning : failed to load "/odm/etc/virtual_audio_policy_configuration.xml": No such file or directory
05-24 19:06:43.852  1827  1827 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-24 19:06:43.852  1827  1827 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-24 19:06:43.861  1820  1820 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-24 19:06:43.861  1820  1820 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-24 19:06:43.867  1820  1820 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 19:06:43.923  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.923  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.924  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.924  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.924  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.924  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.924  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.924  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.924  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.924  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.924  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.925  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.925  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.925  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.925  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.925  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.925  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.925  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.969  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.969  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 19:06:43.972  1820  1820 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 19:06:43.991  1820  1820 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x79e38f63)
05-24 19:06:43.992  1820  1837 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-24 19:06:43.994  1820  1837 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0x79e38f63)
05-24 19:06:43.995  1820  1837 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0x79e38f63)
05-24 19:06:44.061  1820  1820 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-24 19:06:44.068  1820  1820 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 19:06:44.093  1820  1820 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-24 19:06:44.106  1820  1820 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 19:06:44.106  1820  1820 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 19:06:44.106  1820  1820 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 19:06:44.112  1820  1820 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 19:06:44.112  1820  1820 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 19:06:44.113  1820  1820 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-24 19:06:44.117  1820  1820 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-24 19:06:44.117  1820  1820 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-24 19:06:44.119  1827  1827 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 19:06:44.119  1827  1827 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 19:06:44.120  1820  1820 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-24 19:06:44.120  1820  1820 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-24 19:06:44.121  1820  1820 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 19:06:44.121  1827  1827 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-24 19:06:44.121  1827  1827 I AudioFlinger: openOutput() this 0xb400007e5b2d3070, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-24 19:06:44.122  1827  1827 I AudioFlinger: operator(): Using 3000 ms as standby time
05-24 19:06:44.123  1827  1827 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 19:06:44.124  1827  1827 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 19:06:44.124   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 19:06:44.125  1827  1827 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 19:06:44.133  1827  1850 I AudioFlinger: AudioFlinger's thread 0xb400007f6eb4c760 tid=1850 ready to run
05-24 19:06:44.133  1827  1850 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:44.134  1827  1827 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:44.136  1827  1850 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.136  1827  1827 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:44.136  1827  1827 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:44.136  1827  1827 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:44.136  1827  1827 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:44.136  1827  1827 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:44.136  1827  1827 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:44.136  1827  1827 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:44.136  1827  1827 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:44.136  1827  1827 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:44.136  1827  1827 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:44.136  1827  1827 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:44.136  1827  1827 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:44.136  1827  1827 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 19:06:44.136  1827  1827 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 19:06:44.136  1827  1827 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 19:06:44.137  1827  1827 W AudioFlinger: moveEffects() AUDIO_SESSION_OUTPUT_MIX not found in orphans, checking other mix
05-24 19:06:44.137  1827  1827 W AudioFlinger: moveEffects() bad srcIo 0
05-24 19:06:44.137  1827  1827 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 19:06:44.159  1820  1820 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x2, output_devices == current_output_devices(0x00000002), return
05-24 19:06:44.162  1827  1827 I AudioFlinger: openOutput() this 0xb400007e5b2d3070, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x8
05-24 19:06:44.162  1820  1820 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 19:06:44.163  1827  1827 I AudioFlinger: HAL output buffer size 2048 frames, normal sink buffer size 2048 frames
05-24 19:06:44.164  1827  1852 I AudioFlinger: AudioFlinger's thread 0xb400007f6e9ae760 tid=1852 ready to run
05-24 19:06:44.164  1827  1852 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.164  1827  1852 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.165  1827  1827 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 19:06:44.230  1820  1820 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x8, output_devices == current_output_devices(0x00000002), return
05-24 19:06:44.232  1827  1827 I AudioFlinger: openOutput() this 0xb400007e5b2d3070, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x4
05-24 19:06:44.232  1820  1820 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 19:06:44.233  1827  1827 I AudioFlinger: HAL output buffer size 256 frames, normal sink buffer size 768 frames
05-24 19:06:44.234  1827  1854 I AudioFlinger: AudioFlinger's thread 0xb400007f6e927760 tid=1854 ready to run
05-24 19:06:44.235  1827  1854 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.235  1827  1854 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.237  1827  1827 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 19:06:44.259  1820  1820 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x4, output_devices == current_output_devices(0x00000002), return
05-24 19:06:44.261  1827  1827 I AudioFlinger: openOutput() this 0xb400007e5b2d3070, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x8000
05-24 19:06:44.262  1820  1820 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 19:06:44.263  1827  1827 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 19:06:44.264  1827  1856 I AudioFlinger: AudioFlinger's thread 0xb400007f6e628760 tid=1856 ready to run
05-24 19:06:44.264  1827  1856 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.265  1827  1856 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.267  1827  1827 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 19:06:44.299  1820  1820 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x8000, output_devices == current_output_devices(0x00000002), return
05-24 19:06:44.301  1827  1827 I AudioFlinger: openOutput() this 0xb400007e5b2d3070, module 10 Device AUDIO_DEVICE_OUT_TELEPHONY_TX, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x10000
05-24 19:06:44.302  1820  1820 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 19:06:44.303  1827  1827 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 19:06:44.304  1827  1857 I AudioFlinger: AudioFlinger's thread 0xb400007f6a892760 tid=1857 ready to run
05-24 19:06:44.304  1827  1857 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.305  1827  1857 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.335  1827  1859 I AudioFlinger: AudioFlinger's thread 0xb400007f6a79ea78 tid=1859 ready to run
05-24 19:06:44.336  1827  1859 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.337  1827  1859 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.341  1827  1861 I AudioFlinger: AudioFlinger's thread 0xb400007f6a79ea78 tid=1861 ready to run
05-24 19:06:44.341  1827  1861 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.343  1827  1861 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.348  1827  1863 I AudioFlinger: AudioFlinger's thread 0xb400007f6a79ea78 tid=1863 ready to run
05-24 19:06:44.348  1827  1863 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.349  1827  1863 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.352  1827  1865 I AudioFlinger: AudioFlinger's thread 0xb400007f6a79ea78 tid=1865 ready to run
05-24 19:06:44.353  1827  1865 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.353  1827  1865 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.357  1827  1868 I AudioFlinger: AudioFlinger's thread 0xb400007f6a79ea78 tid=1868 ready to run
05-24 19:06:44.358  1827  1868 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.358  1827  1868 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.361  1820  1825 E DevicesFactoryHAL: loadAudioInterface couldn't load audio hw module audio.binaural_record (No such file or directory)
05-24 19:06:44.362  1827  1827 E AudioFlinger: loadHwModule() error -22 loading module binaural_record
05-24 19:06:44.362  1827  1827 W APM_AudioPolicyManager: could not load HW module binaural_record
05-24 19:06:44.367  1827  1827 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 19:06:44.368  1827  1827 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 19:06:44.368  1827  1827 I AudioFlinger: loadHwModule() Loaded bluetooth audio interface, handle 18
05-24 19:06:44.369  1820  1825 E DevicesFactoryHAL: loadAudioInterface couldn't load audio hw module audio.a2dp (No such file or directory)
05-24 19:06:44.369  1827  1827 E AudioFlinger: loadHwModule() error -22 loading module a2dp
05-24 19:06:44.369  1827  1827 W APM_AudioPolicyManager: could not load HW module a2dp
05-24 19:06:44.369  1820  1825 E DevicesFactoryHAL: loadAudioInterface couldn't load audio hw module audio.usb (No such file or directory)
05-24 19:06:44.369  1827  1827 E AudioFlinger: loadHwModule() error -22 loading module usb
05-24 19:06:44.369  1827  1827 W APM_AudioPolicyManager: could not load HW module usb
05-24 19:06:44.371  1820  1825 I r_submix: adev_open(name=audio_hw_if)
05-24 19:06:44.372  1827  1827 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 19:06:44.372  1827  1827 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 19:06:44.372  1820  1825 I r_submix: adev_init_check()
05-24 19:06:44.372  1827  1827 I AudioFlinger: loadHwModule() Loaded r_submix audio interface, handle 26
05-24 19:06:44.374  1827  1870 I AudioFlinger: AudioFlinger's thread 0xb400007f6a79ea78 tid=1870 ready to run
05-24 19:06:44.374  1827  1870 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.376  1827  1870 W AudioFlinger: no wake lock to update, system not ready yet
05-24 19:06:44.380  1827  1827 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 19:06:44.380   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 19:06:44.381  1827  1827 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 19:06:44.388  1064  1614 W AudioAnalytics: onAudioServerStart: (key=audio.policy) AudioPolicy ctor, loadTimeMs:547.313599
05-24 19:06:44.388  1827  1827 I audioserver: main: initialization done in 570.814 ms, joining thread pool
05-24 19:06:44.403  1820  1875 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 19:06:44.404  1820  1875 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 19:06:44.404  1820  1875 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 19:06:44.405  1820  1875 W audio_engineer_test: unknown enum value string receiver for ctl TFA98XX Profile
05-24 19:06:44.405  1820  1875 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 19:06:44.405  1820  1875 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 19:06:44.405  1820  1875 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 19:06:44.406  1820  1875 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 19:06:44.418  1400  1400 I AS.AudioService: Stream 5: using max vol of 7
05-24 19:06:44.418  1400  1400 I AS.AudioService: Stream 5: using default vol of 5
05-24 19:06:44.418  1400  1400 I AS.AudioService: Stream 2: using max vol of 7
05-24 19:06:44.418  1400  1400 I AS.AudioService: Stream 2: using default vol of 5
05-24 19:06:44.422  1400  1400 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 19:06:44.439  1400  1881 I AS.AudioService: updateIndexFactors() stream:0 index min/max:1/15 indexStepFactor:2.3333333
05-24 19:06:44.439  1400  1400 I SystemServiceManager: Starting com.android.server.soundtrigger_middleware.SoundTriggerMiddlewareService$Lifecycle
05-24 19:06:44.440  1400  1881 I AS.AudioService: updateIndexFactors() stream:1 index min/max:0/7 indexStepFactor:1.0
05-24 19:06:44.440  1400  1881 I AS.AudioService: updateIndexFactors() stream:2 index min/max:0/7 indexStepFactor:1.0
05-24 19:06:44.441  1400  1881 I AS.AudioService: updateIndexFactors() stream:3 index min/max:0/15 indexStepFactor:1.0
05-24 19:06:44.442  1400  1881 I AS.AudioService: updateIndexFactors() stream:4 index min/max:1/7 indexStepFactor:1.0
05-24 19:06:44.442  1400  1881 I AS.AudioService: updateIndexFactors() stream:5 index min/max:0/7 indexStepFactor:1.0
05-24 19:06:44.443  1400  1881 I AS.AudioService: updateIndexFactors() stream:7 index min/max:0/7 indexStepFactor:1.0
05-24 19:06:44.443  1400  1881 I AS.AudioService: updateIndexFactors() stream:8 index min/max:0/15 indexStepFactor:1.0
05-24 19:06:44.444  1400  1881 I AS.AudioService: updateIndexFactors() stream:9 index min/max:0/15 indexStepFactor:1.0
05-24 19:06:44.445  1400  1881 I AS.AudioService: updateIndexFactors() stream:10 index min/max:1/15 indexStepFactor:1.0
05-24 19:06:44.446  1400  1881 I AS.AudioService: updateIndexFactors() stream:11 index min/max:0/15 indexStepFactor:1.0
05-24 19:06:44.448  1400  1400 I SystemServiceManager: Starting com.android.server.DockObserver
05-24 19:06:44.449  1400  1400 W WiredAccessoryManager: This kernel does not have usb audio support
05-24 19:06:44.449  1400  1400 W WiredAccessoryManager: This kernel does not have HDMI audio support
05-24 19:06:44.449  1400  1400 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/3/0 does not have DP audio support
05-24 19:06:44.450  1400  1400 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/2/0 does not have DP audio support
05-24 19:06:44.450  1400  1400 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/1/0 does not have DP audio support
05-24 19:06:44.450  1400  1400 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/0/0 does not have DP audio support
05-24 19:06:44.450  1400  1400 I SystemServiceManager: Starting com.android.server.midi.MidiService$Lifecycle
05-24 19:06:44.451  1400  1400 I SystemServiceManager: Starting com.android.server.adb.AdbService$Lifecycle
05-24 19:06:44.453  1400  1400 I SystemServiceManager: Starting com.android.server.usb.UsbService$Lifecycle
05-24 19:06:44.453  1400  1400 I SystemServiceManager: Starting com.android.server.SerialService$Lifecycle
05-24 19:06:44.454  1400  1400 I HardwarePropertiesManagerService-JNI: Thermal AIDL service is not declared, trying HIDL
05-24 19:06:44.456  1400  1400 I SystemServiceManager: Starting com.android.server.twilight.TwilightService
05-24 19:06:44.456  1400  1400 I SystemServiceManager: Starting com.android.server.display.color.ColorDisplayService
05-24 19:06:44.458  1400  1400 I SystemServiceManager: Starting com.android.server.job.JobSchedulerService
05-24 19:06:44.459  1400  1752 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 19:06:44.460  1400  1881 W BroadcastLoopers: Found previously unknown looper Thread[AudioService,5,main]
05-24 19:06:44.462  1400  1563 I UsbDeviceManager: Usb gadget hal service started android.hardware.usb.gadget@1.0::IUsbGadget default
05-24 19:06:44.466  1400  1556 W JobInfo : Job 'com.google.android.setupwizard/.deviceorigin.provider.DeviceOriginWipeOutJobService#8580' has a deadline with no functional constraints. The deadline won't improve job execution latency. Consider removing the deadline.
05-24 19:06:44.468  1400  1752 W StorageManagerService: No primary storage defined yet; hacking together a stub
05-24 19:06:44.468  1400  1400 I SystemServiceManager: Starting com.android.server.soundtrigger.SoundTriggerService
05-24 19:06:44.469  1400  1400 I SystemServiceManager: Starting com.android.server.trust.TrustManagerService
05-24 19:06:44.470  1400  1400 I SystemServiceManager: Starting com.android.server.backup.BackupManagerService$Lifecycle
05-24 19:06:44.472  1400  1400 I SystemServiceManager: Starting com.android.server.appwidget.AppWidgetService
05-24 19:06:44.476  1400  1400 I SystemServiceManager: Starting com.android.server.voiceinteraction.VoiceInteractionManagerService
05-24 19:06:44.479  1400  1752 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 19:06:44.480  1400  1563 I UsbPortManager: Usb hal service started android.hardware.usb@1.0::IUsb default
05-24 19:06:44.481  1400  1556 W JobInfo : Job 'android/com.android.server.usage.UsageStatsIdleService#0' has a deadline with no functional constraints. The deadline won't improve job execution latency. Consider removing the deadline.
05-24 19:06:44.482  1400  1400 I SystemServiceManager: Starting com.android.server.GestureLauncherService
05-24 19:06:44.482  1400  1400 I SystemServiceManager: Starting com.android.server.SensorNotificationService
05-24 19:06:44.483  1827  1880 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_HEADSET, connection: bt-le}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 19:06:44.483  1827  1880 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x20000000, enabled 1, streamToDriveAbs 3
05-24 19:06:44.484  1827  1880 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_HEARING_AID, connection: wireless}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 19:06:44.484  1827  1880 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x8000000, enabled 1, streamToDriveAbs 3
05-24 19:06:44.484  1827  1880 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_SPEAKER, connection: bt-le}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 19:06:44.484  1827  1880 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x20000001, enabled 1, streamToDriveAbs 3
05-24 19:06:44.484  1827  1880 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_BROADCAST, connection: bt-le}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 19:06:44.484  1827  1880 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x20000002, enabled 1, streamToDriveAbs 3
05-24 19:06:44.484  1400  1881 E BluetoothAdapter: Bluetooth service is null
05-24 19:06:44.485  1400  1881 E BluetoothAdapter: Bluetooth service is null
05-24 19:06:44.485   910   910 I android.hardware.usb@1.3-service-mediatekv2: Registering 1.2 callback
05-24 19:06:44.485   910   910 I android.hardware.usb@1.3-service-mediatekv2: registering callback
05-24 19:06:44.485  1400  1881 E BluetoothAdapter: Bluetooth service is null
05-24 19:06:44.485   910  1898 E android.hardware.usb@1.3-service-mediatekv2: creating thread
05-24 19:06:44.485  1400  1881 E BluetoothAdapter: Bluetooth service is null
05-24 19:06:44.485  1400  1881 I AS.SpatializerHelper: init effectExpected=false
05-24 19:06:44.485  1400  1881 I AS.SpatializerHelper: init(): setting state to STATE_NOT_SUPPORTED due to effect not expected
05-24 19:06:44.485   910   910 I android.hardware.usb@1.3-service-mediatekv2: port0
05-24 19:06:44.486   910   910 I android.hardware.usb@1.3-service-mediatekv2: HAL version V1_2
05-24 19:06:44.486   910   910 I android.hardware.usb@1.3-service-mediatekv2: 0:port0 connected:1 canChangeMode:1 canChagedata:0 canChangePower:0 supportedModes:3
05-24 19:06:44.486   910   910 I android.hardware.usb@1.3-service-mediatekv2: ContaminantDetectionStatus:2 ContaminantProtectionStatus:0
05-24 19:06:44.536  1400  1400 I SystemServiceManager: Starting com.android.server.emergency.EmergencyAffordanceService
05-24 19:06:44.537  1400  1400 I SystemServiceManager: Starting com.android.server.blob.BlobStoreManagerService
05-24 19:06:44.539  1400  1400 I SystemServiceManager: Starting com.android.server.dreams.DreamManagerService
05-24 19:06:44.543   872   884 I ServiceManagerCppClient: Waiting for service 'statscompanion' on '/dev/binder'...
05-24 19:06:44.543  1400  1400 I SystemServiceManager: Starting com.android.server.print.PrintManagerService
05-24 19:06:44.545  1400  1400 I SystemServiceManager: Starting com.android.server.security.AttestationVerificationManagerService
05-24 19:06:44.546  1400  1400 I SystemServiceManager: Starting com.android.server.companion.CompanionDeviceManagerService
05-24 19:06:44.553  1400  1400 I SystemServiceManager: Starting com.android.server.companion.virtual.VirtualDeviceManagerService
05-24 19:06:44.555  1400  1400 I SystemServiceManager: Starting com.android.server.restrictions.RestrictionsManagerService
05-24 19:06:44.556  1400  1400 I SystemServiceManager: Starting com.android.server.media.MediaSessionService
05-24 19:06:44.558  1400  1400 I SystemServiceManager: Starting com.android.server.media.MediaResourceMonitorService
05-24 19:06:44.561  1400  1400 I SystemServiceManager: Starting com.android.server.biometrics.sensors.face.FaceService
05-24 19:06:44.562  1400  1400 I SystemServiceManager: Starting com.android.server.biometrics.sensors.fingerprint.FingerprintService
05-24 19:06:44.564  1400  1400 I SystemServiceManager: Starting com.android.server.biometrics.BiometricService
05-24 19:06:44.566  1400  1400 I CameraManagerGlobal: Connecting to camera service
05-24 19:06:44.571  1400  1400 I SystemServiceManager: Starting com.android.server.biometrics.AuthService
05-24 19:06:44.572  1400  1400 I FingerprintService: Before:getDeclaredInstances: IFingerprint instance found, a.length=0
05-24 19:06:44.572  1400  1400 I FingerprintService: After:getDeclaredInstances: a.length=1
05-24 19:06:44.573  1400  1400 I FaceService: Before:getDeclaredInstances: IFace instance found, a.length=0
05-24 19:06:44.573  1400  1400 I FaceService: After:getDeclaredInstances: a.length=1
05-24 19:06:44.574  1400  1400 E AuthService: Unknown modality: 2
05-24 19:06:44.574  1400  1400 I SystemServiceManager: Starting com.android.server.security.authenticationpolicy.AuthenticationPolicyService
05-24 19:06:44.576  1400  1400 I SystemServiceManager: Starting com.android.server.app.AppLockManagerService$Lifecycle
05-24 19:06:44.578  1400  1400 I SystemServiceManager: Starting com.android.server.display.FreeformService
05-24 19:06:44.590  1400  1400 I SystemServiceManager: Starting com.android.server.pm.ShortcutService$Lifecycle
05-24 19:06:44.592  1400  1400 I SystemServiceManager: Starting com.android.server.pm.LauncherAppsService
05-24 19:06:44.595  1400  1400 I SystemServiceManager: Starting com.android.server.pm.CrossProfileAppsService
05-24 19:06:44.597  1400  1400 I SystemServiceManager: Starting com.android.server.pocket.PocketService
05-24 19:06:44.605  1400  1400 I SystemServiceManager: Starting com.android.server.people.PeopleService
05-24 19:06:44.606  1400  1400 I SystemServiceManager: Starting com.android.server.media.metrics.MediaMetricsManagerService
05-24 19:06:44.608  1400  1400 I SystemServiceManager: Starting com.android.server.pm.BackgroundInstallControlService
05-24 19:06:44.610  1400  1400 I SystemServiceManager: Starting com.android.server.voltage.CustomDeviceConfigService
05-24 19:06:44.610  1400  1400 I SystemServiceManager: Starting com.android.server.custom.LineageHardwareService
05-24 19:06:44.611  1400  1400 I SystemServiceManager: Starting com.android.server.custom.display.LiveDisplayService
05-24 19:06:44.614  1400  1400 I SystemServiceManager: Starting com.android.server.custom.health.HealthInterfaceService
05-24 19:06:44.615  1400  1400 I SystemServiceManager: Starting com.android.server.HideAppListService
05-24 19:06:44.615  1400  1400 I HideAppListService: Starting HideAppListService
05-24 19:06:44.615  1400  1400 I SystemServiceManager: Starting com.android.server.GameSpaceManagerService
05-24 19:06:44.616  1400  1400 I SystemServiceManager: Starting com.android.server.media.projection.MediaProjectionManagerService
05-24 19:06:44.620  1400  1400 I SystemServiceManager: Starting com.android.server.slice.SliceManagerService$Lifecycle
05-24 19:06:44.622  1400  1400 I SystemServiceManager: Starting com.android.server.stats.StatsCompanion$Lifecycle
05-24 19:06:44.624  1400  1400 I SystemServiceManager: Starting com.android.server.scheduling.RebootReadinessManagerService$Lifecycle
05-24 19:06:44.627  1400  1400 I SystemServiceManager: Starting com.android.server.stats.pull.StatsPullAtomService
05-24 19:06:44.628  1400  1400 I SystemServiceManager: Starting com.android.server.stats.bootstrap.StatsBootstrapAtomService$Lifecycle
05-24 19:06:44.628  1400  1400 I SystemServiceManager: Starting com.android.server.incident.IncidentCompanionService
05-24 19:06:44.629  1400  1400 I SystemServiceManager: Starting com.android.server.sdksandbox.SdkSandboxManagerService$Lifecycle
05-24 19:06:44.635  1400  1400 I SystemServiceManager: Starting com.android.server.adservices.AdServicesManagerService$Lifecycle
05-24 19:06:44.637  1400  1400 I SystemServiceManager: Starting com.android.server.ondevicepersonalization.OnDevicePersonalizationSystemService$Lifecycle
05-24 19:06:44.638  1400  1400 I ondevicepersonalization: OnDevicePersonalizationSystemService started!
05-24 19:06:44.639  1400  1400 I SystemServiceManager: Starting android.os.profiling.ProfilingService$Lifecycle
05-24 19:06:44.643  1400  1400 I SystemServiceManager: Starting com.android.server.MmsServiceBroker
05-24 19:06:44.644   872   884 I ServiceManagerCppClient: Waiting for service 'statscompanion' on '/dev/binder' successful after waiting 100ms
05-24 19:06:44.644  1400  1400 I SystemServiceManager: Starting com.android.server.autofill.AutofillManagerService
05-24 19:06:44.648  1400  1400 E AutofillManagerServiceImpl: Bad service name: proton.android.pass/proton.android.pass.autofill.ProtonPassAutofillService
05-24 19:06:44.650  1400  1400 I SystemServiceManager: Starting com.android.server.credentials.CredentialManagerService
05-24 19:06:44.651  1400  1400 I SystemServiceManager: Starting com.android.server.clipboard.ClipboardService
05-24 19:06:44.653  1400  1400 I SystemServiceManager: Starting com.android.server.appbinding.AppBindingService$Lifecycle
05-24 19:06:44.654  1400  1400 I SystemServiceManager: Starting com.android.server.tracing.TracingServiceProxy
05-24 19:06:44.658   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.authsecret@1.0::IAuthSecret/default in either framework or device VINTF manifest.
05-24 19:06:44.659  1400  1400 I LockSettingsService: Device doesn't implement AuthSecret HAL
05-24 19:06:44.660  1400  1400 I SystemServiceManager: Starting phase 480
05-24 19:06:44.667  1400  1400 W PocketService: Un-handled boot phase:480
05-24 19:06:44.668  1400  1400 I SystemServiceManager: Starting phase 500
05-24 19:06:44.668  1400  1400 E StatsPullAtomCallbackImpl: Failed to start PowerStatsService statsd pullers
05-24 19:06:44.670  1400  1400 E BatteryStatsService: Could not register PowerStatsInternal
05-24 19:06:44.673   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.power.stats@1.0::IPowerStats/default in either framework or device VINTF manifest.
05-24 19:06:44.673  1400  1574 E BatteryStatsService: Unable to load Power.Stats.HAL. Setting rail availability to false
05-24 19:06:44.674  1400  1574 E BluetoothAdapter: Bluetooth service is null
05-24 19:06:44.703  1400  1581 W KeyguardServiceDelegate: onScreenTurningOn(): no keyguard service!
05-24 19:06:44.729  1400  1564 E AppWidgetManager: Notify service of inheritance info
05-24 19:06:44.729  1400  1564 E AppWidgetManager: 	at com.android.server.appwidget.AppWidgetServiceImpl.ensureGroupStateLoadedLocked(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:22)
05-24 19:06:44.729  1400  1564 E AppWidgetManager: 	at com.android.server.appwidget.AppWidgetServiceImpl.getInstalledProvidersForProfile(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:76)
05-24 19:06:44.812  1400  1400 W SystemServiceManager: Service com.android.server.alarm.AlarmManagerService took 70 ms in onBootPhase
05-24 19:06:44.846  1400  1400 I WifiScanningService: Starting wifiscanner
05-24 19:06:44.848  1400  1400 I EthernetServiceImpl: Starting Ethernet service
05-24 19:06:44.849  1400  1793 I WifiContext: Got resolveInfos for com.android.server.wifi.intent.action.SERVICE_WIFI_RESOURCES_APK: [ResolveInfo{2a6872e com.android.wifi.resources/android.app.Activity m=0x108000 userHandle=UserHandle{0}}]
05-24 19:06:44.878  1400  1789 I WifiService: WifiService starting up with Wi-Fi disabled
05-24 19:06:44.891  1400  1789 I WifiHalHidlImpl: Initializing the WiFi HAL
05-24 19:06:44.891  1400  1789 I WifiHalHidlImpl: initServiceManagerIfNecessaryLocked
05-24 19:06:44.893  1400  1789 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.2::IServiceManager/default
05-24 19:06:44.897  1400  1789 I WifiHalHidlImpl: initWifiIfNecessaryLocked
05-24 19:06:44.898   580   580 I hwservicemanager: Since android.hardware.wifi@1.0::IWifi/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 19:06:44.899   580   580 I hwservicemanager: Since android.hardware.wifi@1.0::IWifi/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 19:06:44.900  1400  1789 I HidlServiceManagement: getService: Trying again for android.hardware.wifi@1.0::IWifi/default...
05-24 19:06:44.910  1400  1564 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 19:06:44.912  1400  1564 I HealthServiceWrapperHidl: health: HealthServiceWrapper listening to instance default
05-24 19:06:44.915   580   580 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IKeySwapper/default in either framework or device VINTF manifest.
05-24 19:06:44.920  1400  1564 I StatsPullAtomService: register thermal listener successfully
05-24 19:06:44.925   580   580 I hwservicemanager: getTransport: Cannot find entry vendor.oplus.hardware.commondcs@1.0::ICommonDcsHalService/commondcsservice in either framework or device VINTF manifest.
05-24 19:06:44.925  1100  1414 E android.hardware.biometrics.fingerprint@2.1-service: service NULL
05-24 19:06:44.926  1400  1400 I SystemServiceManager: Starting com.android.server.policy.PermissionPolicyService
05-24 19:06:44.934  1400  1400 E UserManagerService: Auto-lock preference updated but private space user not found
05-24 19:06:44.936  1400  1400 I AS.AudioService: registerAudioPolicy for android.media.audiopolicy.AudioPolicy$1@497cf6 u/pid:1000/1400 with config:reg:32:ap:0
05-24 19:06:44.939  1932  1932 I android.hardware.wifi@1.0-service-lazy: Wifi Hal is booting up...
05-24 19:06:44.942  1932  1932 I HidlServiceManagement: Registered android.hardware.wifi@1.5::IWifi/default
05-24 19:06:44.943  1932  1932 I HidlServiceManagement: Removing namespace from process name android.hardware.wifi@1.0-service-lazy to wifi@1.0-service-lazy.
05-24 19:06:44.947   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.wifi@1.6::IWifi/default in either framework or device VINTF manifest.
05-24 19:06:44.950  1400  1400 I SystemServiceManager: Starting com.android.server.crashrecovery.CrashRecoveryModule$Lifecycle
05-24 19:06:44.954  1400  1585 W DefaultPermGrantPolicy: No such package:com.google.android.apps.camera.services
05-24 19:06:44.955  1400  1585 W DefaultPermGrantPolicy: No such package:com.verizon.mips.services
05-24 19:06:44.956  1400  1585 W DefaultPermGrantPolicy: No such package:com.google.android.adservices
05-24 19:06:44.957  1400  1585 W DefaultPermGrantPolicy: No such package:com.google.android.apps.actionsservice
05-24 19:06:44.962  1400  1400 I BrightnessSynchronizer: Initial brightness readings: 88(int), 0.34251967(float)
05-24 19:06:44.964  1400  1400 I SystemServiceManager: Starting com.android.server.app.GameManagerService$Lifecycle
05-24 19:06:44.969  1400  1400 I SystemServiceManager: Starting phase 520
05-24 19:06:44.985  1400  1400 W PocketService: Un-handled boot phase:520
05-24 19:06:44.987  1400  1400 I SystemServiceManager: Starting com.android.safetycenter.SafetyCenterService
05-24 19:06:45.004  1400  1400 I SystemServiceManager: Starting com.android.server.appsearch.AppSearchModule$Lifecycle
05-24 19:06:45.017  1400  1400 I AppSearchModule: AppsIndexer service is disabled.
05-24 19:06:45.017  1400  1400 I AppSearchModule: AppOpenEventIndexer service is disabled.
05-24 19:06:45.018  1400  1400 I SystemServiceManager: Starting com.android.server.media.MediaCommunicationService
05-24 19:06:45.020  1400  1400 I SystemServiceManager: Starting com.android.server.compat.overrides.AppCompatOverridesService$Lifecycle
05-24 19:06:45.021  1400  1400 I SystemServiceManager: Starting com.android.server.power.SleepModeService
05-24 19:06:45.024  1400  1400 I SystemServiceManager: Starting com.android.server.healthconnect.HealthConnectManagerService
05-24 19:06:45.032  1400  1400 I SystemServiceManager: Starting com.android.server.devicelock.DeviceLockService
05-24 19:06:45.036  1400  1400 I DeviceLockService: Registering device_lock
05-24 19:06:45.037  1400  1400 I SystemServiceManager: Starting com.android.server.SensitiveContentProtectionManagerService
05-24 19:06:45.061  1400  1400 E ActivityManager: Unable to find com.android.overlay.permissioncontroller/u0
05-24 19:06:45.064  1400  1400 E ActivityManager: Unable to find com.google.android.printservice.recommendation/u0
05-24 19:06:45.106  1400  1400 I SystemServer: Making services ready
05-24 19:06:45.106  1400  1400 I SystemServiceManager: Starting phase 550
05-24 19:06:45.113  1400  1400 I ThermalManagerService$ThermalHalWrapper: Thermal HAL 2.0 service connected.
05-24 19:06:45.113   909   909 I <EMAIL>: thermal_zone_num are changed0
05-24 19:06:45.113   909   909 W <EMAIL>: tz_data_v1[2].tz_idx:0
05-24 19:06:45.114   909   909 W <EMAIL>: tz_data_v1[3].tz_idx:2
05-24 19:06:45.114   909   909 W <EMAIL>: tz_data_v1[5].tz_idx:3
05-24 19:06:45.114   909   909 W <EMAIL>: tz_data_v1[0].tz_idx:5
05-24 19:06:45.114   909   909 W <EMAIL>: tz_data_v1[1].tz_idx:5
05-24 19:06:45.114   909   909 W <EMAIL>: tz_data_v1[9].tz_idx:5
05-24 19:06:45.115   909   909 W <EMAIL>: init_tz_path_v1:find out tz path
05-24 19:06:45.115   909   909 W <EMAIL>: get_tz_map: tz0, name=mtktscpu, label=CPU, muti_tz_num=1
05-24 19:06:45.115   909   909 W <EMAIL>: tz_idx0:5, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 19:06:45.115   909   909 W <EMAIL>: get_tz_map: tz1, name=mtktscpu, label=GPU, muti_tz_num=1
05-24 19:06:45.115   909   909 W <EMAIL>: tz_idx0:5, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 19:06:45.115   909   909 W <EMAIL>: get_tz_map: tz2, name=mtktsbattery, label=BATTERY, muti_tz_num=1
05-24 19:06:45.115   909   909 W <EMAIL>: tz_idx0:0, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 19:06:45.115   909   909 W <EMAIL>: get_tz_map: tz3, name=mtktsAP, label=SKIN, muti_tz_num=1
05-24 19:06:45.115   909   909 W <EMAIL>: tz_idx0:2, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 19:06:45.115   909   909 W <EMAIL>: get_tz_map: tz4, name=notsupport, label=USB_PORT, muti_tz_num=1
05-24 19:06:45.115   909   909 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 19:06:45.115   909   909 W <EMAIL>: get_tz_map: tz5, name=mtktsbtsmdpa, label=POWER_AMPLIFIER, muti_tz_num=1
05-24 19:06:45.115   909   909 W <EMAIL>: tz_idx0:3, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 19:06:45.115   909   909 W <EMAIL>: get_tz_map: tz6, name=notsupport, label=BCL_VOLTAGE, muti_tz_num=1
05-24 19:06:45.115   909   909 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 19:06:45.115   909   909 W <EMAIL>: get_tz_map: tz7, name=notsupport, label=BCL_CURRENT, muti_tz_num=1
05-24 19:06:45.115   909   909 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 19:06:45.115   909   909 W <EMAIL>: get_tz_map: tz8, name=notsupport, label=BCL_PERCENTAGE, muti_tz_num=1
05-24 19:06:45.115   909   909 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 19:06:45.115   909   909 W <EMAIL>: get_tz_map: tz9, name=mtktscpu, label=NPU, muti_tz_num=1
05-24 19:06:45.115   909   909 W <EMAIL>: tz_idx0:5, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 19:06:45.115   909   909 I <EMAIL>: fill_temperatures filterType0 name: CPU type: CPU throttlingStatus: NONE value: 54.411 ret_temps size 0
05-24 19:06:45.115   909   909 I <EMAIL>: fill_temperatures filterType0 name: GPU type: GPU throttlingStatus: NONE value: 54.411 ret_temps size 1
05-24 19:06:45.115   909   909 I <EMAIL>: fill_temperatures filterType0 name: BATTERY type: BATTERY throttlingStatus: NONE value: 37.275 ret_temps size 2
05-24 19:06:45.115   909   909 I <EMAIL>: fill_temperatures filterType0 name: SKIN type: SKIN throttlingStatus: NONE value: 45.553 ret_temps size 3
05-24 19:06:45.115   909   909 I <EMAIL>: fill_temperatures filterType0 name: POWER_AMPLIFIER type: POWER_AMPLIFIER throttlingStatus: NONE value: 44.089 ret_temps size 4
05-24 19:06:45.115   909   909 I <EMAIL>: fill_temperatures filterType0 name: NPU type: NPU throttlingStatus: NONE value: 54.411 ret_temps size 5
05-24 19:06:45.116   909   909 I <EMAIL>: fill_thresholds filterType1 name: SKIN type: SKIN hotThrottlingThresholds: 50 vrThrottlingThreshold: 50 ret_thresholds size 0
05-24 19:06:45.121  1400  1400 W ActivityManager: Too early to start/bind service in system_server: Phase=550 ComponentInfo{com.android.server.telecom/com.android.server.telecom.components.TelecomService}
05-24 19:06:45.122  1400  1400 W ActivityManager: Too early to start/bind service in system_server: Phase=550 ComponentInfo{com.android.server.telecom/com.android.server.telecom.components.TelecomService}
05-24 19:06:45.153   580   580 I hwservicemanager: Notifying android.hardware.wifi@1.5::IWifi/default they have clients: 1
05-24 19:06:45.202  1400  1400 W SystemServiceManager: Service com.android.server.content.ContentService$Lifecycle took 78 ms in onBootPhase
05-24 19:06:45.207   580   580 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IGloveMode/default in either framework or device VINTF manifest.
05-24 19:06:45.208   580   580 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IStylusMode/default in either framework or device VINTF manifest.
05-24 19:06:45.209   580   580 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IHighTouchPollingRate/default in either framework or device VINTF manifest.
05-24 19:06:45.232  1400  1779 W StorageManagerService: Failed to get storage lifetime
05-24 19:06:45.243  1065  1295 I OMXClient: IOmx service obtained
05-24 19:06:45.245  1074  1074 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.aac.decoder'  err=ComponentNotFound(0x80001003)
05-24 19:06:45.245  1065  1295 W OmxInfoBuilder: Fail to add media type audio/mp4a-latm to codec OMX.google.aac.decoder
05-24 19:06:45.247  1065  1295 I OMXClient: IOmx service obtained
05-24 19:06:45.248  1074  1074 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.amrnb.decoder'  err=ComponentNotFound(0x80001003)
05-24 19:06:45.248  1065  1295 W OmxInfoBuilder: Fail to add media type audio/3gpp to codec OMX.google.amrnb.decoder
05-24 19:06:45.250  1065  1295 I OMXClient: IOmx service obtained
05-24 19:06:45.251  1074  1074 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.amrwb.decoder'  err=ComponentNotFound(0x80001003)
05-24 19:06:45.251  1065  1295 W OmxInfoBuilder: Fail to add media type audio/amr-wb to codec OMX.google.amrwb.decoder
05-24 19:06:45.253  1065  1295 I OMXClient: IOmx service obtained
05-24 19:06:45.253  1074  1074 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.flac.decoder'  err=ComponentNotFound(0x80001003)
05-24 19:06:45.254  1065  1295 W OmxInfoBuilder: Fail to add media type audio/flac to codec OMX.google.flac.decoder
05-24 19:06:45.256  1065  1295 I OMXClient: IOmx service obtained
05-24 19:06:45.257  1074  1074 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.g711.alaw.decoder'  err=ComponentNotFound(0x80001003)
05-24 19:06:45.257  1065  1295 W OmxInfoBuilder: Fail to add media type audio/g711-alaw to codec OMX.google.g711.alaw.decoder
05-24 19:06:45.259  1065  1295 I OMXClient: IOmx service obtained
05-24 19:06:45.260  1074  1074 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.g711.mlaw.decoder'  err=ComponentNotFound(0x80001003)
05-24 19:06:45.260  1065  1295 W OmxInfoBuilder: Fail to add media type audio/g711-mlaw to codec OMX.google.g711.mlaw.decoder
05-24 19:06:45.263  1065  1295 I OMXClient: IOmx service obtained
05-24 19:06:45.264  1074  1074 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.mp3.decoder'  err=ComponentNotFound(0x80001003)
05-24 19:06:45.265  1065  1295 W OmxInfoBuilder: Fail to add media type audio/mpeg to codec OMX.google.mp3.decoder
05-24 19:06:45.267  1065  1295 I OMXClient: IOmx service obtained
05-24 19:06:45.268  1074  1074 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.opus.decoder'  err=ComponentNotFound(0x80001003)
05-24 19:06:45.271  1065  1295 W OmxInfoBuilder: Fail to add media type audio/opus to codec OMX.google.opus.decoder
05-24 19:06:45.273  1065  1295 I OMXClient: IOmx service obtained
05-24 19:06:45.273  1074  1074 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.raw.decoder'  err=ComponentNotFound(0x80001003)
05-24 19:06:45.274  1065  1295 W OmxInfoBuilder: Fail to add media type audio/raw to codec OMX.google.raw.decoder
05-24 19:06:45.275  1065  1295 I OMXClient: IOmx service obtained
05-24 19:06:45.276  1074  1074 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.vorbis.decoder'  err=ComponentNotFound(0x80001003)
05-24 19:06:45.277  1065  1295 W OmxInfoBuilder: Fail to add media type audio/vorbis to codec OMX.google.vorbis.decoder
05-24 19:06:45.278  1065  1295 I OMXClient: IOmx service obtained
05-24 19:06:45.280  1074  1074 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.aac.encoder'  err=ComponentNotFound(0x80001003)
05-24 19:06:45.280  1065  1295 W OmxInfoBuilder: Fail to add media type audio/mp4a-latm to codec OMX.google.aac.encoder
05-24 19:06:45.282  1065  1295 I OMXClient: IOmx service obtained
05-24 19:06:45.283  1074  1074 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.amrnb.encoder'  err=ComponentNotFound(0x80001003)
05-24 19:06:45.284  1065  1295 W OmxInfoBuilder: Fail to add media type audio/3gpp to codec OMX.google.amrnb.encoder
05-24 19:06:45.287  1065  1295 I OMXClient: IOmx service obtained
05-24 19:06:45.288  1074  1074 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.amrwb.encoder'  err=ComponentNotFound(0x80001003)
05-24 19:06:45.288  1065  1295 W OmxInfoBuilder: Fail to add media type audio/amr-wb to codec OMX.google.amrwb.encoder
05-24 19:06:45.290  1065  1295 I OMXClient: IOmx service obtained
05-24 19:06:45.292  1074  1136 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.flac.encoder'  err=ComponentNotFound(0x80001003)
05-24 19:06:45.292  1065  1295 W OmxInfoBuilder: Fail to add media type audio/flac to codec OMX.google.flac.encoder
05-24 19:06:45.296  1065  1295 I Codec2Client: Available Codec2 services: "default" "software"
05-24 19:06:45.306  1400  1400 W SystemServiceManager: Service com.android.server.NetworkStatsServiceInitializer took 77 ms in onBootPhase
05-24 19:06:45.306  1400  1400 I ConnectivityServiceInitializerB: Starting vcn_management
05-24 19:06:45.314  1400  1803 I VcnManagementService: new snapshot: TelephonySubscriptionSnapshot{ mActiveDataSubId=-1, mSubIdToInfoMap={}, mSubIdToCarrierConfigMap={}, mPrivilegedPackages={} }
05-24 19:06:45.324  1400  1881 I AS.AudioDeviceBroker: setBluetoothScoOn: false, mBluetoothScoOn: false, btScoRequesterUId: -1, from: resetBluetoothSco
05-24 19:06:45.326  1400  1874 W BroadcastLoopers: Found previously unknown looper Thread[AudioDeviceBroker,5,main]
05-24 19:06:45.330  1827  1880 I AudioFlinger: systemReady
05-24 19:06:45.332  1400  1566 I ActivityManager: Start proc 1952:com.android.systemui/u0a226 for service {com.android.systemui/com.android.systemui.wallpapers.ImageWallpaper}
05-24 19:06:45.333   910   910 I android.hardware.usb@1.3-service-mediatekv2: port0
05-24 19:06:45.334   910   910 I android.hardware.usb@1.3-service-mediatekv2: HAL version V1_2
05-24 19:06:45.334   910   910 I android.hardware.usb@1.3-service-mediatekv2: 0:port0 connected:1 canChangeMode:1 canChagedata:0 canChangePower:0 supportedModes:3
05-24 19:06:45.334   910   910 I android.hardware.usb@1.3-service-mediatekv2: ContaminantDetectionStatus:2 ContaminantProtectionStatus:0
05-24 19:06:45.337  1400  1400 I AppLockManagerService: onBootCompleted
05-24 19:06:45.340  1400  1400 I LMOFreeform/LMOFreeformUIService: add SystemService: com.libremobileos.freeform.server.LMOFreeformUIService@3b9fd72
05-24 19:06:45.341  1400  1400 W PocketService: Un-handled boot phase:550
05-24 19:06:45.343  1400  1400 I AppBindingService: Updating constants with: null
05-24 19:06:45.353  1956  1956 I zygiskd64: [KSU] Unmounted /system/etc/audio_effects.xml
05-24 19:06:45.355  1956  1956 I zygiskd64: [KSU] Unmounted /vendor/etc/audio_effects.xml
05-24 19:06:45.387  1952  1952 W ndroid.systemui: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 19:06:45.422  1952  1952 W BpBinder: Linking to death on org.lsposed.lspd.service.ILSPApplicationService but there are no threads (yet?) listening to incoming transactions. See ProcessState::startThreadPool and ProcessState::setThreadPoolMaxThreadCount. Generally you should setup the binder threadpool before other initialization steps.
05-24 19:06:45.443  1400  1400 W SystemServiceManager: Service com.android.server.policy.PermissionPolicyService took 100 ms in onBootPhase
05-24 19:06:45.470  1400  1709 W PinnerService: Could not find pinlist.meta for "/product/app/webview/webview.apk": pinning as blob
05-24 19:06:45.558  1065  1295 I Codec2InfoBuilder: adding type 'audio/x-adpcm-dvi-ima'
05-24 19:06:45.558  1065  1295 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-dvi-ima
05-24 19:06:45.561  1065  1295 I Codec2InfoBuilder: adding type 'audio/x-adpcm-ms'
05-24 19:06:45.561  1065  1295 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-ms
05-24 19:06:45.564  1065  1295 I Codec2InfoBuilder: adding type 'audio/alac'
05-24 19:06:45.564  1065  1295 W AudioCapabilities: Unsupported mediaType audio/alac
05-24 19:06:45.567  1065  1295 I Codec2InfoBuilder: adding type 'audio/ape'
05-24 19:06:45.567  1065  1295 W AudioCapabilities: Unsupported mediaType audio/ape
05-24 19:06:45.638   871   950 I netd    : networkSetPermissionForUser(1, [1002, 10160, 10191, 10204, 10221, 10222, 10271, 10272, 10273, 10282, 10355]) <0.02ms>
05-24 19:06:45.638   871   950 I netd    : networkSetPermissionForUser(2, [1000, 1001, 1073, 2000, 10152, 10171, 10183, 10199, 10201, 10205, 10226, 10234, 10251]) <0.01ms>
05-24 19:06:45.668  1400  1400 I SystemServiceManager: Starting phase 600
05-24 19:06:45.692  1400  1400 I ServiceWatcher: [network] chose new implementation 10369/app.grapheneos.networklocation/.NetworkLocationService@0
05-24 19:06:45.700  1400  1400 I ServiceWatcher: [fused] chose new implementation 1000/com.android.location.fused/.FusedLocationService@0
05-24 19:06:45.703  1400  1566 I ActivityManager: Start proc 2028:app.grapheneos.networklocation/u0a369 for service {app.grapheneos.networklocation/app.grapheneos.networklocation.NetworkLocationService}
05-24 19:06:45.722  2035  2035 I zygiskd32: [KSU] Unmounted /system/etc/audio_effects.xml
05-24 19:06:45.722  2035  2035 I zygiskd32: [KSU] Unmounted /vendor/etc/audio_effects.xml
05-24 19:06:45.731  1400  1564 E AppLockManagerService: requireUnlock queried by unknown user id 0
05-24 19:06:45.735  1065  1295 I Codec2InfoBuilder: adding type 'audio/mp4a-latm'
05-24 19:06:45.740  1065  1295 I Codec2InfoBuilder: adding type 'audio/mp4a-latm'
05-24 19:06:45.745  1065  1295 I Codec2InfoBuilder: adding type 'audio/3gpp'
05-24 19:06:45.746  1055  1108 W MNLD    : hal_gps_init: hal_gps_init
05-24 19:06:45.747  1400  1400 I GnssLocationProviderJni: Unable to initialize IGnssGeofencing interface.
05-24 19:06:45.748  1065  1295 I Codec2InfoBuilder: adding type 'audio/3gpp'
05-24 19:06:45.751  1400  1400 I GnssManager: gnss hal initialized
05-24 19:06:45.752  1065  1295 I Codec2InfoBuilder: adding type 'audio/amr-wb'
05-24 19:06:45.755  1065  1295 I Codec2InfoBuilder: adding type 'audio/amr-wb'
05-24 19:06:45.762  1400  1400 I ServiceWatcher: [GeocoderProxy] chose new implementation null
05-24 19:06:45.764  1400  1400 I ServiceWatcher: [HardwareActivityRecognitionProxy] chose new implementation null
05-24 19:06:45.766  1400  1400 I ServiceWatcher: [GeofenceProxy] chose new implementation null
05-24 19:06:45.776  1400  1400 W SystemServiceManager: Service com.android.server.location.LocationManagerService$Lifecycle took 87 ms in onBootPhase
05-24 19:06:45.782  1065  1295 I Codec2InfoBuilder: adding type 'audio/flac'
05-24 19:06:45.784  1065  1295 I Codec2InfoBuilder: adding type 'audio/flac'
05-24 19:06:45.788  1065  1295 I Codec2InfoBuilder: adding type 'audio/g711-alaw'
05-24 19:06:45.791  1065  1295 I Codec2InfoBuilder: adding type 'audio/g711-mlaw'
05-24 19:06:45.811  2028  2028 W networklocation: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 19:06:45.816  1065  1295 I Codec2InfoBuilder: adding type 'audio/mpeg'
05-24 19:06:45.819  1400  1400 I ServiceWatcher: [TrustManagerService] chose new implementation null
05-24 19:06:45.821  1400  2076 E AppLockManagerService: Config unavailable for user 0
05-24 19:06:45.835  1065  1295 I Codec2InfoBuilder: adding type 'audio/opus'
05-24 19:06:45.839  1065  1295 I Codec2InfoBuilder: adding type 'audio/opus'
05-24 19:06:45.841  1400  1400 W PocketService: Un-handled boot phase:600
05-24 19:06:45.842  1400  1400 W AlarmManager: Unrecognized alarm listener com.android.server.stats.StatsCompanionService$PullingAlarmListener@6105c19
05-24 19:06:45.842  1065  1295 I Codec2InfoBuilder: adding type 'audio/raw'
05-24 19:06:45.842  1400  1400 W AlarmManager: Unrecognized alarm listener com.android.server.stats.StatsCompanionService$PeriodicAlarmListener@55d04de
05-24 19:06:45.847  1400  1400 I StatsCompanionService: Told statsd that StatsCompanionService is alive.
05-24 19:06:45.859  1065  1295 I Codec2InfoBuilder: adding type 'audio/vorbis'
05-24 19:06:45.868  1400  1566 I ActivityManager: Start proc 2110:com.android.networkstack.process/1073 for service {com.android.networkstack/com.android.server.NetworkStackService}
05-24 19:06:45.869  1952  1979 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 19:06:45.872  1400  1400 I MR2ServiceImpl: switchUser | user: 0
05-24 19:06:45.873  1400  1400 I MmsServiceBroker: Delay connecting to MmsService until an API is called
05-24 19:06:45.880  2028  2028 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-24 19:06:45.886  1400  1400 I SystemServiceManager: Calling onStartUser 0
05-24 19:06:45.901  2110  2110 W rkstack.process: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 19:06:45.904  1400  1779 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-dvi-ima
05-24 19:06:45.905  1400  1779 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-ms
05-24 19:06:45.906  1400  1779 W AudioCapabilities: Unsupported mediaType audio/alac
05-24 19:06:45.906  1400  1779 W AudioCapabilities: Unsupported mediaType audio/ape
05-24 19:06:45.915  1400  1964 I Codec2Client: Available Codec2 services: "default" "software"
05-24 19:06:45.923  1400  1554 I ServiceWatcher: [network] connected to {app.grapheneos.networklocation/app.grapheneos.networklocation.NetworkLocationService}
05-24 19:06:45.930  2096  2096 I WebViewZygoteInit: Starting WebViewZygoteInit
05-24 19:06:45.934  1400  1769 I BluetoothSystemServer: AirplaneModeListener: Init completed. isOn=false, isOnOverrode=false
05-24 19:06:45.934  1400  1769 I BluetoothSystemServer: SatelliteModeListener: Initialized successfully with state: false
05-24 19:06:45.966  2096  2096 I WebViewZygoteInit: Beginning application preload for com.android.webview
05-24 19:06:45.977  1400  2094 E StatsCompanionService: Could not get installer for package: com.google.android.trichromelibrary
05-24 19:06:45.977  1400  2094 E StatsCompanionService: android.content.pm.PackageManager$NameNotFoundException: com.google.android.trichromelibrary
05-24 19:06:45.977  1400  2094 E StatsCompanionService: 	at android.app.ApplicationPackageManager.getInstallSourceInfo(ApplicationPackageManager.java:2772)
05-24 19:06:45.977  1400  2094 E StatsCompanionService: 	at com.android.server.stats.StatsCompanionService.getInstallerPackageName(StatsCompanionService.java:153)
05-24 19:06:45.977  1400  2094 E StatsCompanionService: 	at com.android.server.stats.StatsCompanionService.$r8$lambda$MBPStrBhgnmbybdtzkoTAe-YOYw(StatsCompanionService.java:229)
05-24 19:06:45.977  1400  2094 E StatsCompanionService: 	at com.android.server.stats.StatsCompanionService$$ExternalSyntheticLambda1.run(R8$$SyntheticClass:0)
05-24 19:06:45.977  1400  2094 E StatsCompanionService: 	at android.os.Handler.handleCallback(Handler.java:991)
05-24 19:06:45.977  1400  2094 E StatsCompanionService: 	at android.os.Handler.dispatchMessage(Handler.java:102)
05-24 19:06:45.977  1400  2094 E StatsCompanionService: 	at android.os.Looper.loopOnce(Looper.java:232)
05-24 19:06:45.977  1400  2094 E StatsCompanionService: 	at android.os.Looper.loop(Looper.java:317)
05-24 19:06:45.977  1400  2094 E StatsCompanionService: 	at android.os.HandlerThread.run(HandlerThread.java:85)
05-24 19:06:45.985  1400  1400 W SystemServiceManager: Service com.android.server.StorageManagerService$Lifecycle took 52 ms in onStartUser-0
05-24 19:06:45.987  1400  1779 W AudioCapabilities: Unsupported mime audio/x-adpcm-dvi-ima
05-24 19:06:45.992  1400  1779 W AudioCapabilities: Unsupported mime audio/x-adpcm-dvi-ima
05-24 19:06:45.993  1400  1779 W AudioCapabilities: Unsupported mime audio/x-adpcm-ms
05-24 19:06:45.993  1400  1779 W AudioCapabilities: Unsupported mime audio/x-adpcm-ms
05-24 19:06:45.993  1400  1779 W AudioCapabilities: Unsupported mime audio/alac
05-24 19:06:45.993  1400  1779 W AudioCapabilities: Unsupported mime audio/alac
05-24 19:06:45.994  1400  1779 W AudioCapabilities: Unsupported mime audio/ape
05-24 19:06:45.994  1400  1779 W AudioCapabilities: Unsupported mime audio/ape
05-24 19:06:45.997  1400  1400 W VoiceInteractionManager: no available voice recognition services found for user 0
05-24 19:06:45.997  1400  1400 I AppLockManagerService: onUserStarting: userId = 0
05-24 19:06:46.002  2096  2096 I WebViewZygoteInit: Application preload done
05-24 19:06:46.036  2151  2151 W com.android.se: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 19:06:46.095  1400  1400 I SystemServiceManager: Starting com.android.server.scheduling.RebootReadinessManagerService$Lifecycle
05-24 19:06:46.095  1400  1400 I SystemServiceManager: Not starting an already started service com.android.server.scheduling.RebootReadinessManagerService$Lifecycle
05-24 19:06:46.103  2151  2151 W ContextImpl: Failed to ensure /data/user/0/com.android.se/cache: mkdir failed: ENOENT (No such file or directory)
05-24 19:06:46.104  2151  2151 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-24 19:06:46.106  2171  2171 W m.android.phone: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 19:06:46.130  2174  2174 W ndroid.settings: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 19:06:46.133  2151  2151 I SecureElementService: main onCreate
05-24 19:06:46.136  2151  2151 I SecureElementService: Check if terminal eSE1 is available.
05-24 19:06:46.139   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.secure_element@1.2::ISecureElement/eSE1 in either framework or device VINTF manifest.
05-24 19:06:46.140   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.secure_element@1.1::ISecureElement/eSE1 in either framework or device VINTF manifest.
05-24 19:06:46.141   580   580 I hwservicemanager: getTransport: Cannot find entry android.hardware.secure_element@1.0::ISecureElement/eSE1 in either framework or device VINTF manifest.
05-24 19:06:46.143  2151  2151 I SecureElementService: No HAL implementation for eSE1
05-24 19:06:46.143  2151  2151 I SecureElementService: Check if terminal SIM1 is available.
05-24 19:06:46.145   580   580 I hwservicemanager: Since android.hardware.secure_element@1.2::ISecureElement/SIM1 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 19:06:46.147   580   580 I hwservicemanager: Since android.hardware.secure_element@1.2::ISecureElement/SIM1 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 19:06:46.147  2151  2151 I HidlServiceManagement: getService: Trying again for android.hardware.secure_element@1.2::ISecureElement/SIM1...
05-24 19:06:46.148   580  2201 I hwservicemanager: Tried to start android.hardware.secure_element@1.2::ISecureElement/SIM1 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 19:06:46.149   580  2205 I hwservicemanager: Tried to start android.hardware.secure_element@1.2::ISecureElement/SIM1 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 19:06:46.154  1400  1554 I ServiceWatcher: [fused] connected to {com.android.location.fused/com.android.location.fused.FusedLocationService}
05-24 19:06:46.157  1400  1400 I ExplicitHealthCheckController: Service not ready to get health check supported packages. Binding...
05-24 19:06:46.158  1400  1400 I ExplicitHealthCheckController: Explicit health check service is bound
05-24 19:06:46.160  1952  1952 E SharedConnectivityManager: To support shared connectivity service on this device, the service's package name and intent action strings must not be empty
05-24 19:06:46.165  1400  1400 W TrustManagerService: EXTRA_USER_HANDLE missing or invalid, value=0
05-24 19:06:46.169  1400  1554 I ServiceWatcher: [GeocoderProxy] chose new implementation null
05-24 19:06:46.169  1400  1554 I ServiceWatcher: [HardwareActivityRecognitionProxy] chose new implementation null
05-24 19:06:46.169  1400  1554 I ServiceWatcher: [GeofenceProxy] chose new implementation null
05-24 19:06:46.171  1400  1554 I ServiceWatcher: [TrustManagerService] chose new implementation null
05-24 19:06:46.176  1400  1807 W BroadcastLoopers: Found previously unknown looper Thread[AudioService Broadcast,5,main]
05-24 19:06:46.176  1400  1400 I ConnectivityModuleConnector: Networking module service connected
05-24 19:06:46.177  1400  1400 I NetworkStackClient: Network stack service connected
05-24 19:06:46.199  1400  1400 I Telecom : SystemStateHelper: Registering broadcast receiver: android.content.IntentFilter@3bc8ca3: TS.init@AAA
05-24 19:06:46.199  1400  1400 I Telecom : SystemStateHelper: Registering broadcast receiver: android.content.IntentFilter@333bba0: TS.init@AAA
05-24 19:06:46.205  1400  1400 I Telecom : CallAudioRouteController: calculateSupportedRouteMaskInit: is wired headset plugged in - false: TS.init@AAA
05-24 19:06:46.205  1400  1400 I Telecom : AudioRoute$Factory: createRetry; type=TYPE_SPEAKER, address=null, retryCount=2: TS.init@AAA
05-24 19:06:46.205  1400  1400 I Telecom : AudioRoute$Factory: type: 1: TS.init@AAA
05-24 19:06:46.205  1400  1400 I Telecom : AudioRoute$Factory: type: 2: TS.init@AAA
05-24 19:06:46.205  1400  1400 I Telecom : AudioRoute$Factory: createRetry; type=TYPE_EARPIECE, address=null, retryCount=2: TS.init@AAA
05-24 19:06:46.205  1400  1400 I Telecom : AudioRoute$Factory: type: 1: TS.init@AAA
05-24 19:06:46.211  1400  2217 I Telecom : CallAudioModeStateMachine: Audio focus entering UNFOCUSED state
05-24 19:06:46.211  1400  2217 I Telecom : CallAudioModeStateMachine: Message received: null.: TS.init->CAMSM.pM_1@AAA
05-24 19:06:46.215  1400  1400 I Telecom : MissedCallNotifierImpl: reloadFromDatabase: Boot not yet complete -- call log db may not be available. Deferring loading until boot complete for user 0: TS.init@AAA
05-24 19:06:46.219  1952  2027 E AppWidgetManager: Notify service of inheritance info
05-24 19:06:46.219  1952  2027 E AppWidgetManager: 	at com.android.internal.appwidget.IAppWidgetService$Stub$Proxy.getInstalledProvidersForProfile(IAppWidgetService.java:1071)
05-24 19:06:46.234  1400  1400 I ContentSuggestionsManagerService: Updating for user 0: disabled=false
05-24 19:06:46.235  1400  1400 E ContentSuggestionsPerUserService: Bad service name: com.google.android.as/com.google.android.apps.miphone.aiai.app.AiAiContentSuggestionsService
05-24 19:06:46.235  1400  1400 I AutofillManagerService: Updating for user 0: disabled=false
05-24 19:06:46.235  1400  1400 E AutofillManagerServiceImpl: Bad service name: proton.android.pass/proton.android.pass.autofill.ProtonPassAutofillService
05-24 19:06:46.236  2171  2171 E CarrierIdProvider: read carrier list from ota pb failure: java.io.FileNotFoundException: /data/misc/carrierid/carrier_list.pb: open failed: ENOENT (No such file or directory)
05-24 19:06:46.242  1400  1400 W ActivityManager: Unable to start service Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener } U=0: not found
05-24 19:06:46.242  1400  1400 W NotificationManagerService.NotificationListeners: Unable to bind notification listener service: Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener (has extras) } in user 0
05-24 19:06:46.251  1400  1400 I ConnectivityModuleConnector: Networking module service connected
05-24 19:06:46.251  1400  1554 W PermissionService: getPermissionFlags: Unknown user -1
05-24 19:06:46.251  1400  1554 W PermissionService: getPermissionFlags: Unknown user -1
05-24 19:06:46.253  1400  1566 I ActivityManager: Start proc 2226:com.android.permissioncontroller/u0a266 for broadcast {com.android.permissioncontroller/com.android.permissioncontroller.privacysources.SafetyCenterReceiver}
05-24 19:06:46.266  2226  2226 W ssioncontroller: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 19:06:46.277  1952  2039 I CameraManagerGlobal: Connecting to camera service
05-24 19:06:46.297  1056  2207 I AttributionAndPermissionUtils: checkPermission checkPermission (forDataDelivery 0 startDataDelivery 0): Permission soft denied for client attribution [uid 10226, pid 1952, packageName "<unknown>"]
05-24 19:06:46.304   956  2250 W gpuservice: AIBinder_linkToDeath is being called with a non-null cookie and no onUnlink callback set. This might not be intended. AIBinder_DeathRecipient_setOnUnlinked should be called first.
05-24 19:06:46.313  1952  1952 I SystemUIService: Found SurfaceFlinger's GPU Priority: 13143
05-24 19:06:46.313  1952  1952 I SystemUIService: Setting SysUI's GPU Context priority to: 12545
05-24 19:06:46.318  2171  2171 E RILJ    : Feature android.hardware.telephony.data is declared, but service DATA is missing [PHONE0]
05-24 19:06:46.318  2171  2171 E RILJ    : Feature android.hardware.telephony.calling is declared, but service VOICE is missing [PHONE0]
05-24 19:06:46.319  2171  2171 E RILJ    : Feature android.hardware.telephony.ims is declared, but service IMS is missing [PHONE0]
05-24 19:06:46.319  2171  2171 E RILJ    : Feature android.hardware.telephony.messaging is declared, but service MESSAGING is missing [PHONE0]
05-24 19:06:46.321  2174  2174 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 19:06:46.347  2171  2171 E RILJ    : Feature android.hardware.telephony.data is declared, but service DATA is missing [PHONE1]
05-24 19:06:46.348  2171  2171 E RILJ    : Feature android.hardware.telephony.calling is declared, but service VOICE is missing [PHONE1]
05-24 19:06:46.349  2171  2171 E RILJ    : Feature android.hardware.telephony.ims is declared, but service IMS is missing [PHONE1]
05-24 19:06:46.351  2171  2171 E RILJ    : Feature android.hardware.telephony.messaging is declared, but service MESSAGING is missing [PHONE1]
05-24 19:06:46.372  1400  2066 W WifiService: Attempt to retrieve WifiConfiguration with invalid scanResult List
05-24 19:06:46.374  1400  1557 W KeyguardServiceDelegate: onScreenTurningOn(): no keyguard service!
05-24 19:06:46.380  1400  1727 W WifiService: Attempt to retrieve WifiConfiguration with invalid scanResult List
05-24 19:06:46.381  1400  2244 E WifiService: Attempt to retrieve passpoint with invalid scanResult List
05-24 19:06:46.381  1400  2066 W WifiService: Attempt to retrieve OsuProviders with invalid scanResult List
05-24 19:06:46.417  1952  2285 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 19:06:46.437  1952  2285 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 19:06:46.440  1400  1566 I ActivityManager: Start proc 2296:com.android.launcher3/u0a230 for service {com.android.launcher3/com.android.quickstep.TouchInteractionService}
05-24 19:06:46.472  2298  2298 W receiver.module: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 19:06:46.485  2296  2296 W droid.launcher3: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 19:06:46.522  2171  2171 E SatelliteController: SatelliteController was not yet initialized.
05-24 19:06:46.534  2296  2296 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-24 19:06:46.547  2296  2296 I QuickstepProtoLogGroup: Initializing ProtoLog.
05-24 19:06:46.564  1400  1566 I ActivityManager: Start proc 2343:com.android.smspush/u0a237 for service {com.android.smspush/com.android.smspush.WapPushManager}
05-24 19:06:46.584  2343  2343 W android.smspush: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 19:06:46.635  2296  2360 E FileLog : java.io.FileNotFoundException: /data/user/0/com.android.launcher3/files/log-0: open failed: ENOENT (No such file or directory)
05-24 19:06:46.635  2296  2360 E FileLog : 	at java.io.FileOutputStream.<init>(FileOutputStream.java:259)
05-24 19:06:46.635  2296  2360 E FileLog : 	at java.io.FileWriter.<init>(FileWriter.java:113)
05-24 19:06:46.635  2296  2360 E FileLog : Caused by: android.system.ErrnoException: open failed: ENOENT (No such file or directory)
05-24 19:06:46.641  2343  2343 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-24 19:06:46.655  2171  2171 E EmergencyNumberTracker: [0]Cache ota emergency database IOException: java.io.FileNotFoundException: /data/misc/emergencynumberdb/emergency_number_db: open failed: ENOENT (No such file or directory)
05-24 19:06:46.660  2298  2361 E CellBroadcastConfigService: fails to setCellBroadcastRanges
05-24 19:06:46.672  2298  2361 E CellBroadcastConfigService: fails to setCellBroadcastRanges
05-24 19:06:46.681  1400  2242 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-24 19:06:46.684  1400  1727 I DefaultPermGrantPolicy: Granting permissions to enabled data services for user:0
05-24 19:06:46.698  1400  1727 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-24 19:06:46.699  1400  2214 W Telecom : BluetoothDeviceManager: getBluetoothHeadset: Acquire BluetoothHeadset service failed due to: java.util.concurrent.TimeoutException
05-24 19:06:46.699  1400  2214 I Telecom : BluetoothRouteManager: getBluetoothAudioConnectedDevice: no service available.
05-24 19:06:46.700  1400  2216 I Telecom : CallAudioRouteController: Message received: BT_AUDIO_DISCONNECTED=1301, arg1=0
05-24 19:06:46.719  1952  1952 E SharedConnectivityManager: To support shared connectivity service on this device, the service's package name and intent action strings must not be empty
05-24 19:06:46.739  2171  2171 E SatelliteController: SatelliteController was not yet initialized.
05-24 19:06:46.791  2171  2171 E EmergencyNumberTracker: [1]Cache ota emergency database IOException: java.io.FileNotFoundException: /data/misc/emergencynumberdb/emergency_number_db: open failed: ENOENT (No such file or directory)
05-24 19:06:46.800  1400  2242 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-24 19:06:46.801  1400  2242 I DefaultPermGrantPolicy: Granting permissions to enabled data services for user:0
05-24 19:06:46.811  1400  2167 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-24 19:06:46.904  2171  2379 I ImsResolver: Initializing cache.
05-24 19:06:46.917  1400  2166 I MR2ServiceImpl: registerManager | callerUid: 10226, callerPid: 1952, callerPackage: com.android.systemui, targetPackageName: null, targetUserId: UserHandle{0}, hasMediaRoutingControl: false
05-24 19:06:46.918  2171  2171 E SatelliteModemInterface: Unable to bind to the satellite service because the package is undefined.
05-24 19:06:46.923  1400  1905 I MR2ServiceImpl: addProviderRoutes | provider: com.android.server.media/.SystemMediaRoute2Provider, routes: [ROUTE_ID_BUILTIN_SPEAKER | Phone]
05-24 19:06:46.931  1400  1905 I AS.AudioService: removePreferredDevicesForStrategy strat:5
05-24 19:06:46.965  2171  2171 I TelephonyRcsService: updateFeatureControllers: oldSlots=0, newNumSlots=2
05-24 19:06:47.018  1400  1400 I AS.AudioService: onSubscriptionsChanged()
05-24 19:06:47.023  1400  1803 I VcnManagementService: new snapshot: TelephonySubscriptionSnapshot{ mActiveDataSubId=2, mSubIdToInfoMap={}, mSubIdToCarrierConfigMap={}, mPrivilegedPackages={} }
05-24 19:06:47.040  1400  1564 E StatsPullAtomService: subInfo of subId 2 is invalid, ignored.
05-24 19:06:47.043  1400  1564 E StatsPullAtomService: subInfo of subId 1 is invalid, ignored.
05-24 19:06:47.043  1400  1400 I AS.AudioService: onSubscriptionsChanged()
05-24 19:06:47.066  2171  2171 E RILJ    : getImei not supported on service MODEM < 2.1. [PHONE0]
05-24 19:06:47.072  2171  2171 E RILJ    : setNullCipherAndIntegrityEnabled not supported on service NETWORK < 2.1. [PHONE0]
05-24 19:06:47.073  2171  2171 E RILJ    : setCellularIdentifierTransparencyEnabled not supported on service NETWORK < 2.2. [PHONE0]
05-24 19:06:47.075  2171  2171 E RILJ    : setSecurityAlgorithmsUpdatedEnabled not supported on service NETWORK < 2.2. [PHONE0]
05-24 19:06:47.086  2171  2171 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-24 19:06:47.090  1400  1564 I StatsPullAtomService: subId 2 added into historical sub list
05-24 19:06:47.099  2171  2171 E NRM-C-0 : service not connected. Domain = PS
05-24 19:06:47.099  2171  2171 E NRM-C-0 : service not connected. Domain = CS
05-24 19:06:47.099  2171  2171 E NRM-I-0 : service not connected. Domain = PS
05-24 19:06:47.110  2171  2171 E NRM-C-0 : service not connected. Domain = PS
05-24 19:06:47.111  2171  2171 E NRM-C-0 : service not connected. Domain = CS
05-24 19:06:47.111  2171  2171 E NRM-I-0 : service not connected. Domain = PS
05-24 19:06:47.140  1952  2400 I Codec2Client: Available Codec2 services: "default" "software"
05-24 19:06:47.148  2151  2151 W HidlServiceManagement: Waited one second for android.hardware.secure_element@1.2::ISecureElement/SIM1
05-24 19:06:47.148   580   580 I hwservicemanager: Since android.hardware.secure_element@1.2::ISecureElement/SIM1 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 19:06:47.149  2151  2151 I HidlServiceManagement: getService: Trying again for android.hardware.secure_element@1.2::ISecureElement/SIM1...
05-24 19:06:47.168  2171  2171 E RILJ    : getImei not supported on service MODEM < 2.1. [PHONE1]
05-24 19:06:47.170  2171  2171 E RILJ    : setNullCipherAndIntegrityEnabled not supported on service NETWORK < 2.1. [PHONE1]
05-24 19:06:47.170  2171  2171 E RILJ    : setCellularIdentifierTransparencyEnabled not supported on service NETWORK < 2.2. [PHONE1]
05-24 19:06:47.171  2171  2171 E RILJ    : setSecurityAlgorithmsUpdatedEnabled not supported on service NETWORK < 2.2. [PHONE1]
05-24 19:06:47.175  2171  2171 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-24 19:06:47.181  2171  2171 E NRM-C-1 : service not connected. Domain = PS
05-24 19:06:47.182  2171  2171 E NRM-C-1 : service not connected. Domain = CS
05-24 19:06:47.182  2171  2171 E NRM-I-1 : service not connected. Domain = PS
05-24 19:06:47.188   580  2403 I hwservicemanager: Tried to start android.hardware.secure_element@1.2::ISecureElement/SIM1 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 19:06:47.191  2171  2171 E NRM-C-1 : service not connected. Domain = PS
05-24 19:06:47.191  2171  2171 E NRM-C-1 : service not connected. Domain = CS
05-24 19:06:47.191  2171  2171 E NRM-I-1 : service not connected. Domain = PS
05-24 19:06:47.226  1400  2244 W Binder  : java.lang.SecurityException: Need REGISTER_STATS_PULL_ATOM permission.: Neither user 10226 nor current process has android.permission.REGISTER_STATS_PULL_ATOM.
05-24 19:06:47.226  1400  2244 W Binder  : 	at android.app.ContextImpl.enforceCallingOrSelfPermission(ContextImpl.java:2630)
05-24 19:06:47.226  1400  2244 W Binder  : 	at com.android.server.stats.StatsManagerService.enforceRegisterStatsPullAtomPermission(StatsManagerService.java:678)
05-24 19:06:47.226  1400  2244 W Binder  : 	at com.android.server.stats.StatsManagerService.registerPullAtomCallback(StatsManagerService.java:219)
05-24 19:06:47.226  1400  2244 W Binder  : 	at android.os.IStatsManagerService$Stub.onTransact(IStatsManagerService.java:434)
05-24 19:06:47.231  1400  2166 I StatusBarManagerService: registerStatusBar bar=com.android.internal.statusbar.IStatusBar$Stub$Proxy@c02f22
05-24 19:06:47.240  1952  1952 I KeyguardSecurityView: Switching mode from Uninitialized to Default
05-24 19:06:47.253  1952  1952 E OverviewProxyService: Failed to get overview proxy for disable flags.
05-24 19:06:47.276  1952  1952 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 19:06:47.276  1400  1564 I StatsPullAtomService: subId 1 added into historical sub list
05-24 19:06:47.286  1952  1952 E OverviewProxyService: Failed to get overview proxy for disable flags.
05-24 19:06:47.306  1952  1952 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 19:06:47.318  1952  1952 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 19:06:47.343  1400  2166 W UserManagerService: Requested status bar icon for non-badged user 0
05-24 19:06:47.353  2171  2171 I Telephony: TelecomAccountRegistry$AccountEntry: registerPstnPhoneAccount: Registering account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with Telecom. subId=-1
05-24 19:06:47.356  1400  2167 I Telecom : TelecomServiceImpl$1: registerPhoneAccount: account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@AA0
05-24 19:06:47.357  1400  2167 I Telecom : PhoneAccountRegistrar: New phone account registered: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@AA0
05-24 19:06:47.361  1400  2167 I Telecom : CallsManager: Sending phone-account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} registered intent as user: TSI.rPA(cap)@AA0
05-24 19:06:47.364  1400  2167 I Telecom : PhoneAccountRegistrar: Notifying telephony of voice service override change for 1 SIMs, hasService = false: TSI.rPA(cap)@AA0
05-24 19:06:47.364  1400  2167 I Telecom : PhoneAccountRegistrar: maybeNotifyTelephonyForVoiceServiceState: simTm is null.: TSI.rPA(cap)@AA0
05-24 19:06:47.364  2171  2171 I Telephony: TelecomAccountRegistry$AccountEntry: Registered phoneAccount: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with handle: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}
05-24 19:06:47.366  2171  2171 I Telephony: TelecomAccountRegistry: Unregistering phone account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 2, UserHandle{0}.
05-24 19:06:47.368  1400  2166 I Telecom : CallsManager: Sending phone-account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 2, UserHandle{0} unregistered intent as user: TSI.uPA(cap)@AA8
05-24 19:06:47.369  2171  2171 I Telephony: TelecomAccountRegistry: Unregistering phone account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 1, UserHandle{0}.
05-24 19:06:47.370  1400  2066 I Telecom : CallsManager: Sending phone-account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 1, UserHandle{0} unregistered intent as user: TSI.uPA(cap)@ABA
05-24 19:06:47.387  1952  1952 I SystemUIService: Topological CoreStartables completed in 2 iterations
05-24 19:06:47.387  1952  1952 E OverviewProxyService: Failed to get overview proxy for disable flags.
05-24 19:06:47.402  1952  1952 W SimLog  : invalid subId in handleServiceStateChange()
05-24 19:06:47.403  1400  2244 W UserManagerService: Requested status bar icon for non-badged user 0
05-24 19:06:47.443  1290  1290 E bootanimation: === MALI DEBUG ===eglp_check_display_valid_and_initialized_and_retain retun EGL_NOT_INITIALIZED
05-24 19:06:47.462  2171  2171 I Telephony: TelecomAccountRegistry$AccountEntry: registerPstnPhoneAccount: Registering account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with Telecom. subId=-1
05-24 19:06:47.463  1400  2167 I Telecom : TelecomServiceImpl$1: registerPhoneAccount: account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABE
05-24 19:06:47.465  1400  2167 I Telecom : PhoneAccountRegistrar: Modify account: [ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}]: TSI.rPA(cap)@ABE
05-24 19:06:47.467  1400  2167 I Telecom : CallsManager: handlePhoneAccountChanged: phoneAccount=[[X] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABE
05-24 19:06:47.469  1400  2167 I Telecom : PhoneAccountRegistrar: Notifying telephony of voice service override change for 1 SIMs, hasService = false: TSI.rPA(cap)@ABE
05-24 19:06:47.470  1400  2167 I Telecom : PhoneAccountRegistrar: maybeNotifyTelephonyForVoiceServiceState: simTm is null.: TSI.rPA(cap)@ABE
05-24 19:06:47.470  2171  2171 I Telephony: TelecomAccountRegistry$AccountEntry: Registered phoneAccount: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with handle: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}
05-24 19:06:47.472  1400  1400 I AS.AudioService: onSubscriptionsChanged()
05-24 19:06:47.475  1400  1400 I AS.AudioService: onSubscriptionsChanged()
05-24 19:06:47.477  1400  1400 I AS.AudioService: onSubscriptionsChanged()
05-24 19:06:47.478  1400  1400 I AS.AudioService: onSubscriptionsChanged()
05-24 19:06:47.494  2171  2171 E SST     : [0] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-24 19:06:47.494  2171  2171 E SST     : [0] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-24 19:06:47.494  2171  2171 E SST     : [0] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-24 19:06:47.494  2171  2171 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-24 19:06:47.495  2171  2171 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-24 19:06:47.505  1400  2242 W UserManagerService: Requested status bar icon for non-badged user 0
05-24 19:06:47.525  2171  2171 E NRM-C-0 : service not connected. Domain = PS
05-24 19:06:47.525  2171  2171 E NRM-C-0 : service not connected. Domain = CS
05-24 19:06:47.525  2171  2171 E NRM-I-0 : service not connected. Domain = PS
05-24 19:06:47.535  2171  2171 E SST     : [1] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-24 19:06:47.535  2171  2171 E SST     : [1] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-24 19:06:47.535  2171  2171 E SST     : [1] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-24 19:06:47.541  2171  2171 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-24 19:06:47.541  2171  2171 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-24 19:06:47.578  2171  2171 E NRM-C-1 : service not connected. Domain = PS
05-24 19:06:47.578  2171  2171 E NRM-C-1 : service not connected. Domain = CS
05-24 19:06:47.578  2171  2171 E NRM-I-1 : service not connected. Domain = PS
05-24 19:06:47.632   960  1762 I PowerHalLoader: Successfully connected to Power HAL AIDL service.
05-24 19:06:47.635  1400  1557 I SystemServiceManager: Starting phase 1000
05-24 19:06:47.635  1400  1557 E PowerStatsService: Failed to start PowerStatsService loggers
05-24 19:06:47.644  1400  1557 I TransparencyService: Boot completed. Getting boot integrity data.
