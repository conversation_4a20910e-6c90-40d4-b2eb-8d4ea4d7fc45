05-24 20:21:07.630   575   575 I hwservicemanager: getFrameworkHalManifest: Reading VINTF information.
05-24 20:21:07.633   574   574 I auditd  : SELinux: Loaded service context from:
05-24 20:21:07.633   574   574 I auditd  : 		/system/etc/selinux/plat_service_contexts
05-24 20:21:07.633   574   574 I auditd  : 		/system_ext/etc/selinux/system_ext_service_contexts
05-24 20:21:07.633   574   574 I auditd  : 		/product/etc/selinux/product_service_contexts
05-24 20:21:07.633   574   574 I auditd  : 		/vendor/etc/selinux/vendor_service_contexts
05-24 20:21:07.633   574   574 I auditd  : 		/odm/etc/selinux/odm_service_contexts
05-24 20:21:07.639   575   575 I hwservicemanager: Loaded APEX Infos from /bootstrap-apex/apex-info-list.xml
05-24 20:21:07.639   575   575 I hwservicemanager: getDeviceHalManifest: Reading VINTF information.
05-24 20:21:07.658   575   575 I hwservicemanager: Loaded APEX Infos from /bootstrap-apex/apex-info-list.xml
05-24 20:21:06.988     1     1 I auditd  : type=1107 audit(0.0:3): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_cam_stereo_camera_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=1'
05-24 20:21:06.988     1     1 I /system/bin/init: type=1107 audit(0.0:3): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_cam_stereo_camera_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=1'
05-24 20:21:06.988     1     1 I auditd  : type=1107 audit(0.0:4): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.build.fingerprint pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:fingerprint_prop:s0 tclass=property_service permissive=1'
05-24 20:21:06.988     1     1 I /system/bin/init: type=1107 audit(0.0:4): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.build.fingerprint pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:fingerprint_prop:s0 tclass=property_service permissive=1'
05-24 20:21:07.661   575   575 I hwservicemanager: getDeviceHalManifest: Successfully processed VINTF information
05-24 20:21:07.661   575   575 I hwservicemanager: getFrameworkHalManifest: Successfully processed VINTF information
05-24 20:21:07.666   575   575 I hwservicemanager: hwservicemanager is ready now.
05-24 20:21:07.807     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvdata" to "5": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-24 20:21:07.813     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvdata" to "5": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-24 20:21:07.820     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvcfg" to "6": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-24 20:21:07.827     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvcfg" to "6": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-24 20:21:07.880   632   632 I auditd  : type=1400 audit(0.0:5): avc:  denied  { execute_no_trans } for  comm="init" path="/vendor/bin/sh" dev="dm-1" ino=276 scontext=u:r:init:s0 tcontext=u:object_r:vendor_shell_exec:s0 tclass=file permissive=1
05-24 20:21:07.880   632   632 I init    : type=1400 audit(0.0:5): avc:  denied  { execute_no_trans } for  path="/vendor/bin/sh" dev="dm-1" ino=276 scontext=u:r:init:s0 tcontext=u:object_r:vendor_shell_exec:s0 tclass=file permissive=1
05-24 20:21:07.888   634   634 I auditd  : type=1400 audit(0.0:6): avc:  denied  { execute_no_trans } for  comm="sh" path="/vendor/bin/toybox_vendor" dev="dm-1" ino=311 scontext=u:r:init:s0 tcontext=u:object_r:vendor_toolbox_exec:s0 tclass=file permissive=1
05-24 20:21:07.888   634   634 I sh      : type=1400 audit(0.0:6): avc:  denied  { execute_no_trans } for  path="/vendor/bin/toybox_vendor" dev="dm-1" ino=311 scontext=u:r:init:s0 tcontext=u:object_r:vendor_toolbox_exec:s0 tclass=file permissive=1
05-24 20:21:07.892   634   634 I auditd  : type=1400 audit(0.0:7): avc:  denied  { execute } for  comm="cat" path="/vendor/lib64/libcrypto.so" dev="dm-1" ino=1784 scontext=u:r:init:s0 tcontext=u:object_r:same_process_hal_file:s0 tclass=file permissive=1
05-24 20:21:07.892   634   634 I cat     : type=1400 audit(0.0:7): avc:  denied  { execute } for  path="/vendor/lib64/libcrypto.so" dev="dm-1" ino=1784 scontext=u:r:init:s0 tcontext=u:object_r:same_process_hal_file:s0 tclass=file permissive=1
05-24 20:21:07.953   644   644 I HidlServiceManagement: Registered android.system.suspend@1.0::ISystemSuspend/default
05-24 20:21:07.953   644   644 I HidlServiceManagement: Removing namespace from process name android.system.suspend-service to suspend-service.
05-24 20:21:07.959   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.boot@1.0::IBootControl/default in either framework or device VINTF manifest.
05-24 20:21:07.968   646   646 I <EMAIL>: Trustonic Keymaster 4.1 Service starts
05-24 20:21:07.982   646   646 I HidlServiceManagement: Registered android.hardware.keymaster@4.1::IKeymasterDevice/default
05-24 20:21:07.982   646   646 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 20:21:07.982   646   646 I <EMAIL>: Trustonic Keymaster 4.1 Service registered
05-24 20:21:07.989   645   645 I keystore2: system/security/keystore2/src/keystore2_main.rs:154 - Successfully registered Keystore 2.0 service.
05-24 20:21:08.047   574   653 W libc    : Unable to set property "ctl.interface_start" to "aidl/android.system.keystore2.IKeystoreService/default": PROP_ERROR_HANDLE_CONTROL_MESSAGE (0x20)
05-24 20:21:08.067   665   665 I libperfmgr: Pixel Power HAL AIDL Service with Extension is starting with config: /vendor/etc/powerhint.json
05-24 20:21:08.069   665   665 I libperfmgr: Failed to read Node[18]'s ResetOnInit, set to 'false'
05-24 20:21:08.069   665   665 I libperfmgr: Failed to read Node[19]'s ResetOnInit, set to 'false'
05-24 20:21:08.069   665   665 I libperfmgr: Failed to read Node[20]'s ResetOnInit, set to 'false'
05-24 20:21:08.069   665   665 I libperfmgr: Failed to read Node[21]'s ResetOnInit, set to 'false'
05-24 20:21:08.071   665   665 I libperfmgr: PowerHint AUDIO_STREAMING_LOW_LATENCY has 3 node actions, and 0 hint actions parsed
05-24 20:21:08.071   665   665 I libperfmgr: Initialized HintManager from JSON config: /vendor/etc/powerhint.json
05-24 20:21:08.072   665   665 I powerhal-libperfmgr: Initialize PowerHAL
05-24 20:21:08.073   665   665 I powerhal-libperfmgr: Pixel Power HAL AIDL Service with Extension is started.
05-24 20:21:08.079   591   591 I vold    : fscrypt_initialize_systemwide_keys
05-24 20:21:08.109   591   591 I incfs   : Initial API level of the device: 30
05-24 20:21:08.126   645   656 E keystore2:     1: system/security/keystore2/src/globals.rs:264: Trying to get Legacy wrapper. Attempt to get keystore compat service for security level r#STRONGBOX
05-24 20:21:08.127   591   591 E vold    : keystore2 Keystore earlyBootEnded returned service specific error: -68
05-24 20:21:08.131   675   675 I tombstoned: tombstoned successfully initialized
05-24 20:21:08.486   816   816 I derive_sdk: extension ad_services version is 15
05-24 20:21:08.653   820   820 I derive_classpath: ReadClasspathFragment /apex/com.android.adservices/etc/classpaths/bootclasspath.pb
05-24 20:21:08.654   820   820 I derive_classpath: ReadClasspathFragment /apex/com.android.btservices/etc/classpaths/bootclasspath.pb
05-24 20:21:08.657   820   820 I derive_classpath: ReadClasspathFragment /apex/com.android.nfcservices/etc/classpaths/bootclasspath.pb
05-24 20:21:08.658   820   820 I derive_classpath: ReadClasspathFragment /apex/com.android.permission/etc/classpaths/bootclasspath.pb
05-24 20:21:08.662   820   820 I derive_classpath: ReadClasspathFragment /apex/com.android.adservices/etc/classpaths/systemserverclasspath.pb
05-24 20:21:08.662   820   820 I derive_classpath: ReadClasspathFragment /apex/com.android.btservices/etc/classpaths/systemserverclasspath.pb
05-24 20:21:08.665   820   820 I derive_classpath: ReadClasspathFragment /apex/com.android.permission/etc/classpaths/systemserverclasspath.pb
05-24 20:21:08.667   820   820 I derive_classpath: export BOOTCLASSPATH /apex/com.android.art/javalib/core-oj.jar:/apex/com.android.art/javalib/core-libart.jar:/apex/com.android.art/javalib/okhttp.jar:/apex/com.android.art/javalib/bouncycastle.jar:/apex/com.android.art/javalib/apache-xml.jar:/system/framework/framework.jar:/system/framework/framework-graphics.jar:/system/framework/framework-location.jar:/system/framework/framework-connectivity-b.jar:/system/framework/ext.jar:/system/framework/telephony-common.jar:/system/framework/voip-common.jar:/system/framework/ims-common.jar:/system/framework/framework-platformcrashrecovery.jar:/system/framework/framework-ondeviceintelligence-platform.jar:/system/framework/mediatek-common.jar:/system/framework/mediatek-framework.jar:/system/framework/mediatek-ims-base.jar:/system/framework/mediatek-ims-common.jar:/system/framework/mediatek-telecom-common.jar:/system/framework/mediatek-telephony-base.jar:/system/framework/mediatek-telephony-common.jar:/apex/com.android.i18n/javalib/core-icu4j.jar:/apex/com.android.adservices/javalib/framework-adservices.jar:/apex/com.android.adservices/javalib/framework-sdksandbox.jar:/apex/com.android.appsearch/javalib/framework-appsearch.jar:/apex/com.android.btservices/javalib/framework-bluetooth.jar:/apex/com.android.configinfrastructure/javalib/framework-configinfrastructure.jar:/apex/com.android.conscrypt/javalib/conscrypt.jar:/apex/com.android.devicelock/javalib/framework-devicelock.jar:/apex/com.android.healthfitness/javalib/framework-healthfitness.jar:/apex/com.android.ipsec/javalib/android.net.ipsec.ike.jar:/apex/com.android.media/javalib/updatable-media.jar:/apex/com.android.mediaprovider/javalib/framework-mediaprovider.jar:/apex/com.android.mediaprovider/javalib/framework-pdf.jar:/apex/com.android.mediaprovider/javalib/framework-pdf-v.jar:/apex/com.android.mediaprovider/javalib/framework-photopicker.jar:/apex/com.android.nfcservices/javalib/framework-nfc.jar:/apex/com.android.ondevicepersonalization/javalib/framework-ondevicepersonalization.jar:/apex/com.android.os.statsd/javalib/framework-statsd.jar:/apex/com.android.permission/javalib/framework-permission.jar:/apex/com.android.permission/javalib/framework-permission-s.jar:/apex/com.android.profiling/javalib/framework-profiling.jar:/apex/com.android.scheduling/javalib/framework-scheduling.jar:/apex/com.android.sdkext/javalib/framework-sdkextensions.jar:/apex/com.android.tethering/javalib/framework-connectivity.jar:/apex/com.android.tethering/javalib/framework-connectivity-t.jar:/apex/com.android.tethering/javalib/framework-tethering.jar:/apex/com.android.uwb/javalib/framework-uwb.jar:/apex/com.android.virt/javalib/framework-virtualization.jar:/apex/com.android.wifi/javalib/framework-wifi.jar
05-24 20:21:08.667   820   820 I derive_classpath: export SYSTEMSERVERCLASSPATH /system/framework/com.android.location.provider.jar:/system/framework/services.jar:/apex/com.android.adservices/javalib/service-adservices.jar:/apex/com.android.adservices/javalib/service-sdksandbox.jar:/apex/com.android.appsearch/javalib/service-appsearch.jar:/
05-24 20:21:08.702   821   821 I art_boot: Property persist.device_config.runtime_native_boot.useartservice not set
05-24 20:21:08.727   822   822 W odsign  : Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.system.keystore2.IKeystoreService/default
05-24 20:21:08.739   822   822 I odsign  : Initialized Keystore key.
05-24 20:21:09.805   574   574 I auditd  : SELinux: Loaded service context from:
05-24 20:21:09.806   574   574 I auditd  : 		/system/etc/selinux/plat_service_contexts
05-24 20:21:09.806   574   574 I auditd  : 		/system_ext/etc/selinux/system_ext_service_contexts
05-24 20:21:09.806   574   574 I auditd  : 		/product/etc/selinux/product_service_contexts
05-24 20:21:09.806   574   574 I auditd  : 		/vendor/etc/selinux/vendor_service_contexts
05-24 20:21:09.806   574   574 I auditd  : 		/odm/etc/selinux/odm_service_contexts
05-24 20:21:09.822   883   883 I netdClient: Skipping libnetd_client init since *we* are netd
05-24 20:21:09.824     1     1 I auditd  : type=1400 audit(0.0:8): avc:  denied  { create } for  comm="init" name="als_correction" scontext=u:r:init:s0 tcontext=u:object_r:socket_device:s0 tclass=sock_file permissive=1
05-24 20:21:09.824     1     1 I init    : type=1400 audit(0.0:8): avc:  denied  { create } for  name="als_correction" scontext=u:r:init:s0 tcontext=u:object_r:socket_device:s0 tclass=sock_file permissive=1
05-24 20:21:09.824     1     1 I auditd  : type=1400 audit(0.0:9): avc:  denied  { setattr } for  comm="init" name="als_correction" dev="tmpfs" ino=15754 scontext=u:r:init:s0 tcontext=u:object_r:socket_device:s0 tclass=sock_file permissive=1
05-24 20:21:09.824     1     1 I init    : type=1400 audit(0.0:9): avc:  denied  { setattr } for  name="als_correction" dev="tmpfs" ino=15754 scontext=u:r:init:s0 tcontext=u:object_r:socket_device:s0 tclass=sock_file permissive=1
05-24 20:21:09.827   889   889 E zygisk-core64: Retrying to connect to zygiskd, sleep 1s failed with 2: No such file or directory
05-24 20:21:09.828   899   899 I auditd  : type=1400 audit(0.0:13): avc:  denied  { execute_no_trans } for  comm="init" path="/system_ext/bin/als_correction_service" dev="dm-4" ino=74 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=1
05-24 20:21:09.828   899   899 I init    : type=1400 audit(0.0:13): avc:  denied  { execute_no_trans } for  path="/system_ext/bin/als_correction_service" dev="dm-4" ino=74 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=1
05-24 20:21:09.835   883   883 I NetdUpdatable: libnetd_updatable_init: Initializing
05-24 20:21:09.835   883   883 I NetdUpdatable: initMaps successfully
05-24 20:21:09.835   883   883 I netd    : libnetd_updatable_init success
05-24 20:21:09.839   575   575 I hwservicemanager: getFrameworkHalManifest: Reloading VINTF information.
05-24 20:21:09.839   575   575 I hwservicemanager: getFrameworkHalManifest: Reading VINTF information.
05-24 20:21:09.843   575   575 I hwservicemanager: Loaded APEX Infos from /apex/apex-info-list.xml
05-24 20:21:09.844   575   575 I hwservicemanager: getDeviceHalManifest: Reloading VINTF information.
05-24 20:21:09.844   575   575 I hwservicemanager: getDeviceHalManifest: Reading VINTF information.
05-24 20:21:09.859   575   575 I hwservicemanager: Loaded APEX Infos from /apex/apex-info-list.xml
05-24 20:21:09.860   883   883 I netd    : Initializing RouteController: 913us
05-24 20:21:09.860   575   575 I hwservicemanager: getDeviceHalManifest: Successfully processed VINTF information
05-24 20:21:09.860   575   575 I hwservicemanager: getFrameworkHalManifest: Successfully processed VINTF information
05-24 20:21:09.862   902   902 I HidlServiceManagement: Registered android.hidl.allocator@1.0::IAllocator/ashmem
05-24 20:21:09.863   902   902 I HidlServiceManagement: Removing namespace from process name android.hidl.allocator@1.0-service to allocator@1.0-service.
05-24 20:21:09.872   904   904 I mtk.hal.bt@1.0-impl: Init IBluetoothHCI
05-24 20:21:09.876   904   904 I HidlServiceManagement: Registered android.hardware.bluetooth@1.1::IBluetoothHci/default
05-24 20:21:09.876   904   904 I HidlServiceManagement: Removing namespace from process name android.hardware.bluetooth@1.1-service-mediatek to bluetooth@1.1-service-mediatek.
05-24 20:21:09.893   883   883 I netd    : Initializing XfrmController: 33291us
05-24 20:21:09.900   912   912 E android.hardware.gnss-service.mediatek: safe_sendto: safe_sendto() sendto() failed path=[mtk_hal2mnl] ret=-1 reason=[Connection refused]111
05-24 20:21:09.900   912   912 E android.hardware.gnss-service.mediatek: gps_device__get_gps_interface: hal2mnl_hal_reboot failed because of safe_sendto fail ,strerror:Connection refused 
05-24 20:21:09.901   883   883 I resolv  : resolv_init: Initializing resolver
05-24 20:21:09.901   925   925 W <EMAIL>: ThermalHelper:tz_map_version 1
05-24 20:21:09.901   925   925 I <EMAIL>: ThermalWatcherThread started
05-24 20:21:09.909   915   915 I android.hardware.health@2.1-service: default instance initializing with healthd_config...
05-24 20:21:09.912   883   883 I netd    : Registering NetdNativeService: 353us
05-24 20:21:09.912   883   883 I netd    : Registering MDnsService: 500us
05-24 20:21:09.914   927   927 I vtservice_hidl: [VT][SRV]before VTService_HiDL_instantiate
05-24 20:21:09.916   910   910 I HidlServiceManagement: Registered android.hardware.drm@1.4::IDrmFactory/widevine
05-24 20:21:09.917   910   910 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 20:21:09.918   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_agps
05-24 20:21:09.921   912   912 E android.hardware.gnss-service.mediatek: safe_sendto: safe_sendto() sendto() failed path=[mtk_hal2mnl] ret=-1 reason=[Connection refused]111
05-24 20:21:09.921   912   912 E android.hardware.gnss-service.mediatek: gps_device__get_gps_interface: hal2mnl_hal_reboot failed because of safe_sendto fail ,strerror:Connection refused 
05-24 20:21:09.921   926   926 I android.hardware.usb@1.3-service-mediatekv2: UsbGadget
05-24 20:21:09.923   933   933 W <EMAIL>: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.hardware.power.IPower/default
05-24 20:21:09.923   933   933 I <EMAIL>: Connected to power AIDL HAL
05-24 20:21:09.924   883   883 I HidlServiceManagement: Registered android.system.net.netd@1.1::INetd/default
05-24 20:21:09.924   883   883 I netd    : Registering NetdHwService: 11712us
05-24 20:21:09.924   915   915 I HidlServiceManagement: Registered android.hardware.health@2.1::IHealth/default
05-24 20:21:09.925   915   915 I HidlServiceManagement: Removing namespace from process name android.hardware.health@2.1-service to health@2.1-service.
05-24 20:21:09.925   915   915 I android.hardware.health@2.1-service: default: Hal init done
05-24 20:21:09.928   925   925 I HidlServiceManagement: Registered android.hardware.thermal@2.0::IThermal/default
05-24 20:21:09.928   925   925 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 20:21:09.931   910   910 I HidlServiceManagement: Registered android.hardware.drm@1.4::ICryptoFactory/widevine
05-24 20:21:09.931   910   910 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 20:21:09.933   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_wlan
05-24 20:21:09.928   959   959 I auditd  : type=1400 audit(0.0:14): avc:  denied  { execute } for  comm="init" name="vendor.oplus.hardware.olc@2.0-service" dev="dm-3" ino=17 scontext=u:r:init:s0 tcontext=u:object_r:vendor_file:s0 tclass=file permissive=1
05-24 20:21:09.928   959   959 I init    : type=1400 audit(0.0:14): avc:  denied  { execute } for  name="vendor.oplus.hardware.olc@2.0-service" dev="dm-3" ino=17 scontext=u:r:init:s0 tcontext=u:object_r:vendor_file:s0 tclass=file permissive=1
05-24 20:21:09.928   959   959 I auditd  : type=1400 audit(0.0:15): avc:  denied  { execute_no_trans } for  comm="init" path="/odm/bin/hw/vendor.oplus.hardware.olc@2.0-service" dev="dm-3" ino=17 scontext=u:r:init:s0 tcontext=u:object_r:vendor_file:s0 tclass=file permissive=1
05-24 20:21:09.928   959   959 I init    : type=1400 audit(0.0:15): avc:  denied  { execute_no_trans } for  path="/odm/bin/hw/vendor.oplus.hardware.olc@2.0-service" dev="dm-3" ino=17 scontext=u:r:init:s0 tcontext=u:object_r:vendor_file:s0 tclass=file permissive=1
05-24 20:21:09.938   905   905 I HidlServiceManagement: Registered android.hardware.cas@1.2::IMediaCasService/default
05-24 20:21:09.939   905   905 I HidlServiceManagement: Removing namespace from process name android.hardware.cas@1.2-service to cas@1.2-service.
05-24 20:21:09.941   926   926 I HidlServiceManagement: Registered android.hardware.usb@1.3::IUsb/default
05-24 20:21:09.941   926   926 I HidlServiceManagement: Removing namespace from process name android.hardware.usb@1.3-service-mediatekv2 to usb@1.3-service-mediatekv2.
05-24 20:21:09.942   903   903 I HidlServiceManagement: Registered android.hardware.audio@7.1::IDevicesFactory/default
05-24 20:21:09.943   903   903 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-24 20:21:09.943   903   903 I LegacySupport: Registration complete for android.hardware.audio@7.1::IDevicesFactory/default.
05-24 20:21:09.947   933   933 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkpower@1.2::IMtkPower/default
05-24 20:21:09.948   933   933 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 20:21:09.949   912   912 I HidlServiceManagement: Registered android.hardware.gnss@2.1::IGnss/default
05-24 20:21:09.950   912   912 I HidlServiceManagement: Removing namespace from process name android.hardware.gnss-service.mediatek to gnss-service.mediatek.
05-24 20:21:09.951   954   954 E vendor.oplus.hardware.charger@1.0-service: notifyScreenStatus: 0
05-24 20:21:09.955   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_bt
05-24 20:21:09.940   959   959 I auditd  : type=1400 audit(0.0:16): avc:  denied  { execute } for  comm="vendor.oplus.ha" path="/vendor/lib64/libutils.so" dev="dm-1" ino=2081 scontext=u:r:init:s0 tcontext=u:object_r:same_process_hal_file:s0 tclass=file permissive=1
05-24 20:21:09.940   959   959 I vendor.oplus.ha: type=1400 audit(0.0:16): avc:  denied  { execute } for  path="/vendor/lib64/libutils.so" dev="dm-1" ino=2081 scontext=u:r:init:s0 tcontext=u:object_r:same_process_hal_file:s0 tclass=file permissive=1
05-24 20:21:09.944   959   959 I auditd  : type=1400 audit(0.0:17): avc:  denied  { read } for  comm="vendor.oplus.ha" name="vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:09.944   959   959 I vendor.oplus.ha: type=1400 audit(0.0:17): avc:  denied  { read } for  name="vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:09.944   959   959 I auditd  : type=1400 audit(0.0:18): avc:  denied  { write } for  comm="vendor.oplus.ha" name="vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:09.944   959   959 I vendor.oplus.ha: type=1400 audit(0.0:18): avc:  denied  { write } for  name="vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:09.944   959   959 I auditd  : type=1400 audit(0.0:19): avc:  denied  { open } for  comm="vendor.oplus.ha" path="/dev/binderfs/vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:09.944   959   959 I vendor.oplus.ha: type=1400 audit(0.0:19): avc:  denied  { open } for  path="/dev/binderfs/vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:09.944   959   959 I auditd  : type=1400 audit(0.0:20): avc:  denied  { ioctl } for  comm="vendor.oplus.ha" path="/dev/binderfs/vndbinder" dev="binder" ino=6 ioctlcmd=0x6209 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:09.944   959   959 I vendor.oplus.ha: type=1400 audit(0.0:20): avc:  denied  { ioctl } for  path="/dev/binderfs/vndbinder" dev="binder" ino=6 ioctlcmd=0x6209 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:09.944   959   959 I auditd  : type=1400 audit(0.0:21): avc:  denied  { map } for  comm="vendor.oplus.ha" path="/dev/binderfs/vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:09.944   959   959 I vendor.oplus.ha: type=1400 audit(0.0:21): avc:  denied  { map } for  path="/dev/binderfs/vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:09.948   959   959 I auditd  : type=1400 audit(0.0:22): avc:  denied  { create } for  comm="vendor.oplus.ha" scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:09.948   959   959 I vendor.oplus.ha: type=1400 audit(0.0:22): avc:  denied  { create } for  scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:09.948   959   959 I auditd  : type=1400 audit(0.0:23): avc:  denied  { bind } for  comm="vendor.oplus.ha" scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:09.948   959   959 I vendor.oplus.ha: type=1400 audit(0.0:23): avc:  denied  { bind } for  scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:09.948   959   959 I auditd  : type=1400 audit(0.0:24): avc:  denied  { write } for  comm="vendor.oplus.ha" scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:09.948   959   959 I vendor.oplus.ha: type=1400 audit(0.0:24): avc:  denied  { write } for  scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:09.948   959   959 I auditd  : type=1400 audit(0.0:25): avc:  denied  { read } for  comm="vendor.oplus.ha" scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:09.948   959   959 I vendor.oplus.ha: type=1400 audit(0.0:25): avc:  denied  { read } for  scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:09.948   959   959 I auditd  : type=1400 audit(0.0:26): avc:  denied  { call } for  comm="vendor.oplus.ha" scontext=u:r:init:s0 tcontext=u:r:hwservicemanager:s0 tclass=binder permissive=1
05-24 20:21:09.948   959   959 I vendor.oplus.ha: type=1400 audit(0.0:26): avc:  denied  { call } for  scontext=u:r:init:s0 tcontext=u:r:hwservicemanager:s0 tclass=binder permissive=1
05-24 20:21:09.956   950   950 I HidlServiceManagement: Registered vendor.oplus.hardware.cammidasservice@1.0::IMIDASService/default
05-24 20:21:09.957   950   950 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.cammidasservice@1.0-service to cammidasservice@1.0-service.
05-24 20:21:09.958   950   950 I vendor.oplus.hardware.cammidasservice@1.0-service: midasservice register successfully
05-24 20:21:09.959   914   914 W hwcomposer: [DRMDEV] 0xcccccccc[64] property[MSYNC2_0_ENABLE] does not do initialize  
05-24 20:21:09.959   914   914 W hwcomposer: [DRMDEV] 0xcccccccc[64] property[SKIP_CONFIG] does not do initialize  
05-24 20:21:09.959   914   914 W hwcomposer: [DRMDEV] failed to initialize crtc[0]: 64  
05-24 20:21:09.960   914   914 W hwcomposer: [DRMDEV] 0xcccccccc[88] property[MSYNC2_0_ENABLE] does not do initialize  
05-24 20:21:09.960   914   914 W hwcomposer: [DRMDEV] 0xcccccccc[88] property[SKIP_CONFIG] does not do initialize  
05-24 20:21:09.960   914   914 W hwcomposer: [DRMDEV] failed to initialize crtc[1]: 88  
05-24 20:21:09.960   914   914 W hwcomposer: [DRMDEV] 0xcccccccc[99] property[MSYNC2_0_ENABLE] does not do initialize  
05-24 20:21:09.960   914   914 W hwcomposer: [DRMDEV] 0xcccccccc[99] property[SKIP_CONFIG] does not do initialize  
05-24 20:21:09.960   914   914 W hwcomposer: [DRMDEV] failed to initialize crtc[2]: 99  
05-24 20:21:09.960   914   914 W hwcomposer: [DRMDEV] failed to initialize all crtc: -19  
05-24 20:21:09.961   914   914 E hwcomposer: [DRMDEV] failed to initialize drm resource  
05-24 20:21:09.961   575   575 E SELinux : avc:  denied  { find } for interface=vendor.oplus.hardware.olc::IOplusLogCore sid=u:r:init:s0 pid=959 scontext=u:r:init:s0 tcontext=u:object_r:default_android_hwservice:s0 tclass=hwservice_manager permissive=1
05-24 20:21:09.961   575   575 I auditd  : avc:  denied  { find } for interface=vendor.oplus.hardware.olc::IOplusLogCore sid=u:r:init:s0 pid=959 scontext=u:r:init:s0 tcontext=u:object_r:default_android_hwservice:s0 tclass=hwservice_manager permissive=1
05-24 20:21:09.961   575   575 I hwservicemanager: getTransport: Cannot find entry vendor.oplus.hardware.olc@2.0::IOplusLogCore/default in either framework or device VINTF manifest.
05-24 20:21:09.961   954   954 E vendor.oplus.hardware.charger@1.0-service: setChgStatusToBcc: 0
05-24 20:21:09.961   959   959 E HidlServiceManagement: Service vendor.oplus.hardware.olc@2.0::IOplusLogCore/default must be in VINTF manifest in order to register/get.
05-24 20:21:09.962   959   959 E OLC_HAL : registerHidlService failed. 
05-24 20:21:09.964   927   927 I HidlServiceManagement: Registered vendor.mediatek.hardware.videotelephony@1.0::IVideoTelephony/default
05-24 20:21:09.965   914   914 I hwcomposer: [PqXmlParser] [PQ_SERVICE] mDeviceVersion = AMS644VA04_MTK04_20615  
05-24 20:21:09.965   914   914 I hwcomposer: [PqXmlParser] [PQ_SERVICE] mDeviceManufacture = samsung1024  
05-24 20:21:09.965   914   914 I hwcomposer: [PqXmlParser] [PQ_SERVICE] prjName:20662  
05-24 20:21:09.965   914   914 I hwcomposer: [PqXmlParser] init: failed to open file: /vendor/etc/cust_pq.xml  
05-24 20:21:09.965   926   926 I HidlServiceManagement: Registered android.hardware.usb.gadget@1.1::IUsbGadget/default
05-24 20:21:09.965   926   926 I android.hardware.usb@1.3-service-mediatekv2: USB HAL Ready.
05-24 20:21:09.966   968   968 W MTK_FG_FUEL: fd < 0, init first!
05-24 20:21:09.966   968   968 W MTK_FG_FUEL: init failed, return!
05-24 20:21:09.966   968   968 W MTK_FG  : fd < 0, init first!
05-24 20:21:09.966   968   968 E MTK_FG  : init failed, return!
05-24 20:21:09.966   968   968 W MTK_FG  : fd < 0, init first!
05-24 20:21:09.966   968   968 E MTK_FG  : init failed, return!
05-24 20:21:09.966   968   968 W MTK_FG  : fd < 0, init first!
05-24 20:21:09.966   968   968 E MTK_FG  : init failed, return!
05-24 20:21:09.966   913   913 I HidlServiceManagement: Registered android.hardware.graphics.allocator@4.0::IAllocator/default
05-24 20:21:09.967   913   913 I HidlServiceManagement: Removing namespace from process name android.hardware.graphics.allocator@4.0-service-mediatek to allocator@4.0-service-mediatek.
05-24 20:21:09.971   945   945 I HidlServiceManagement: Registered vendor.trustonic.tee@1.1::ITee/default
05-24 20:21:09.972   945   945 I HidlServiceManagement: Removing namespace from process name vendor.trustonic.tee@1.1-service to tee@1.1-service.
05-24 20:21:09.973   933   933 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkpower@1.2::IMtkPerf/default
05-24 20:21:09.973   933   933 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 20:21:09.975   940   940 I HidlServiceManagement: Registered vendor.mediatek.hardware.nvram@1.1::INvram/default
05-24 20:21:09.975   940   940 I HidlServiceManagement: Removing namespace from process name vendor.mediatek.hardware.nvram@1.1-service to nvram@1.1-service.
05-24 20:21:09.977   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_sensor
05-24 20:21:09.978   575   575 I hwservicemanager: Since vendor.mediatek.hardware.pq@2.14::IPictureQuality/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 20:21:09.979   914   914 E hwcomposer: [IPqDevice] Can't get PQ service tried (0) times  
05-24 20:21:09.981   945   945 I HidlServiceManagement: Registered vendor.trustonic.tee.tui@1.0::ITui/default
05-24 20:21:09.982   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_network
05-24 20:21:09.983   903   903 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-24 20:21:09.983   903   903 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-24 20:21:09.984   954   954 I HidlServiceManagement: Registered vendor.oplus.hardware.charger@1.0::ICharger/default
05-24 20:21:09.984   954   954 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.charger@1.0-service to charger@1.0-service.
05-24 20:21:09.986   954   954 E vendor.oplus.hardware.charger@1.0-service: ERR:Failed to get bms heating config file
05-24 20:21:09.986   954   954 E vendor.oplus.hardware.charger@1.0-service: can't parse config, rc=-1
05-24 20:21:09.990   987   987 E ccci_mdinit: (1):main, fail to open ccci_dump, err(Permission denied)
05-24 20:21:09.991   914   914 I HidlServiceManagement: Registered vendor.mediatek.hardware.composer_ext@1.0::IComposerExt/default
05-24 20:21:09.991   914   914 I hwcomposer: [HWC] IComposerExt service registration completed.  
05-24 20:21:09.991   987   987 I ccci_mdinit: (1):[main] drv_ver: 2
05-24 20:21:09.991   987   987 I ccci_mdinit: (1):[main] ccci_create_md_status_listen_thread
05-24 20:21:09.992   987   987 I ccci_mdinit: (1):md_init ver:2.30, sub:0, 1
05-24 20:21:09.992   987   987 I NVRAM   : MD1 set status: vendor.mtk.md1.status=init 
05-24 20:21:09.993   987   987 I ccci_mdinit: (1):MD0 set status: mtk.md0.status=init 
05-24 20:21:09.993   987   987 I NVRAM   : MD0 set status: mtk.md0.status=init 
05-24 20:21:09.993   987   987 E ccci_mdinit: (1):get property fail: ro.vendor.mtk_mipc_support
05-24 20:21:09.993   987   987 I ccci_mdinit: (1):service names: [init.svc.vendor.gsm0710muxd][init.svc.vendor.ril-daemon-mtk][init.svc.emdlogger1][init.svc.vendor.ccci_fsd]
05-24 20:21:09.993   987   987 I ccci_mdinit: (1):md_img_exist 0 0 0 0
05-24 20:21:09.993   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_ipaddr
05-24 20:21:09.993   987   987 I ccci_mdinit: (1):MD1 set status: mtk.md1.status=reset 
05-24 20:21:09.993   987   987 E ccci_mdinit: (1):[get_mdini_killed_state] error: get mdinit killed: 25(-1)
05-24 20:21:09.994   987   987 I ccci_mdinit: (1):MD_LOG_MEMDUMP_WAIT:ro.vendor.md_log_memdump_wait not exist, using default value
05-24 20:21:09.994   987   987 I ccci_mdinit: (1):MD_LOG_MEMDUMP_WAIT value: 0
05-24 20:21:09.994   987   987 I ccci_mdinit: (1):md0: mdl_mode=0
05-24 20:21:09.994   987   987 I ccci_mdinit: (1):check_nvram_ready(), property_get("vendor.service.nvram_init") = , read_nvram_ready_retry = 1
05-24 20:21:09.995   914   914 I HidlServiceManagement: Registered android.hardware.graphics.composer@2.3::IComposer/default
05-24 20:21:09.995   914   914 I HidlServiceManagement: Removing namespace from process name android.hardware.graphics.composer@2.3-service to composer@2.3-service.
05-24 20:21:09.999   922   922 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/acc_cali.json) open failed: -2 (No such file or directory)
05-24 20:21:10.000   964   964 I HidlServiceManagement: Registered vendor.oplus.hardware.performance@1.0::IPerformance/default
05-24 20:21:10.000   964   964 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.performance@1.0-service to performance@1.0-service.
05-24 20:21:10.001   575   999 I hwservicemanager: Tried to start vendor.mediatek.hardware.pq@2.14::IPictureQuality/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 20:21:10.002   922   922 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/gyro_cali.json) open failed: -2 (No such file or directory)
05-24 20:21:10.002   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_lbs
05-24 20:21:10.004   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_framework2agps
05-24 20:21:10.004   975   975 I credstore: Registered binder service
05-24 20:21:10.005   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_agps2framework
05-24 20:21:10.006   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mnld2nlputils
05-24 20:21:10.007   922   922 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/als_cali.json) open failed: -2 (No such file or directory)
05-24 20:21:10.007   922   922 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/ps_cali.json) open failed: -2 (No such file or directory)
05-24 20:21:10.007   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mnld2debugService
05-24 20:21:10.007   922   922 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/sar_cali.json) open failed: -2 (No such file or directory)
05-24 20:21:10.007   922   922 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/ois_cali.json) open failed: -2 (No such file or directory)
05-24 20:21:10.008   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_debugService2mnld
05-24 20:21:10.009   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_meta2mnld
05-24 20:21:10.010   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_agpsd2debugService
05-24 20:21:10.011   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_debugService2agpsd
05-24 20:21:10.013   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/AgpsInterface
05-24 20:21:10.014   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/AgpsDebugInterface
05-24 20:21:10.016  1022  1022 I TeeMcDaemon: Initialise Secure World [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/MobiCoreDriverDaemon.cpp:260]
05-24 20:21:10.016  1022  1022 W TeeMcDaemon: Cannot open key SO  (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:445]
05-24 20:21:10.016   991   991 E ccci_mdinit: (3):main, fail to open ccci_dump, err(Permission denied)
05-24 20:21:10.016  1022  1022 I TeeMcDaemon: Start services [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/MobiCoreDriverDaemon.cpp:325]
05-24 20:21:10.017   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mnld2mtklogger
05-24 20:21:10.020   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mtklogger2mnld
05-24 20:21:10.022   922   922 I HidlServiceManagement: Registered android.hardware.sensors@2.0::ISensors/default
05-24 20:21:10.022   922   922 I HidlServiceManagement: Removing namespace from <NAME_EMAIL>6893 to sensors@2.0-service-multihal.mt6893.
05-24 20:21:10.025   928   928 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lbs_log_v2s
05-24 20:21:10.026   961   961 I HidlServiceManagement: Registered vendor.oplus.hardware.oplusSensor@1.0::ISensorFeature/default
05-24 20:21:10.026   961   961 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.oplusSensor@1.0-service to oplusSensor@1.0-service.
05-24 20:21:10.030   665   668 W powerhal-libperfmgr: Connecting to PPS daemon failed (No such file or directory)
05-24 20:21:10.039   903   903 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 20:21:10.041   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.configstore@1.0::ISurfaceFlingerConfigs/default in either framework or device VINTF manifest.
05-24 20:21:10.042  1036  1036 I gsid    : no DSU: No such file or directory
05-24 20:21:10.044  1022  1031 W TeeMcDaemon: Cannot open trustlet /data/vendor/mcRegistry/07050501000000000000000000000020.tlbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.050  1022  1031 W TeeMcDaemon: Cannot open trustlet /data/vendor/mcRegistry/08050500000000000000000000000000.tlbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.052  1022  1031 W TeeMcDaemon: Cannot open trustlet /data/vendor/mcRegistry/08050500000000000000000000000000.tlbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.054   911   911 I HidlServiceManagement: Registered android.hardware.gatekeeper@1.0::IGatekeeper/default
05-24 20:21:10.054   911   911 I HidlServiceManagement: Removing namespace from process name android.hardware.gatekeeper@1.0-service to gatekeeper@1.0-service.
05-24 20:21:10.055   985   985 I SurfaceFlinger: Using HWComposer service: default
05-24 20:21:10.059   985   985 I SurfaceFlinger: SurfaceFlinger's main thread ready to run. Initializing graphics H/W...
05-24 20:21:10.068   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: failed to get path of fd 3: No such file or directory
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(15): previous definition here
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(38): previous definition here
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(69): previous definition here
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(70): previous definition here
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(26): previous definition here
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(37): previous definition here
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(45): previous definition here
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(46): previous definition here
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(53): previous definition here
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(56): previous definition here
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(59): previous definition here
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(60): previous definition here
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(61): previous definition here
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(74): previous definition here
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(15): previous definition here
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(88): previous definition here
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(89): previous definition here
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(90): previous definition here
05-24 20:21:10.069   916   916 W android.hardware.media.c2@1.2-mediatek: libminijail[916]: compile_file: <fd>(91): previous definition here
05-24 20:21:10.076   916   916 I HidlServiceManagement: Registered android.hardware.media.c2@1.1::IComponentStore/default
05-24 20:21:10.076   916   916 I HidlServiceManagement: Removing namespace from process name android.hardware.media.c2@1.2-mediatek to c2@1.2-mediatek.
05-24 20:21:10.128   973   973 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 20:21:10.129   973   973 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 20:21:10.129   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 20:21:10.149   973   973 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 20:21:10.149   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 20:21:10.150   973   973 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 20:21:10.154   973   973 W BatteryNotifier: batterystats service unavailable!
05-24 20:21:10.154   973   973 I ServiceManagerCppClient: Waiting for service 'media.metrics' on '/dev/binder'...
05-24 20:21:10.177  1069  1069 I bootstat: Service started: /system/bin/bootstat --set_system_boot_reason 
05-24 20:21:10.181  1022  1022 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/05160000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.181  1022  1022 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/020b0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.186  1022  1022 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/030b0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.186  1022  1022 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/03100000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.192  1022  1022 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/031c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.192  1022  1022 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/033c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.192  1022  1022 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/035c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.192  1022  1022 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/037c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.192  1022  1022 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/031c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.192  1022  1022 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/032c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.193  1022  1022 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/033c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.193  1022  1022 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/034c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.193  1022  1022 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/035c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.193  1022  1022 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/036c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.193  1022  1022 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/037c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.196  1022  1022 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/070c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.196  1022  1022 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/090b0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.196  1022  1022 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/0f5eed3c3b5a47afacca69a84bf0efad.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.196  1022  1022 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/07060000000000000000000000007169.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.196  1022  1022 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/4be4f7dc1f2c11e5b5f7727283247c7f.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.196  1022  1022 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/08070000000000000000000000008270.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.196  1022  1022 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/07070000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.196  1022  1022 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/07407000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.198  1022  1022 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/6b3f5fa0f8cf55a7be2582587d62d63a.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 20:21:10.199  1074  1074 W perfetto:          service.cc:232 Started traced, listening on /dev/socket/traced_producer /dev/socket/traced_consumer
05-24 20:21:10.201  1073  1073 I perfetto:           probes.cc:104 Starting /system/bin/traced_probes service
05-24 20:21:10.202  1073  1073 I perfetto:  probes_producer.cc:332 Connected to the service
05-24 20:21:10.211  1079  1079 W MTK_FG_NVRAM: fd < 0, init first!
05-24 20:21:10.211  1079  1079 W MTK_FG_NVRAM: init failed, return!
05-24 20:21:10.211   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.atrace@1.0::IAtraceDevice/default in either framework or device VINTF manifest.
05-24 20:21:10.216  1079  1079 W MTK_FG_NVRAM: fd < 0, init first!
05-24 20:21:10.217  1079  1079 W MTK_FG_NVRAM: init failed, return!
05-24 20:21:10.217  1079  1079 W MTK_FG_NVRAM: fd < 0, init first!
05-24 20:21:10.217  1079  1079 W MTK_FG_NVRAM: init failed, return!
05-24 20:21:10.217  1079  1079 W MTK_FG_NVRAM: fd < 0, init first!
05-24 20:21:10.217  1079  1079 W MTK_FG_NVRAM: init failed, return!
05-24 20:21:10.217  1079  1079 W MTK_FG_NVRAM: fd < 0, init first!
05-24 20:21:10.217  1079  1079 W MTK_FG_NVRAM: init failed, return!
05-24 20:21:10.217  1079  1079 W MTK_FG_NVRAM: fd < 0, init first!
05-24 20:21:10.217  1079  1079 W MTK_FG_NVRAM: init failed, return!
05-24 20:21:10.217  1079  1079 W MTK_FG_NVRAM: fd < 0, init first!
05-24 20:21:10.217  1079  1079 W MTK_FG_NVRAM: init failed, return!
05-24 20:21:10.217  1079  1079 W MTK_FG_NVRAM: fd < 0, init first!
05-24 20:21:10.217  1079  1079 W MTK_FG_NVRAM: init failed, return!
05-24 20:21:10.217  1079  1079 W MTK_FG_NVRAM: fd < 0, init first!
05-24 20:21:10.217  1079  1079 W MTK_FG_NVRAM: init failed, return!
05-24 20:21:10.217  1079  1079 W MTK_FG_NVRAM: fd < 0, init first!
05-24 20:21:10.217  1079  1079 W MTK_FG_NVRAM: init failed, return!
05-24 20:21:10.253  1084  1084 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 20:21:10.253  1084  1084 E mnld_pwr_interface: mnld_pwr_init: mnld_pwr_open failed, No such file or directory
05-24 20:21:10.253  1084  1084 E MNL2AGPS: bind_udp_socket: bind failed path=[/data/agps_supl/agps_to_mnl] reason=[No such file or directory]
05-24 20:21:10.254  1084  1084 E mtk_lbs_utility: init_timer_id_alarm: timerfd_create  CLOCK_BOOTTIME_ALARM 
05-24 20:21:10.254  1084  1084 E MNLD    : mnld_init: mnl2hal_release_wakelock failed because of safe_sendto fail ,strerror:Connection refused 
05-24 20:21:10.254  1084  1084 E MNLD    : mnld_init: mnl2hal_mnld_reboot failed because of safe_sendto fail ,strerror:Connection refused 
05-24 20:21:10.254  1084  1084 E mnld    : mtk_socket_connect_local: mtk_socket_connect_local() connect() failed reason=[Connection refused]111 for path=[mtk_mnld2debugService]
05-24 20:21:10.256  1084  1110 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 20:21:10.257  1084  1110 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:0
05-24 20:21:10.259  1094  1094 I HidlServiceManagement: Registered android.system.wifi.keystore@1.0::IKeystore/default
05-24 20:21:10.260  1100  1100 I thermal_repeater: RilRPC_init 
05-24 20:21:10.260  1100  1100 I thermal_repeater: RilRPC_init dlopen fail: dlopen failed: library "librpcril.so" not found 
05-24 20:21:10.281  1121  1121 I thermal_src1: ta_daemon_init
05-24 20:21:10.282  1095  1095 I android.hardware.media.omx@1.0-service: mediacodecservice starting
05-24 20:21:10.286  1121  1121 I thermal_src: ta_catm_init_flow
05-24 20:21:10.286  1121  1121 I thermal_src: u_CATM_ON == -1, get catm init val
05-24 20:21:10.286  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: failed to get path of fd 5: No such file or directory
05-24 20:21:10.286  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: allowing syscall: clock_gettime
05-24 20:21:10.286  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: allowing syscall: connect
05-24 20:21:10.286  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: allowing syscall: fcntl64
05-24 20:21:10.286  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: allowing syscall: socket
05-24 20:21:10.286  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: allowing syscall: writev
05-24 20:21:10.286  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(59): syscall getrandom redefined here
05-24 20:21:10.286  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(15): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(1): syscall read redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(9): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(2): syscall write redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(5): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(3): syscall exit redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(40): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(4): syscall rt_sigreturn redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(45): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(6): syscall exit_group redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(44): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(7): syscall clock_gettime redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(7): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(8): syscall gettimeofday redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(47): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(9): syscall futex redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(3): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(10): syscall getrandom redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(15): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(13): syscall ppoll redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(13): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(14): syscall pipe2 redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(46): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(15): syscall openat redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(30): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(16): syscall dup redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(12): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(17): syscall close redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(10): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(18): syscall lseek redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(50): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(19): syscall getdents64 redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(58): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(20): syscall faccessat redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(38): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(27): syscall rt_sigprocmask redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(41): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(30): syscall prctl redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(6): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(31): syscall madvise redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(29): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(32): syscall mprotect redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(28): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(33): syscall munmap redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(27): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(34): syscall getuid32 redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(34): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(35): syscall fstat64 redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(35): previous definition here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(36): syscall mmap2 redefined here
05-24 20:21:10.287  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(14): previous definition here
05-24 20:21:10.289  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(66): syscall getpid redefined here
05-24 20:21:10.289  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(11): previous definition here
05-24 20:21:10.289  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(67): syscall gettid redefined here
05-24 20:21:10.289  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(12): previous definition here
05-24 20:21:10.289  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(74): syscall recvfrom redefined here
05-24 20:21:10.289  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(22): previous definition here
05-24 20:21:10.289  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(77): syscall sched_getaffinity redefined here
05-24 20:21:10.289  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(75): previous definition here
05-24 20:21:10.289  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(82): syscall sysinfo redefined here
05-24 20:21:10.289  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(24): previous definition here
05-24 20:21:10.289  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: <fd>(83): syscall setsockopt redefined here
05-24 20:21:10.289  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(23): previous definition here
05-24 20:21:10.289  1095  1095 W android.hardware.media.omx@1.0-service: libminijail[1095]: logging seccomp filter failures
05-24 20:21:10.305  1095  1095 I HidlServiceManagement: Registered android.hardware.media.omx@1.0::IOmx/default
05-24 20:21:10.305  1095  1095 I HidlServiceManagement: Removing namespace from process name android.hardware.media.omx@1.0-service to omx@1.0-service.
05-24 20:21:10.306  1095  1095 I android.hardware.media.omx@1.0-service: IOmx HAL service created.
05-24 20:21:10.310  1095  1095 E MediaCodecsXmlParser: Cannot find the role for a decoder of type audio/x-adpcm-dvi-ima
05-24 20:21:10.310  1095  1095 E MediaCodecsXmlParser: Cannot find the role for a decoder of type audio/x-adpcm-ms
05-24 20:21:10.310  1095  1095 E MediaCodecsXmlParser: Cannot find the role for a decoder of type audio/alac
05-24 20:21:10.310  1095  1095 E MediaCodecsXmlParser: Cannot find the role for a decoder of type audio/ape
05-24 20:21:10.310  1095  1095 I OmxStore: node [OMX.MTK.AUDIO.DECODER.GSM] not found in IOmx
05-24 20:21:10.310  1095  1095 I OmxStore: node [OMX.MTK.AUDIO.DECODER.MP3] not found in IOmx
05-24 20:21:10.312  1095  1095 I HidlServiceManagement: Registered android.hardware.media.omx@1.0::IOmxStore/default
05-24 20:21:10.312  1095  1095 I HidlServiceManagement: Removing namespace from process name android.hardware.media.omx@1.0-service to omx@1.0-service.
05-24 20:21:10.326  1089  1089 I main_extractorservice: enable media.extractor memory limits
05-24 20:21:10.328  1125  1149 I MtkAgpsNative: Enter mtk_agps_up_init
05-24 20:21:10.330  1089  1089 W mediaextractor: libminijail[1089]: failed to get path of fd 5: No such file or directory
05-24 20:21:10.330  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(38): previous definition here
05-24 20:21:10.330  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(18): previous definition here
05-24 20:21:10.330  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(6): previous definition here
05-24 20:21:10.330  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(27): previous definition here
05-24 20:21:10.330  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(29): previous definition here
05-24 20:21:10.330  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(28): previous definition here
05-24 20:21:10.330  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(4): previous definition here
05-24 20:21:10.330  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(32): previous definition here
05-24 20:21:10.330  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(12): previous definition here
05-24 20:21:10.330  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(9): previous definition here
05-24 20:21:10.330  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(8): previous definition here
05-24 20:21:10.330  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(23): previous definition here
05-24 20:21:10.330  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(41): previous definition here
05-24 20:21:10.330  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(25): previous definition here
05-24 20:21:10.330  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(56): previous definition here
05-24 20:21:10.330  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(5): previous definition here
05-24 20:21:10.330  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(14): previous definition here
05-24 20:21:10.331  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(13): previous definition here
05-24 20:21:10.331  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(11): previous definition here
05-24 20:21:10.331  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(15): previous definition here
05-24 20:21:10.331  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(16): previous definition here
05-24 20:21:10.331  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(10): previous definition here
05-24 20:21:10.332  1136  1136 I auditd  : type=1400 audit(0.0:33): avc:  denied  { execute_no_trans } for  comm="init" path="/vendor/bin/sh" dev="dm-1" ino=276 scontext=u:r:init:s0 tcontext=u:object_r:vendor_shell_exec:s0 tclass=file permissive=1
05-24 20:21:10.332  1136  1136 I init    : type=1400 audit(0.0:33): avc:  denied  { execute_no_trans } for  path="/vendor/bin/sh" dev="dm-1" ino=276 scontext=u:r:init:s0 tcontext=u:object_r:vendor_shell_exec:s0 tclass=file permissive=1
05-24 20:21:10.340  1089  1089 W mediaextractor: libminijail[1089]: compile_file: /apex/com.android.media/etc/seccomp_policy/crash_dump.arm64.policy(7): previous definition here
05-24 20:21:10.340  1089  1089 W mediaextractor: libminijail[1089]: compile_file: <fd>(56): previous definition here
05-24 20:21:10.340  1089  1089 W mediaextractor: libminijail[1089]: compile_file: /apex/com.android.media/etc/seccomp_policy/crash_dump.arm64.policy(6): previous definition here
05-24 20:21:10.341  1093  1093 I storaged: Unable to get AIDL health service, trying HIDL...
05-24 20:21:10.343  1131  1131 I VPUD    : vdec_codec_service_init() block mode
05-24 20:21:10.343  1131  1131 I VPUD    : venc_codec_service_init()
05-24 20:21:10.343  1131  1131 I VPUD    : -- send_init_fin
05-24 20:21:10.345  1125  1149 E agps    : [agps] ERR: [MNL] bind failed path=[/data/agps_supl/mnl_to_agps] reason=[No such file or directory]
05-24 20:21:10.346  1125  1149 E agps    : [agps] ERR: [Default] mtk_expat_xml_load2() failed [fopen() No such file or directory] file [/data/vendor/agps_supl/agps_profiles_conf2.xml]
05-24 20:21:10.346  1125  1149 E agps    : [agps] ERR: [Default] mtk_expat_xml_load2() failed [fopen() No such file or directory] file [/data/agps_supl/agps_profiles_conf2.xml]
05-24 20:21:10.346  1098  1098 I ULog    : ULog initialized: mode=0x1  filters: req=0x0 func=0x0/0x0 details=0xfffff000 level=3
05-24 20:21:10.347  1131  1185 I VPUD    : vdec_service_entry()
05-24 20:21:10.347  1131  1186 I VPUD    : venc service entry TID = 1186
05-24 20:21:10.352  1125  1149 E mtk_socket: ERR: mtk_socket_connect_local() connect() failed reason=[Connection refused]111 for path=[mtk_agpsd2debugService]
05-24 20:21:10.353  1125  1149 E agps    : [agps] ERR: [CP] get_ccci_uart  open failed node=[/dev/ccci2_tty2] reason=[No such file or directory]
05-24 20:21:10.353  1125  1149 E agps    :  ERR: [AGPS2WIFI] bind failed path=[/data/agps_supl/wifi_2_agps] reason=[No such file or directory]
05-24 20:21:10.353  1125  1149 E agps    : [agps] ERR: [WIFI] wifi_mgr_init  create_wifi2agps_fd failed
05-24 20:21:10.355   973   973 I ServiceManagerCppClient: Waiting for service 'media.metrics' on '/dev/binder' successful after waiting 200ms
05-24 20:21:10.356   973   973 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-24 20:21:10.357  1091  1127 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder'...
05-24 20:21:10.369   973   973 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-24 20:21:10.369   973   973 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-24 20:21:10.386  1215  1215 I KernelSU Next: ksud::cli: command: Services
05-24 20:21:10.386  1215  1215 I KernelSU Next: ksud::init_event: on_services triggered!
05-24 20:21:10.386  1215  1215 I KernelSU Next: ksud::module: /data/adb/service.d not exists, skip
05-24 20:21:10.386  1215  1215 I KernelSU Next: ksud::module: exec /data/adb/modules/bindhosts/service.sh
05-24 20:21:10.388  1215  1215 I KernelSU Next: ksud::module: exec /data/adb/modules/zygisk_lsposed/service.sh
05-24 20:21:10.392  1215  1215 I KernelSU Next: ksud::module: exec /data/adb/modules/zygisksu/service.sh
05-24 20:21:10.394  1215  1215 I KernelSU Next: ksud::module: exec /data/adb/modules/yt_rvx/service.sh
05-24 20:21:10.397  1085  1085 I cameraserver: ServiceManager: 0xb400006d9d9d30b0
05-24 20:21:10.397  1085  1085 I CameraService: CameraService started (pid=1085)
05-24 20:21:10.397  1085  1085 I CameraService: CameraService process starting
05-24 20:21:10.398  1085  1085 W BatteryNotifier: batterystats service unavailable!
05-24 20:21:10.398  1085  1085 W BatteryNotifier: batterystats service unavailable!
05-24 20:21:10.403   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.403   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.403   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.404   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.404   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.404   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.404   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.404   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.404   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.404   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.404   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.404   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.404   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.404   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.405   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.405   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.405   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.405   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.410  1214  1214 E android.hardware.biometrics.fingerprint@2.1-service: fingerprint hwbinder service starting
05-24 20:21:10.410  1214  1214 I android.hardware.biometrics.fingerprint@2.1-service: read_proc_string_data sys_buffer_data=G_OPTICAL_G3S #end
05-24 20:21:10.411   926   926 I android.hardware.usb@1.3-service-mediatekv2: setCurrentUsbFunctions: skip first time for usbd
05-24 20:21:10.411   926   926 I android.hardware.usb@1.3-service-mediatekv2: Usb Gadget setcurrent functions failed
05-24 20:21:10.420   575   575 I hwservicemanager: Since vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 20:21:10.420  1214  1214 E android.hardware.biometrics.fingerprint@2.1-service: fp read fp_id_string = G_OPTICAL_G3S
05-24 20:21:10.420  1214  1214 E android.hardware.biometrics.fingerprint@2.1-service: optical goodix fingerprint
05-24 20:21:10.421  1214  1214 I android.hardware.biometrics.fingerprint@2.1-service: fpSettingsInit project name:0
05-24 20:21:10.421  1214  1214 I android.hardware.biometrics.fingerprint@2.1-service: read_proc_string_data sys_buffer_data=Device version:		AMS644VA04_MTK04_20615
05-24 20:21:10.421  1214  1214 I android.hardware.biometrics.fingerprint@2.1-service: Device manufacture:		samsung1024
05-24 20:21:10.421  1214  1214 I android.hardware.biometrics.fingerprint@2.1-service:  #end
05-24 20:21:10.421  1214  1214 I android.hardware.biometrics.fingerprint@2.1-service: fpSettingsInit lcd type:1
05-24 20:21:10.422  1214  1214 I android.hardware.biometrics.fingerprint@2.1-service: fpSettingsInit select config index is :0
05-24 20:21:10.422  1093  1093 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder'...
05-24 20:21:10.422   575   575 I hwservicemanager: Since android.hardware.camera.provider@2.4::ICameraProvider/internal/0 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 20:21:10.423  1085  1085 W CameraProviderManager: tryToInitializeHidlProviderLocked: HIDL Camera provider HAL 'internal/0' is not actually available
05-24 20:21:10.424  1085  1085 W CameraProviderManager: tryToInitializeAidlProviderLocked: AIDL Camera provider HAL 'android.hardware.camera.provider.ICameraProvider/virtual/0' is not actually available
05-24 20:21:10.426   985   985 I HidlServiceManagement: Registered android.frameworks.displayservice@1.0::IDisplayService/default
05-24 20:21:10.427  1214  1214 I android.hardware.biometrics.fingerprint@2.1-service: do nothing
05-24 20:21:10.427   575  1251 I hwservicemanager: Tried to start vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 20:21:10.428   575  1253 I hwservicemanager: Tried to start android.hardware.camera.provider@2.4::ICameraProvider/internal/0 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 20:21:10.428   575   575 I hwservicemanager: Since vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 20:21:10.429  1209  1209 I HidlServiceManagement: getService: Trying again for vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default...
05-24 20:21:10.430  1085  1085 I HidlServiceManagement: Registered android.frameworks.cameraservice.service@2.2::ICameraService/default
05-24 20:21:10.432  1085  1085 I CameraService: CameraService pinged cameraservice proxy
05-24 20:21:10.432  1085  1085 I cameraserver: ServiceManager: 0xb400006d9d9d30b0 done instantiate
05-24 20:21:10.447  1214  1214 E [GF_HAL][HalContext]: [init], init with G3 HAL.
05-24 20:21:10.453   575  1258 I hwservicemanager: Tried to start vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 20:21:10.455  1092  1092 I mediaserver: ServiceManager: 0xebac2bf0
05-24 20:21:10.456  1092  1092 W BatteryNotifier: batterystats service unavailable!
05-24 20:21:10.456  1092  1092 W BatteryNotifier: batterystats service unavailable!
05-24 20:21:10.473   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.473   903   903 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.477   903   903 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 20:21:10.491   886   886 I zygote  : Initializing ART runtime metrics
05-24 20:21:10.494   987   987 E ccci_mdinit: (1):fail to open /mnt/vendor/nvdata/APCFG/APRDCL/CXP_SBP: 2
05-24 20:21:10.494   987   987 I ccci_mdinit: (1):get_cip_sbp_setting, file /custom/etc/firmware/CIP_MD_SBP NOT exists!
05-24 20:21:10.494   987   987 I ccci_mdinit: (1):PRJ_SBP_ID:ro.vendor.mtk_md_sbp_custom_value not exist, using default value
05-24 20:21:10.499   987   987 I ccci_mdinit: (1):SBP_SUB_ID:persist.vendor.operator.subid not exist
05-24 20:21:10.499   987   987 I ccci_mdinit: (1):set md boot data:mdl=0 sbp=0 dbg_dump=-1 sbp_sub=0
05-24 20:21:10.499   903   903 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x48c06b1f)
05-24 20:21:10.499   987   987 E ccci_mdinit: [SYSENV]get_env_info():240 , env_buffer[0] : 0xb400007e8cf0e020
05-24 20:21:10.500   987   987 I ccci_mdinit: (1):get md_type (null)
05-24 20:21:10.500   987   987 I ccci_mdinit: (1):MD1 set status: mtk.md1.status=bootup 
05-24 20:21:10.501   987   987 I ccci_mdinit: (1):md_id = 0; mdstatusfd = -1
05-24 20:21:10.505   903   903 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x4a8086bf)
05-24 20:21:10.511  1075  1075 I vtservice: [VT][SRV]ServiceManager: 0xb40000774d71fb30
05-24 20:21:10.511  1075  1075 I vtservice: [VT][SRV]before VTService_instantiate
05-24 20:21:10.528  1130  1130 I HidlServiceManagement: Registered vendor.mediatek.hardware.pq@2.15::IPictureQuality/default
05-24 20:21:10.532  1130  1130 I HidlServiceManagement: Removing namespace from process name vendor.mediatek.hardware.pq@2.2-service to pq@2.2-service.
05-24 20:21:10.541   903   903 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 20:21:10.544   903   903 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x5ad1abc3)
05-24 20:21:10.546   903   903 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x75cd96c7)
05-24 20:21:10.547   903   963 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-24 20:21:10.548   903   963 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-24 20:21:10.555  1212  1212 I NetdagentFirewall: setupIptablesHooks done in oem_iptables_init
05-24 20:21:10.555  1212  1212 I NetdagentController: Initializing iptables: 148.3ms
05-24 20:21:10.555  1212  1212 I Netdagent:  Create CommandService  successfully
05-24 20:21:10.559   903   903 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-24 20:21:10.560   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 20:21:10.561   903   903 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-24 20:21:10.561   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 20:21:10.561   903   903 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-24 20:21:10.564   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 20:21:10.565   903   903 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-24 20:21:10.566  1076  1076 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-neuron
05-24 20:21:10.566  1076  1076 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-24 20:21:10.565   903   903 W audiohalservice: Could not register Bluetooth Audio API
05-24 20:21:10.567   903   963 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 20:21:10.567   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-24 20:21:10.567   903   903 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-24 20:21:10.567   903   903 W audiohalservice: Could not register Bluetooth Audio Offload API
05-24 20:21:10.569  1076  1076 I ANNService: Registered service for mtk-neuron
05-24 20:21:10.572  1076  1076 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-dsp
05-24 20:21:10.572  1076  1076 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-24 20:21:10.574  1212  1309 I HidlServiceManagement: Registered vendor.mediatek.hardware.netdagent@1.0::INetdagent/default
05-24 20:21:10.575  1076  1076 I ANNService: Registered service for mtk-dsp
05-24 20:21:10.578  1076  1076 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-mdla
05-24 20:21:10.578  1076  1076 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-24 20:21:10.579  1076  1076 I ANNService: Registered service for mtk-mdla
05-24 20:21:10.581  1076  1076 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-gpu
05-24 20:21:10.581  1076  1076 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-24 20:21:10.582  1076  1076 I ANNService: Registered service for mtk-gpu
05-24 20:21:10.588  1130  1297 E PQ      : [PQ_SERVICE] aisdr2hdr_pqindex is not found in cust_color.xml
05-24 20:21:10.593   903   903 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-24 20:21:10.594   903   903 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-24 20:21:10.594   903   903 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-24 20:21:10.599  1130  1297 I vendor.mediatek.hardware.pq@2.2-service: transferAIOutput(), register trs callback
05-24 20:21:10.603  1076  1076 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.apusys@2.1::INeuronApusys/default
05-24 20:21:10.613  1076  1076 I apuware_server: Start NeuronXrp 2.0 service 
05-24 20:21:10.615  1076  1076 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.xrp@2.0::INeuronXrp/default
05-24 20:21:10.622  1076  1076 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.hmp@1.0::IApuwareHmp/default
05-24 20:21:10.631  1076  1076 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.utils@2.0::IApuwareUtils/default
05-24 20:21:10.668   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.668   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.669   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.669   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.669   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.670  1178  1178 I mediaswcodec: media swcodec service starting
05-24 20:21:10.670   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.671   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.671   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.671  1178  1178 W mediaswcodec: libminijail[1178]: failed to get path of fd 5: No such file or directory
05-24 20:21:10.672   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.672  1178  1178 W mediaswcodec: libminijail[1178]: compile_file: <fd>(39): previous definition here
05-24 20:21:10.672   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.673   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.673  1178  1178 I CodecServiceRegistrant: Creating software Codec2 service...
05-24 20:21:10.673   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.674   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.674   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.675   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.675   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.675   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.676   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.685  1178  1178 I HidlServiceManagement: Registered android.hardware.media.c2@1.2::IComponentStore/software
05-24 20:21:10.687  1178  1178 I CodecServiceRegistrant: Preferred Codec2 HIDL store is set to "default".
05-24 20:21:10.687  1178  1178 I CodecServiceRegistrant: Software Codec2 service created and registered.
05-24 20:21:10.735   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.735   903   963 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:10.739   903   963 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 20:21:10.762   903   963 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x3b9107a1)
05-24 20:21:10.763   903  1329 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-24 20:21:10.764   903  1329 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0x3b9107a1)
05-24 20:21:10.765   903  1329 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0x3b9107a1)
05-24 20:21:10.810   903   963 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-24 20:21:10.815   903   963 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 20:21:10.837   903   963 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-24 20:21:10.857   903   963 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 20:21:10.857   903   963 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 20:21:10.857   903   963 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 20:21:10.863   903   963 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 20:21:10.863   903   963 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 20:21:10.864   903   963 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-24 20:21:10.877   903   963 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-24 20:21:10.877   903   963 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-24 20:21:10.882   973   973 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 20:21:10.882   973   973 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 20:21:10.883   903   903 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-24 20:21:10.884   903   903 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-24 20:21:10.885   903   903 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 20:21:10.886   987  1011 E ccci_fsd(1): FS_OTP_init:otp_get_size:1048576, status=0, type=0!
05-24 20:21:10.886   973   973 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-24 20:21:10.887   973   973 I AudioFlinger: openOutput() this 0xb400007521c46130, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-24 20:21:10.889   973   973 I AudioFlinger: operator(): Using 3000 ms as standby time
05-24 20:21:10.890   973   973 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 20:21:10.892   973   973 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 20:21:10.893   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 20:21:10.894   973   973 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 20:21:10.923   973  1361 I AudioFlinger: AudioFlinger's thread 0xb4000077152de760 tid=1361 ready to run
05-24 20:21:10.970   903  1331 W audio_messenger_ipi: read_ipi_thread(), read dsp fail!! length_read: 0, errno: 0
05-24 20:21:10.978  1364  1364 E DEBUG   : failed to read process info: failed to open /proc/903: No such file or directory
05-24 20:21:11.010   885   885 I zygote64: Initializing ART runtime metrics
05-24 20:21:11.016   574   574 I auditd  : avc:  denied  { find } for pid=1075 uid=1000 name=vendor.mediatek.hardware.videotelephony.IVideoTelephony/default scontext=u:r:vtservice:s0 tcontext=u:object_r:default_android_service:s0 tclass=service_manager permissive=1
05-24 20:21:11.016  1075  1075 W AVSync  : initFD done, g_fd_name: /dev/ccci_imsdc
05-24 20:21:11.034   987  1011 W ccci_mdinit: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.system.suspend.ISystemSuspend/default
05-24 20:21:11.042  1098  1098 I mtkcam-devicemgr: [initialize] +
05-24 20:21:11.046  1241  1241 W ziparchive: Unable to open '/data/adb/modules/zygisk_lsposed/daemon.dm': No such file or directory
05-24 20:21:11.081  1364  1364 F DEBUG   : Cmdline: /vendor/bin/hw/android.hardware.audio.service
05-24 20:21:11.081  1364  1364 F DEBUG   : pid: 903, tid: 962, name: HwBinder:903_1  >>> /vendor/bin/hw/android.hardware.audio.service <<<
05-24 20:21:11.081  1364  1364 F DEBUG   :       #01 pc 0002651d  /vendor/lib/hw/<EMAIL> (android::hardware::audio::V7_1::implementation::StreamOut::setLatencyModeCallback(android::sp<android::hardware::audio::V7_1::IStreamOutLatencyModeCallback> const&)+36) (BuildId: 544a581a8a5464266e14c74f5aea9a1c)
05-24 20:21:11.081  1364  1364 F DEBUG   :       #02 pc 00043c0f  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::_hidl_setLatencyModeCallback(android::hidl::base::V1_0::BnHwBase*, android::hardware::Parcel const&, android::hardware::Parcel*, std::__1::function<void (android::hardware::Parcel&)>)+198) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 20:21:11.081  1364  1364 F DEBUG   :       #03 pc 00044703  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::onTransact(unsigned int, android::hardware::Parcel const&, android::hardware::Parcel*, unsigned int, std::__1::function<void (android::hardware::Parcel&)>)+1926) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 20:21:11.220  1241  1241 I LSPosedService: starting server...
05-24 20:21:11.220  1241  1241 I LSPosedService: version 1.10.1 (7178)
05-24 20:21:11.252  1084  1101 E mnld    : thread_adc_capture_init: set IOCTL_EMI_MEMORY_INIT failed,(Success)
05-24 20:21:11.257  1084  1110 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 20:21:11.257  1084  1110 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:1
05-24 20:21:11.260   885   885 E ActivityRecognitionHardware: activity_recognition HAL is deprecated. class_init is effectively a no-op
05-24 20:21:11.260  1408  1408 I auditd  : type=1400 audit(0.0:61): avc:  denied  { open } for  comm="getprop" path="/dev/__properties__/u:object_r:audio_config_prop:s0" dev="tmpfs" ino=8281 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:audio_config_prop:s0 tclass=file permissive=1
05-24 20:21:11.260  1408  1408 I getprop : type=1400 audit(0.0:61): avc:  denied  { open } for  path="/dev/__properties__/u:object_r:audio_config_prop:s0" dev="tmpfs" ino=8281 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:audio_config_prop:s0 tclass=file permissive=1
05-24 20:21:11.260  1408  1408 I auditd  : type=1400 audit(0.0:62): avc:  denied  { getattr } for  comm="getprop" path="/dev/__properties__/u:object_r:audio_config_prop:s0" dev="tmpfs" ino=8281 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:audio_config_prop:s0 tclass=file permissive=1
05-24 20:21:11.260  1408  1408 I getprop : type=1400 audit(0.0:62): avc:  denied  { getattr } for  path="/dev/__properties__/u:object_r:audio_config_prop:s0" dev="tmpfs" ino=8281 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:audio_config_prop:s0 tclass=file permissive=1
05-24 20:21:11.260  1408  1408 I auditd  : type=1400 audit(0.0:63): avc:  denied  { map } for  comm="getprop" path="/dev/__properties__/u:object_r:audio_config_prop:s0" dev="tmpfs" ino=8281 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:audio_config_prop:s0 tclass=file permissive=1
05-24 20:21:11.260  1408  1408 I getprop : type=1400 audit(0.0:63): avc:  denied  { map } for  path="/dev/__properties__/u:object_r:audio_config_prop:s0" dev="tmpfs" ino=8281 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:audio_config_prop:s0 tclass=file permissive=1
05-24 20:21:11.260  1408  1408 I auditd  : type=1400 audit(0.0:67): avc:  denied  { open } for  comm="getprop" path="/dev/__properties__/u:object_r:avf_virtualizationservice_prop:s0" dev="tmpfs" ino=8284 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:avf_virtualizationservice_prop:s0 tclass=file permissive=1
05-24 20:21:11.260  1408  1408 I getprop : type=1400 audit(0.0:67): avc:  denied  { open } for  path="/dev/__properties__/u:object_r:avf_virtualizationservice_prop:s0" dev="tmpfs" ino=8284 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:avf_virtualizationservice_prop:s0 tclass=file permissive=1
05-24 20:21:11.260  1408  1408 I auditd  : type=1400 audit(0.0:68): avc:  denied  { getattr } for  comm="getprop" path="/dev/__properties__/u:object_r:avf_virtualizationservice_prop:s0" dev="tmpfs" ino=8284 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:avf_virtualizationservice_prop:s0 tclass=file permissive=1
05-24 20:21:11.260  1408  1408 I getprop : type=1400 audit(0.0:68): avc:  denied  { getattr } for  path="/dev/__properties__/u:object_r:avf_virtualizationservice_prop:s0" dev="tmpfs" ino=8284 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:avf_virtualizationservice_prop:s0 tclass=file permissive=1
05-24 20:21:11.260  1408  1408 I auditd  : type=1400 audit(0.0:69): avc:  denied  { map } for  comm="getprop" path="/dev/__properties__/u:object_r:avf_virtualizationservice_prop:s0" dev="tmpfs" ino=8284 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:avf_virtualizationservice_prop:s0 tclass=file permissive=1
05-24 20:21:11.260  1408  1408 I getprop : type=1400 audit(0.0:69): avc:  denied  { map } for  path="/dev/__properties__/u:object_r:avf_virtualizationservice_prop:s0" dev="tmpfs" ino=8284 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:avf_virtualizationservice_prop:s0 tclass=file permissive=1
05-24 20:21:11.260  1408  1408 I auditd  : type=1400 audit(0.0:73): avc:  denied  { open } for  comm="getprop" path="/dev/__properties__/u:object_r:bluetooth_audio_hal_prop:s0" dev="tmpfs" ino=8290 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:bluetooth_audio_hal_prop:s0 tclass=file permissive=1
05-24 20:21:11.260  1408  1408 I getprop : type=1400 audit(0.0:73): avc:  denied  { open } for  path="/dev/__properties__/u:object_r:bluetooth_audio_hal_prop:s0" dev="tmpfs" ino=8290 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:bluetooth_audio_hal_prop:s0 tclass=file permissive=1
05-24 20:21:11.260  1408  1408 I auditd  : type=1400 audit(0.0:74): avc:  denied  { getattr } for  comm="getprop" path="/dev/__properties__/u:object_r:bluetooth_audio_hal_prop:s0" dev="tmpfs" ino=8290 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:bluetooth_audio_hal_prop:s0 tclass=file permissive=1
05-24 20:21:11.260  1408  1408 I getprop : type=1400 audit(0.0:74): avc:  denied  { getattr } for  path="/dev/__properties__/u:object_r:bluetooth_audio_hal_prop:s0" dev="tmpfs" ino=8290 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:bluetooth_audio_hal_prop:s0 tclass=file permissive=1
05-24 20:21:11.260  1408  1408 I auditd  : type=1400 audit(0.0:75): avc:  denied  { map } for  comm="getprop" path="/dev/__properties__/u:object_r:bluetooth_audio_hal_prop:s0" dev="tmpfs" ino=8290 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:bluetooth_audio_hal_prop:s0 tclass=file permissive=1
05-24 20:21:11.260  1408  1408 I getprop : type=1400 audit(0.0:75): avc:  denied  { map } for  path="/dev/__properties__/u:object_r:bluetooth_audio_hal_prop:s0" dev="tmpfs" ino=8290 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:bluetooth_audio_hal_prop:s0 tclass=file permissive=1
05-24 20:21:11.280  1408  1408 I auditd  : type=1400 audit(0.0:307): avc:  denied  { open } for  comm="getprop" path="/dev/__properties__/u:object_r:drm_service_config_prop:s0" dev="tmpfs" ino=8398 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:drm_service_config_prop:s0 tclass=file permissive=1
05-24 20:21:11.280  1408  1408 I getprop : type=1400 audit(0.0:307): avc:  denied  { open } for  path="/dev/__properties__/u:object_r:drm_service_config_prop:s0" dev="tmpfs" ino=8398 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:drm_service_config_prop:s0 tclass=file permissive=1
05-24 20:21:11.280  1408  1408 I auditd  : type=1400 audit(0.0:308): avc:  denied  { getattr } for  comm="getprop" path="/dev/__properties__/u:object_r:drm_service_config_prop:s0" dev="tmpfs" ino=8398 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:drm_service_config_prop:s0 tclass=file permissive=1
05-24 20:21:11.280  1408  1408 I getprop : type=1400 audit(0.0:308): avc:  denied  { getattr } for  path="/dev/__properties__/u:object_r:drm_service_config_prop:s0" dev="tmpfs" ino=8398 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:drm_service_config_prop:s0 tclass=file permissive=1
05-24 20:21:11.280  1408  1408 I auditd  : type=1400 audit(0.0:309): avc:  denied  { map } for  comm="getprop" path="/dev/__properties__/u:object_r:drm_service_config_prop:s0" dev="tmpfs" ino=8398 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:drm_service_config_prop:s0 tclass=file permissive=1
05-24 20:21:11.280  1408  1408 I getprop : type=1400 audit(0.0:309): avc:  denied  { map } for  path="/dev/__properties__/u:object_r:drm_service_config_prop:s0" dev="tmpfs" ino=8398 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:drm_service_config_prop:s0 tclass=file permissive=1
05-24 20:21:11.321  1214  1214 E [GF_HAL][ShenzhenSensor]: [init] gainvalue: 150/100
05-24 20:21:11.321  1214  1214 E [GF_HAL][ShenzhenSensor]: [init] expotime 38
05-24 20:21:11.321  1214  1214 E [GF_HAL][ShenzhenSensor]: [init] @@@@@ mQRCode=Z918095013A0061493,len=18
05-24 20:21:11.323  1214  1214 E [GF_HAL][ShenzhenSensor]: [init] module_type = 0x6
05-24 20:21:11.323  1214  1214 E [GF_HAL][ShenzhenSensor]: [init] lens_type = 0xa
05-24 20:21:11.280  1408  1408 I auditd  : type=1400 audit(0.0:373): avc:  denied  { open } for  comm="getprop" path="/dev/__properties__/u:object_r:init_perf_lsm_hooks_prop:s0" dev="tmpfs" ino=8440 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:init_perf_lsm_hooks_prop:s0 tclass=file permissive=1
05-24 20:21:11.280  1408  1408 I getprop : type=1400 audit(0.0:373): avc:  denied  { open } for  path="/dev/__properties__/u:object_r:init_perf_lsm_hooks_prop:s0" dev="tmpfs" ino=8440 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:init_perf_lsm_hooks_prop:s0 tclass=file permissive=1
05-24 20:21:11.280  1408  1408 I auditd  : type=1400 audit(0.0:374): avc:  denied  { getattr } for  comm="getprop" path="/dev/__properties__/u:object_r:init_perf_lsm_hooks_prop:s0" dev="tmpfs" ino=8440 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:init_perf_lsm_hooks_prop:s0 tclass=file permissive=1
05-24 20:21:11.280  1408  1408 I getprop : type=1400 audit(0.0:374): avc:  denied  { getattr } for  path="/dev/__properties__/u:object_r:init_perf_lsm_hooks_prop:s0" dev="tmpfs" ino=8440 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:init_perf_lsm_hooks_prop:s0 tclass=file permissive=1
05-24 20:21:11.280  1408  1408 I auditd  : type=1400 audit(0.0:375): avc:  denied  { map } for  comm="getprop" path="/dev/__properties__/u:object_r:init_perf_lsm_hooks_prop:s0" dev="tmpfs" ino=8440 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:init_perf_lsm_hooks_prop:s0 tclass=file permissive=1
05-24 20:21:11.280  1408  1408 I getprop : type=1400 audit(0.0:375): avc:  denied  { map } for  path="/dev/__properties__/u:object_r:init_perf_lsm_hooks_prop:s0" dev="tmpfs" ino=8440 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:init_perf_lsm_hooks_prop:s0 tclass=file permissive=1
05-24 20:21:11.280  1408  1408 I auditd  : type=1400 audit(0.0:376): avc:  denied  { open } for  comm="getprop" path="/dev/__properties__/u:object_r:init_storage_prop:s0" dev="tmpfs" ino=8443 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:init_storage_prop:s0 tclass=file permissive=1
05-24 20:21:11.280  1408  1408 I getprop : type=1400 audit(0.0:376): avc:  denied  { open } for  path="/dev/__properties__/u:object_r:init_storage_prop:s0" dev="tmpfs" ino=8443 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:init_storage_prop:s0 tclass=file permissive=1
05-24 20:21:11.280  1408  1408 I auditd  : type=1400 audit(0.0:377): avc:  denied  { getattr } for  comm="getprop" path="/dev/__properties__/u:object_r:init_storage_prop:s0" dev="tmpfs" ino=8443 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:init_storage_prop:s0 tclass=file permissive=1
05-24 20:21:11.280  1408  1408 I getprop : type=1400 audit(0.0:377): avc:  denied  { getattr } for  path="/dev/__properties__/u:object_r:init_storage_prop:s0" dev="tmpfs" ino=8443 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:init_storage_prop:s0 tclass=file permissive=1
05-24 20:21:11.288  1408  1408 I auditd  : type=1400 audit(0.0:378): avc:  denied  { map } for  comm="getprop" path="/dev/__properties__/u:object_r:init_storage_prop:s0" dev="tmpfs" ino=8443 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:init_storage_prop:s0 tclass=file permissive=1
05-24 20:21:11.288  1408  1408 I getprop : type=1400 audit(0.0:378): avc:  denied  { map } for  path="/dev/__properties__/u:object_r:init_storage_prop:s0" dev="tmpfs" ino=8443 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:init_storage_prop:s0 tclass=file permissive=1
05-24 20:21:11.288  1408  1408 I auditd  : type=1400 audit(0.0:379): avc:  denied  { open } for  comm="getprop" path="/dev/__properties__/u:object_r:init_svc_debug_prop:s0" dev="tmpfs" ino=8444 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:init_svc_debug_prop:s0 tclass=file permissive=1
05-24 20:21:11.288  1408  1408 I getprop : type=1400 audit(0.0:379): avc:  denied  { open } for  path="/dev/__properties__/u:object_r:init_svc_debug_prop:s0" dev="tmpfs" ino=8444 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:init_svc_debug_prop:s0 tclass=file permissive=1
05-24 20:21:11.288  1408  1408 I auditd  : type=1400 audit(0.0:380): avc:  denied  { getattr } for  comm="getprop" path="/dev/__properties__/u:object_r:init_svc_debug_prop:s0" dev="tmpfs" ino=8444 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:init_svc_debug_prop:s0 tclass=file permissive=1
05-24 20:21:11.288  1408  1408 I getprop : type=1400 audit(0.0:380): avc:  denied  { getattr } for  path="/dev/__properties__/u:object_r:init_svc_debug_prop:s0" dev="tmpfs" ino=8444 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:init_svc_debug_prop:s0 tclass=file permissive=1
05-24 20:21:11.288  1408  1408 I auditd  : type=1400 audit(0.0:381): avc:  denied  { map } for  comm="getprop" path="/dev/__properties__/u:object_r:init_svc_debug_prop:s0" dev="tmpfs" ino=8444 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:init_svc_debug_prop:s0 tclass=file permissive=1
05-24 20:21:11.288  1408  1408 I getprop : type=1400 audit(0.0:381): avc:  denied  { map } for  path="/dev/__properties__/u:object_r:init_svc_debug_prop:s0" dev="tmpfs" ino=8444 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:init_svc_debug_prop:s0 tclass=file permissive=1
05-24 20:21:11.292  1408  1408 I auditd  : type=1400 audit(0.0:456): avc:  denied  { open } for  comm="getprop" path="/dev/__properties__/u:object_r:system_service_enable_prop:s0" dev="tmpfs" ino=8652 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:system_service_enable_prop:s0 tclass=file permissive=1
05-24 20:21:11.292  1408  1408 I getprop : type=1400 audit(0.0:456): avc:  denied  { open } for  path="/dev/__properties__/u:object_r:system_service_enable_prop:s0" dev="tmpfs" ino=8652 scontext=u:r:untrusted_app:s0 tcontext=u:object_r:system_service_enable_prop:s0 tclass=file permissive=1
05-24 20:21:11.372   885   885 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlags
05-24 20:21:11.372   885   885 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlagsImpl
05-24 20:21:11.372   885   885 W Zygote  : Class not found for preloading: android.media.audio.Flags
05-24 20:21:11.429  1209  1209 W HidlServiceManagement: Waited one second for vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default
05-24 20:21:11.430   575   575 I hwservicemanager: Since vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 20:21:11.431  1209  1209 I HidlServiceManagement: getService: Trying again for vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default...
05-24 20:21:11.433   575  1414 I hwservicemanager: Tried to start vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 20:21:11.798  1214  1214 E [GF_HAL][FingerprintCore]: [init_report_data] algo version is V03.02.02.230.005
05-24 20:21:11.798  1214  1214 E [GF_HAL][FingerprintCore]: [init_report_data] lcdtype_prop = SDC
05-24 20:21:11.799  1214  1214 E [GF_HAL][FingerprintCore]: [init_report_data] type = V03.02.02.230.005_S_SDC
05-24 20:21:11.800  1419  1419 W idmap2  : failed to find resource 'bool/grant_location_permission_enabled'
05-24 20:21:11.824  1214  1214 I HidlServiceManagement: Registered vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default
05-24 20:21:11.825  1214  1214 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.biometrics.fingerprint@2.1-service to fingerprint@2.1-service.
05-24 20:21:11.828   575   575 W hwservicemanager: Detected instance of android.hardware.biometrics.fingerprint@2.3::IBiometricsFingerprint (pid: 1209) registering over instance of or with base of android.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint (pid: 1214).
05-24 20:21:11.830  1209  1209 I HidlServiceManagement: Registered android.hardware.biometrics.fingerprint@2.3::IBiometricsFingerprint/default
05-24 20:21:11.831  1209  1209 I HidlServiceManagement: Removing namespace from <NAME_EMAIL>6893 to fingerprint@2.3-service.mt6893.
05-24 20:21:11.911  1241  1241 I LSPosedService: service package is not started, wait 1s.
05-24 20:21:12.037  1434  1443 W system_server: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: stats
05-24 20:21:12.037  1434  1443 W BpBinder: Linking to death on android.os.IStatsd but there are no threads (yet?) listening to incoming transactions. See ProcessState::startThreadPool and ProcessState::setThreadPoolMaxThreadCount. Generally you should setup the binder threadpool before other initialization steps.
05-24 20:21:12.038  1434  1434 W system_server: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: serial
05-24 20:21:12.092  1434  1434 W BpBinder: Linking to death on org.lsposed.lspd.service.ILSPApplicationService but there are no threads (yet?) listening to incoming transactions. See ProcessState::startThreadPool and ProcessState::setThreadPoolMaxThreadCount. Generally you should setup the binder threadpool before other initialization steps.
05-24 20:21:12.257  1084  1110 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 20:21:12.257  1084  1110 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:2
05-24 20:21:12.476   987   987 I ccci_mdinit: (1):MD1 set status: mtk.md1.status=ready 
05-24 20:21:12.477   987   987 I ccci_mdinit: (1):start_service init.svc.emdlogger1, but returned 0, maybe has no this property
05-24 20:21:12.477   987   987 I ccci_mdinit: (1):wait_for_property: is_stop_wait: 0
05-24 20:21:12.488   987   987 I ccci_mdinit: (1):start_service init.svc.vendor.gsm0710muxd, but returned 0, maybe has no this property
05-24 20:21:12.497   987   987 I ccci_mdinit: (1):wait_for_property: is_stop_wait: 0
05-24 20:21:12.497   987   987 I ccci_mdinit: (1):wait_for_property:success(init.svc.vendor.gsm0710muxd=running), loop:600
05-24 20:21:12.912  1241  1241 I LSPosedService: service package is not started, wait 1s.
05-24 20:21:12.964  1478  1478 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/slot1
05-24 20:21:12.966  1478  1478 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSlot1
05-24 20:21:12.968  1478  1478 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/imsAospSlot1
05-24 20:21:12.969  1478  1483 E SchedPolicy: open of /dev/cpuctl/bg_non_interactive/tasks failed: No such file or directory
05-24 20:21:12.970  1478  1478 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/imsSlot1
05-24 20:21:12.972  1478  1478 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/se1
05-24 20:21:12.973  1478  1478 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSe1
05-24 20:21:12.974  1478  1490 I RmcVsim : [0] RmcVsimUrcHandler init slot: 0, ch id 0
05-24 20:21:12.975  1478  1478 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/em1
05-24 20:21:12.976  1478  1492 I RmcVsim : [1] RmcVsimUrcHandler init slot: 1, ch id 0
05-24 20:21:12.976  1478  1478 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkEm1
05-24 20:21:12.977  1478  1478 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkAssist1
05-24 20:21:12.978  1478  1478 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRcs1
05-24 20:21:12.979  1478  1478 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkCap1
05-24 20:21:12.980  1478  1478 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSmartRatSwitch1
05-24 20:21:12.983  1478  1478 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRsu1
05-24 20:21:12.985  1478  1478 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/slot2
05-24 20:21:12.986  1478  1478 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSlot2
05-24 20:21:12.987  1478  1478 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/imsAospSlot2
05-24 20:21:12.988  1478  1478 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/imsSlot2
05-24 20:21:12.989  1478  1478 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/se2
05-24 20:21:12.990  1478  1478 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSe2
05-24 20:21:12.992  1478  1478 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/em2
05-24 20:21:12.993  1478  1478 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkEm2
05-24 20:21:12.995  1478  1478 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkAssist2
05-24 20:21:12.996  1478  1478 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRcs2
05-24 20:21:13.001  1478  1478 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkCap2
05-24 20:21:13.002  1478  1478 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSmartRatSwitch2
05-24 20:21:13.003  1478  1478 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRsu2
05-24 20:21:13.006  1478  1478 I RILC-OplusRadioAIDL: radio::registerService starting IOplusRadio vendor.oplus.hardware.radio.IRadioStable/OplusRadio0
05-24 20:21:13.014  1478  1478 I RILC-OplusRadioAIDL: radio::registerService IOplusRadio status: 0
05-24 20:21:13.014  1478  1478 I RILC-OplusRadioAIDL: radio::registerService starting IOplusRadio vendor.oplus.hardware.radio.IRadioStable/OplusRadio1
05-24 20:21:13.015  1478  1478 I RILC-OplusRadioAIDL: radio::registerService IOplusRadio status: 0
05-24 20:21:13.015  1478  1478 I RILC-OplusAppRadio: radio::registerService starting IOplusAppRadio oplus_app_slot1
05-24 20:21:13.015  1478  1511 I RmcDcImsDc2ReqHandler: [0][handleImsBearerNotify] urc=+EIMSPDN: "init"
05-24 20:21:13.018  1075  1075 I AVSync  : avInit, st 1f6ef46ec, int=8, frac=703825c3
05-24 20:21:13.018  1075  1075 I vtservice: [VT][SRV]after VTService_instantiate
05-24 20:21:13.019  1075  1550 I AVSync  : avInit, st 1f70ad8ea, int=8, frac=70aeb321
05-24 20:21:13.023  1478  1478 I HidlServiceManagement: Registered vendor.oplus.hardware.appradio@1.0::IOplusAppRadio/oplus_app_slot1
05-24 20:21:13.023  1478  1478 I RILC-OplusAppRadio: radio::registerService IOplusAppRadio status: 0
05-24 20:21:13.023  1478  1478 I RILC-OplusAppRadio: radio::registerService starting IOplusAppRadio oplus_app_slot2
05-24 20:21:13.024   884   891 I ServiceManagerCppClient: Waiting for service 'statscompanion' on '/dev/binder'...
05-24 20:21:13.024  1478  1478 I HidlServiceManagement: Registered vendor.oplus.hardware.appradio@1.0::IOplusAppRadio/oplus_app_slot2
05-24 20:21:13.024  1478  1478 I RILC-OplusAppRadio: radio::registerService IOplusAppRadio status: 0
05-24 20:21:13.028   927  1375 I VT HIDL : [IVT] [VT THREAD] [VT_Bind] des = volte_imsvt1 initialize communication
05-24 20:21:13.030  1478  1533 I RmcDcImsDc2ReqHandler: [1][handleImsBearerNotify] urc=+EIMSPDN: "init"
05-24 20:21:13.032  1478  1483 I WpfaCppUtils: initialRuleContainer!
05-24 20:21:13.035  1478  1478 I HidlServiceManagement: Registered android.hardware.radio.config@1.3::IRadioConfig/default
05-24 20:21:13.038  1478  1483 I WpfaCppUtils: initialA2MRingBuffer!
05-24 20:21:13.066  1478  1478 I HidlServiceManagement: Registered android.hardware.radio@1.2::ISap/slot1
05-24 20:21:13.067  1478  1478 I HidlServiceManagement: Registered android.hardware.radio@1.2::ISap/slot2
05-24 20:21:13.202  1434  1434 E system_server: memevent listener failed to initialize, not supported kernel
05-24 20:21:13.257  1084  1110 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 20:21:13.257  1084  1110 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:3
05-24 20:21:13.335  1478  1559 E libmnlUtils: No action: deInitReaderLoop can't get mMnlsocket
05-24 20:21:13.335  1478  1559 I wpfa    : initReaderLoop() done, buf_size=67583
05-24 20:21:13.335  1478  1559 I wpfa    : WPFA_DL initialized
05-24 20:21:13.421  1098  1098 I KEYFILE : [INFO   ] CustomCommon.cpp:161 init() ro.vendor.config.oplus.low_ram = 0
05-24 20:21:13.422  1098  1098 I KEYFILE : [INFO   ] CustomCommon.cpp:162 init() vendor.debug.camera.bss.aishutter.weighting = 100,98,96,94,92,90,90,90
05-24 20:21:13.423  1098  1098 I KEYFILE : [INFO   ] CustomCommon.cpp:163 init() vendor.debug.tpi.s.semi.run = 0
05-24 20:21:13.423  1098  1098 I KEYFILE : [INFO   ] CustomCommon.cpp:164 init() vendor.debug.camera.FDAsync = true
05-24 20:21:13.423  1098  1098 E KEYFILE : [ERROR   ] CustomMetadata.cpp:375 init() PROP_SYS_CAM_PACKNAME err 0!
05-24 20:21:13.423  1098  1098 I KEYFILE : [INFO   ] CustomerData.cpp:93 init() 0xb400007d554e9860, size: 288 byte
05-24 20:21:13.634  1478  1483 E RILC-OplusRadioAIDL: oplusRadioServiceAIDL: oplusRadioServiceAIDL[0]->mRadioIndicationOplus == NULL
05-24 20:21:13.862  1478  1483 E RILC-OplusRadioAIDL: oplusRadioServiceAIDL: oplusRadioServiceAIDL[1]->mRadioIndicationOplus == NULL
05-24 20:21:13.868  1434  1434 I SystemServerInitThreadPool: Creating instance with 8 threads
05-24 20:21:13.913  1241  1241 I LSPosedService: service package is not started, wait 1s.
05-24 20:21:13.913  1434  1434 E LSPosed-Bridge: java.lang.NoSuchMethodError: com.android.server.pm.PackageManagerServiceUtils#checkDowngrade(com.android.server.pm.pkg.AndroidPackage,android.content.pm.PackageInfoLite)#exact
05-24 20:21:13.913  1434  1434 E LSPosed-Bridge: 	at LSPHooker_.startBootstrapServices(Unknown Source:11)
05-24 20:21:13.913  1434  1434 E LSPosed-Bridge: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:597)
05-24 20:21:13.913  1434  1434 E LSPosed-Bridge: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:889)
05-24 20:21:13.931  1434  1592 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-com.android.providers.media.module.xml
05-24 20:21:13.932  1434  1592 I SystemConfig: Reading permissions from /system/etc/sysconfig/app.grapheneos.gmscompat.xml
05-24 20:21:13.933  1434  1592 I SystemConfig: Reading permissions from /system/etc/sysconfig/app.grapheneos.networklocation.xml
05-24 20:21:13.933  1434  1592 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-platform-full-base.xml
05-24 20:21:13.934  1434  1592 I SystemConfig: Reading permissions from /system/etc/sysconfig/framework-sysconfig.xml
05-24 20:21:13.935  1434  1592 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-asl-files.xml
05-24 20:21:13.935  1434  1592 I SystemConfig: Reading permissions from /system/etc/sysconfig/hiddenapi-whitelist-co.aospa.sense.xml
05-24 20:21:13.936  1434  1592 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-strict-signature.xml
05-24 20:21:13.936  1434  1592 I SystemConfig: Reading permissions from /system/etc/sysconfig/backup.xml
05-24 20:21:13.937  1434  1592 I SystemConfig: Reading permissions from /system/etc/sysconfig/hiddenapi-package-whitelist.xml
05-24 20:21:13.938  1434  1592 I SystemConfig: Reading permissions from /system/etc/sysconfig/enhanced-confirmation.xml
05-24 20:21:13.938  1434  1592 I SystemConfig: Reading permissions from /system/etc/sysconfig/package-shareduid-allowlist.xml
05-24 20:21:13.939  1434  1592 I SystemConfig: Reading permissions from /system/etc/sysconfig/initial-package-stopped-states.xml
05-24 20:21:13.939  1434  1592 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-platform-handheld-system.xml
05-24 20:21:13.940  1434  1592 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-platform.xml
05-24 20:21:13.940  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/DigitalWellbeing.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.940  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/GoogleContacts.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.940  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/GoogleContactsSyncAdapter.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.940  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/Drive.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.940  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/GoogleMaps.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.940  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.intentresolver.xml
05-24 20:21:13.941  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/GoogleCalendar.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.941  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/CarrierServices.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.941  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/GoogleDialer.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.941  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/GoogleOneTimeInitializer.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.941  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.documentsui.xml
05-24 20:21:13.942  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/app.grapheneos.networklocation.xml
05-24 20:21:13.942  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/DeviceHealthServices.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.943  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/ExtraFiles.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.943  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/org.apache.http.legacy.xml
05-24 20:21:13.943  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.live_wallpaper.xml
05-24 20:21:13.943  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/GooglePlayStore.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.944  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/privapp-permissions-mediatek.xml
05-24 20:21:13.944  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/GBoard.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.944  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/GoogleKeep.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.944  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/GoogleRestore.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.944  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.nfc_extras.xml
05-24 20:21:13.944  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.networkstack.xml
05-24 20:21:13.945  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.credentials.xml
05-24 20:21:13.945  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/privapp-permissions-platform.xml
05-24 20:21:13.947  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/android.test.mock.xml
05-24 20:21:13.948  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/android.test.base.xml
05-24 20:21:13.948  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.webview.xml
05-24 20:21:13.949  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/android.hardware.biometrics.face.xml
05-24 20:21:13.949  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/GoogleMessages.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.949  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/app.grapheneos.logviewer.xml
05-24 20:21:13.949  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/GoogleServicesFramework.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.949  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/GoogleCalendarSyncAdapter.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.949  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/javax.obex.xml
05-24 20:21:13.950  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.location.provider.xml
05-24 20:21:13.951  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/GoogleLocationHistory.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.951  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/android.test.runner.xml
05-24 20:21:13.951  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/GooglePhotos.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.951  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.media.remotedisplay.xml
05-24 20:21:13.951  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.sip.voip.xml
05-24 20:21:13.952  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.window_magnification.xml
05-24 20:21:13.952  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/GoogleClock.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.952  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/GoogleCalculator.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.952  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.future.usb.accessory.xml
05-24 20:21:13.953  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.mediadrm.signer.xml
05-24 20:21:13.953  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/privapp_whitelist_co.aospa.sense.xml
05-24 20:21:13.953  1434  1592 I SystemConfig: Non-xml file /system/etc/permissions/GmsCore.prop in /system/etc/permissions directory, ignoring
05-24 20:21:13.953  1434  1592 I SystemConfig: Reading permissions from /system/etc/permissions/platform.xml
05-24 20:21:13.955  1434  1434 I SystemServiceManager: Starting com.android.server.security.FileIntegrityService
05-24 20:21:13.955  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.ese.xml
05-24 20:21:13.956  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.xml
05-24 20:21:13.956  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.location.gps.xml
05-24 20:21:13.956  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.multitouch.distinct.xml
05-24 20:21:13.956  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.verified_boot.xml
05-24 20:21:13.957  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.mediatek.hardware.vow.xml
05-24 20:21:13.957  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.accelerometer.xml
05-24 20:21:13.957  1434  1434 I SystemServiceManager: Starting com.android.server.pm.Installer
05-24 20:21:13.957  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.bluetooth_le.xml
05-24 20:21:13.958  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.flash-autofocus.xml
05-24 20:21:13.958  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.stepcounter.xml
05-24 20:21:13.958  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.gyroscope.xml
05-24 20:21:13.959  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.wifi.passpoint.xml
05-24 20:21:13.959  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.telephony.ims.xml
05-24 20:21:13.959  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.bluetooth.xml
05-24 20:21:13.959  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.raw.xml
05-24 20:21:13.960  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.android.nfc_extras.xml
05-24 20:21:13.960  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.vulkan.version.xml
05-24 20:21:13.960  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.front.xml
05-24 20:21:13.960  1434  1434 I SystemServiceManager: Starting com.android.server.os.DeviceIdentifiersPolicyService
05-24 20:21:13.961  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.hifi_sensors.xml
05-24 20:21:13.961  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.opengles.deqp.level.xml
05-24 20:21:13.961  1434  1434 I SystemServiceManager: Starting com.android.server.flags.FeatureFlagsService
05-24 20:21:13.961  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.xml
05-24 20:21:13.962  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/nfc_features.xml
05-24 20:21:13.962  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.usb.accessory.xml
05-24 20:21:13.962  1434  1434 I SystemServiceManager: Starting com.android.server.uri.UriGrantsManagerService$Lifecycle
05-24 20:21:13.962  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.telephony.gsm.xml
05-24 20:21:13.963  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.wifi.xml
05-24 20:21:13.963  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.freeform_window_management.xml
05-24 20:21:13.963  1434  1434 I SystemServiceManager: Starting com.android.server.powerstats.PowerStatsService
05-24 20:21:13.964  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.hcef.xml
05-24 20:21:13.964  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.midi.xml
05-24 20:21:13.965  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.usb.host.xml
05-24 20:21:13.965  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.fingerprint.xml
05-24 20:21:13.965  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.multitouch.jazzhand.xml
05-24 20:21:13.966  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.multitouch.xml
05-24 20:21:13.966  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.ipsec_tunnels.xml
05-24 20:21:13.966  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.se.omapi.uicc.xml
05-24 20:21:13.966   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.power.stats@1.0::IPowerStats/default in either framework or device VINTF manifest.
05-24 20:21:13.966  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.opengles.aep.xml
05-24 20:21:13.967  1434  1434 E PowerStatsService: Unable to get power.stats HAL service.
05-24 20:21:13.967  1434  1434 E PowerStatsService: nativeInit failed to connect to power.stats HAL
05-24 20:21:13.967  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.se.omapi.ese.xml
05-24 20:21:13.967  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/handheld_core_hardware.xml
05-24 20:21:13.967  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.hce.xml
05-24 20:21:13.968  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.faketouch.xml
05-24 20:21:13.968  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.light.xml
05-24 20:21:13.968  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.audio.low_latency.xml
05-24 20:21:13.968  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.vulkan.deqp.level.xml
05-24 20:21:13.969  1434  1434 I HidlServiceManagement: Registered android.frameworks.stats@1.0::IStats/default
05-24 20:21:13.969  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.vulkan.level.xml
05-24 20:21:13.969  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.xml
05-24 20:21:13.969  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.vulkan.compute.xml
05-24 20:21:13.970  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.wifi.direct.xml
05-24 20:21:13.970  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.nxp.mifare.xml
05-24 20:21:13.970  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.stepdetector.xml
05-24 20:21:13.971  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.compass.xml
05-24 20:21:13.971  1434  1434 I SystemServiceManager: Starting com.android.server.permission.access.AccessCheckingService
05-24 20:21:13.971  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.proximity.xml
05-24 20:21:13.971  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.mediatek.hardware.vow_dsp.xml
05-24 20:21:13.971  1434  1434 I SystemServiceManager: Starting com.android.server.wm.ActivityTaskManagerService$Lifecycle
05-24 20:21:13.972  1434  1592 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.full.xml
05-24 20:21:13.973  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-overlays.xml
05-24 20:21:13.974  1434  1434 I SystemServiceManager: Starting com.android.server.am.ActivityManagerService$Lifecycle
05-24 20:21:13.974  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/GoogleCamera_6gb_or_more_ram.xml
05-24 20:21:13.974  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/nga.xml
05-24 20:21:13.974  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-org.lineageos.glimpse.xml
05-24 20:21:13.975  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-google-install-constraints-package-allowlist.xml
05-24 20:21:13.975  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/quick_tap.xml
05-24 20:21:13.976  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/initial-package-stopped-states-org.lineageos.etar.xml
05-24 20:21:13.976  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/google.xml
05-24 20:21:13.977  1434  1592 I SystemConfig: Adding association: com.google.android.as <- com.android.bluetooth.services
05-24 20:21:13.977  1434  1592 I SystemConfig: Adding association: com.google.android.as <- com.google.android.bluetooth.services
05-24 20:21:13.978  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/pixel_experience_2017.xml
05-24 20:21:13.978  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-voltage-product.xml
05-24 20:21:13.979  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/pixel_2016_exclusive.xml
05-24 20:21:13.979  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/adaptivecharging.xml
05-24 20:21:13.979  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-handheld-product.xml
05-24 20:21:13.980  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/com.android.messaging.allowlist.xml
05-24 20:21:13.980  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-google-hiddenapi-package-whitelist.xml
05-24 20:21:13.981  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/voltage-component-overrides.xml
05-24 20:21:13.981  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-google-staged-installer-whitelist.xml
05-24 20:21:13.982  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/google_build.xml
05-24 20:21:13.982  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-org.lineageos.etar.xml
05-24 20:21:13.983  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/com.android.deskclock_allowlist.xml
05-24 20:21:13.983  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/dreamliner.xml
05-24 20:21:13.983  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/initial-package-stopped-states-org.lineageos.glimpse.xml
05-24 20:21:13.984  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/initial-package-stopped-states-org.lineageos.aperture.xml
05-24 20:21:13.984  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-preinstalled-packages-product-pixel-2017-and-newer.xml
05-24 20:21:13.985  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/d2d_cable_migration_feature.xml
05-24 20:21:13.985  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-telephony-product.xml
05-24 20:21:13.986  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/org.lineageos.etar.allowlist.xml
05-24 20:21:13.986  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-org.lineageos.aperture.xml
05-24 20:21:13.986  1434  1592 I SystemConfig: Reading permissions from /product/etc/sysconfig/nexus.xml
05-24 20:21:13.987  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.maps.xml
05-24 20:21:13.988  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.widevine.software.drm.xml
05-24 20:21:13.988  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/privapp-permissions-hotword.xml
05-24 20:21:13.988  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/android.software.freeform_window_management.xml
05-24 20:21:13.989  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.gms.xml
05-24 20:21:13.990  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/privapp-permissions-googleapps-turbo.xml
05-24 20:21:13.990  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.wellbeing.xml
05-24 20:21:13.991  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/privapp-permissions-xhotword.xml
05-24 20:21:13.991  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.onetimeinitializer.xml
05-24 20:21:13.992  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.dialer.support.xml
05-24 20:21:13.992  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.keep.xml
05-24 20:21:13.993  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-split-permissions-google.xml
05-24 20:21:13.993  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.dialer.xml
05-24 20:21:13.994  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-privapp-permissions-google-p.xml
05-24 20:21:13.997  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.turbo.xml
05-24 20:21:13.998  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.vending.xml
05-24 20:21:13.999  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/privapp_whitelist_com.android.dialer-ext.xml
05-24 20:21:14.000  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.dialer.xml
05-24 20:21:14.000  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.media.effects.xml
05-24 20:21:14.001  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.ims.xml
05-24 20:21:14.001  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.messaging.xml
05-24 20:21:14.002  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/android.software.angle.xml
05-24 20:21:14.002  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.imsserviceentitlement.xml
05-24 20:21:14.003  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.settings.intelligence.xml
05-24 20:21:14.003  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-privapp-permissions-google-se.xml
05-24 20:21:14.005  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-privapp-permissions-google.xml
05-24 20:21:14.005  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.contacts.xml
05-24 20:21:14.006  1434  1592 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.restore.xml
05-24 20:21:14.006  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/sysconfig/allowlist_com.stevesoltys.seedvault.xml
05-24 20:21:14.007  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/sysconfig/android.telephony.satellite.xml
05-24 20:21:14.009  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.emergency.xml
05-24 20:21:14.010  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_whitelist_com.android.launcher3-ext.xml
05-24 20:21:14.010  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_allowlist_com.libremobileos.freeform.xml
05-24 20:21:14.011  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.hotwordenrollment.common.util.xml
05-24 20:21:14.011  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/permissions_com.stevesoltys.seedvault.xml
05-24 20:21:14.012  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_whitelist_org.lineageos.setupwizard.xml
05-24 20:21:14.012  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/androidx.window.sidecar.xml
05-24 20:21:14.013  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.bluetooth.bthelper.xml
05-24 20:21:14.013  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_whitelist_io.chaldeaprjkt.gamespace.xml
05-24 20:21:14.014  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/android.software.theme_picker.xml
05-24 20:21:14.014  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.systemui.xml
05-24 20:21:14.015  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.settings.xml
05-24 20:21:14.015  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.carrierconfig.xml
05-24 20:21:14.016  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/androidx.window.extensions.xml
05-24 20:21:14.017  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_allowlist_com.libremobileos.sidebar.xml
05-24 20:21:14.017  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp-permissions-custom.xml
05-24 20:21:14.018  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.launcher3.xml
05-24 20:21:14.018  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.google.android.gsf.xml
05-24 20:21:14.019  1434  1592 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.storagemanager.xml
05-24 20:21:14.020  1434  1592 I SystemConfig: Reading permissions from /apex/com.android.tethering/etc/permissions/permissions.xml
05-24 20:21:14.021  1434  1592 I SystemConfig: Reading permissions from /apex/com.android.nfcservices/etc/permissions/com.android.nfc.xml
05-24 20:21:14.022  1434  1592 I SystemConfig: Reading permissions from /apex/com.android.cellbroadcast/etc/permissions/com.android.cellbroadcastreceiver.module.xml
05-24 20:21:14.023  1434  1592 I SystemConfig: Reading permissions from /apex/com.android.cellbroadcast/etc/permissions/com.android.cellbroadcastservice.xml
05-24 20:21:14.024  1434  1592 I SystemConfig: Reading permissions from /apex/com.android.adservices/etc/permissions/com.android.adservices.api.xml
05-24 20:21:14.025  1434  1592 I SystemConfig: Reading permissions from /apex/com.android.mediaprovider/etc/permissions/com.android.photopicker.xml
05-24 20:21:14.025  1434  1592 I SystemConfig: Reading permissions from /apex/com.android.mediaprovider/etc/permissions/com.android.providers.media.module.xml
05-24 20:21:14.027  1434  1592 I SystemConfig: Reading permissions from /apex/com.android.permission/etc/permissions/com.android.permissioncontroller.xml
05-24 20:21:14.028  1434  1592 I SystemConfig: Reading permissions from /apex/com.android.extservices/etc/permissions/android.ext_sminus.services.xml
05-24 20:21:14.030  1434  1592 I SystemConfig: Reading permissions from /apex/com.android.ipsec/etc/permissions/android.net.ipsec.ike.xml
05-24 20:21:14.031  1434  1592 I SystemConfig: Reading permissions from /apex/com.android.apex.cts.shim/etc/permissions/signature-permission-allowlist.xml
05-24 20:21:14.032  1434  1592 I SystemConfig: Reading permissions from /apex/com.android.healthfitness/etc/permissions/com.android.healthconnect.controller.xml
05-24 20:21:14.034  1434  1592 I SystemConfig: Reading permissions from /apex/com.android.devicelock/etc/permissions/com.android.devicelockcontroller.xml
05-24 20:21:14.035  1434  1592 I SystemConfig: Reading permissions from /apex/com.android.btservices/etc/permissions/com.android.bluetooth.xml
05-24 20:21:14.037  1434  1592 I incfs   : Initial API level of the device: 30
05-24 20:21:14.040  1434  1599 E system_server: memevent deregister all events failed, failure to initialize
05-24 20:21:14.040  1434  1599 E OomConnection: failed waiting for OOM events: java.lang.RuntimeException: Failed to initialize memevents listener
05-24 20:21:14.153  1434  1553 W android.permission.PermissionManager: Missing ActivityManager; assuming 1047 does not hold android.permission.MANAGE_APP_OPS_MODES
05-24 20:21:14.154  1434  1434 I SystemServiceManager: Starting com.android.server.pm.DataLoaderManagerService
05-24 20:21:14.160  1434  1434 I SystemServiceManager: Starting com.android.server.power.PowerManagerService
05-24 20:21:14.171  1434  1434 I PowerHalLoader: Successfully connected to Power HAL AIDL service.
05-24 20:21:14.174  1434  1434 I SystemServiceManager: Starting com.android.server.power.ThermalManagerService
05-24 20:21:14.179  1434  1434 I SystemServiceManager: Starting com.android.server.recoverysystem.RecoverySystemService$Lifecycle
05-24 20:21:14.181  1434  1434 I SystemServiceManager: Starting com.android.server.lights.LightsService
05-24 20:21:14.183  1434  1434 I SystemServiceManager: Starting com.android.server.display.DisplayManagerService
05-24 20:21:14.193  1434  1434 I SystemServiceManager: Starting phase 100
05-24 20:21:14.208  1434  1434 I UserManagerService: Upgrading users from userVersion 11 to 11
05-24 20:21:14.257  1084  1110 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 20:21:14.257  1084  1110 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:4
05-24 20:21:14.304  1434  1434 W android.permission.PermissionManager: Missing ActivityManager; assuming 1000 holds android.permission.SET_PREFERRED_APPLICATIONS
05-24 20:21:14.334  1434  1434 W PackageManager: No package known for package restrictions com.android.adservices
05-24 20:21:14.347  1434  1434 W PackageManager: No package known for package restrictions com.android.permission
05-24 20:21:14.388  1434  1434 W PackageManager: No package known for package restrictions com.android.btservices
05-24 20:21:14.463  1434  1434 W PackageManager: No package known for package restrictions com.android.extservices
05-24 20:21:14.482  1434  1434 W PackageManager: No package known for package restrictions com.android.nfcservices
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.noCutout on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.font.sanfrancisco on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package org.omnirom.omnijaws on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package com.android.systemui.clocks.metro on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package in.zeta.android on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package com.android.cts.priv.ctsshim on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package org.voltage.theme.font.dosis on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package com.google.android.youtube on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package com.android.uwb.resources on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package com.nikgapps.overlay.messages on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.corner on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package com.android.adservices.api on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.double on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.themepicker on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package com.rifsxd.ksunext on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package app.grapheneos.gmscompat.config on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.settings on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package com.truecaller on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package com.android.healthconnect.controller on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.manhwabuddy on user 0
05-24 20:21:14.489  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.luascans on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.xperia.settings on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.gradicon.android on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.google.android.onetimeinitializer on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.health.connect.backuprestore on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.virtualmachine.res on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.mxtech.videoplayer.pro on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.systemui on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.managedprovisioning.auto_generated_rro_product__ on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package org.omnirom.omnijaws.auto_generated_rro_product__ on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.apkupdater on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.settings on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.narrow on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.systemui on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.systemui on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.settings on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.android on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.documentsui on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.externalstorage on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.server.deviceconfig.resources on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.settings on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.nikgapps.overlay.googlelocationhistory on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.whatsapp on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.companiondevicemanager on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.coderstory.toolkit on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package io.github.jica98 on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.gourmetscans on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package app.grapheneos.logviewer on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.systemui on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.android.systemui.auto_generated_rro_product__ on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.google.android.apps.messaging on user 0
05-24 20:21:14.490  1434  1434 W PackageSettings: Missing permission state for package com.mediatek.engineermode on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.federatedcompute.services on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.android on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.carrierconfig.mt6893 on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package io.chaldeaprjkt.gamespace.auto_generated_rro_product__ on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.lonelycatgames.Xplore on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.system.monet.snowpaintdrop on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.scyllascans on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.nikgapps.overlay.googlephotos on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.xperia.systemui on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package app.komikku on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package jp.pxv.android on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.systemui on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.themepicker on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package net.thunderbird.android on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.systemui.clocks.bignum on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.avatarpicker on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.xayah.databackup.foss on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.systemui on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.font.rookery on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.snowmtl on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.systemui.plugin.globalactions.wallet on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.safetycenter.resources on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package org.zwanoo.android.speedtest on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.system.monet.vivid on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.vending on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.pacprocessor on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.simappdialog on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package org.protonaosp.deviceconfig.auto_generated_rro_product__ on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package eu.darken.sdmse on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.systemui on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.systemui.clocks.growth on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.connectivity.resources on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.hole on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.tall on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.wide on user 0
05-24 20:21:14.491  1434  1434 W PackageSettings: Missing permission state for package com.ancient.telephonyoverlay on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.android.networkstack.overlay on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package org.lineageos.glimpse.frameworksbaseoverlay on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.android.modulemetadata on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.android.certinstaller on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.android.carrierconfig on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.launcher on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.lorn.android on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.android.internal.systemui.navbar.threebutton on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.brave.browser on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.aurorascans on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.android.talkback on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.android.wifi.dialog on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.nikgapps.overlay.gmscore on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.android.hotwordenrollment.xgoogle on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.oneplusparts.overlay.rm on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package ru.mike.updatelocker on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.launcher on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.launcher on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.philiascans on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package co.aospa.sense.auto_generated_rro_product__ on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.kewnscans on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.shojoscans on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.settings on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package org.lineageos.cupida.frameworkresoverlay on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.android on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package ru.andr7e.deviceinfohw on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.android.egg on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.android.launcher3 on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package livio.pack.lang.en_US on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.google.android.trichromelibrary_710306033 on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package org.lineageos.settings.doze.overlay on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.android.backupconfirm on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.font.fluidsans on user 0
05-24 20:21:14.492  1434  1434 W PackageSettings: Missing permission state for package com.axiel7.anihyou on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.settings.auto_generated_rro_vendor__ on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package org.voltage.theme.font.opposans on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.systemui.auto_generated_rro_vendor__ on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.google.android.deskclock on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package org.protonaosp.deviceconfig on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.wmods.wppenhacer on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.systemui.clocks.numoverlap on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.statementservice on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.xperia.android on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.google.android.gm on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package org.calyxos.backup.contacts on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.launcher on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.voltageos.colorstub on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.webtoons on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.oplus.doze.overlay_system on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.settings on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.settings.intelligence on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.systemui.clocks.calligraphy on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.systemui.accessibility.accessibilitymenu on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package org.voltage.theme.font.linotte on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.oplus.doze.overlay_systemui on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.themepicker on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package org.adaway on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.zeptoconsumerapp on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.f0x1d.logfox on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.ancient.frameworkresoverlay.mt6893 on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package ru.tech.imageresizershrinker on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.mangademon on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.ancient.systemuioverlay.mt6893 on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package org.lineageos.setupwizard.auto_generated_rro_product__ on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.sharedstoragebackup on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.launcher on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.printspooler on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.hotwordenrollment.okgoogle on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.emergency.auto_generated_rro_product__ on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.settings on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.dreams.basic on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.settings.overlay.oplus.target on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.launcher on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.providers.settings.auto_generated_rro_product__ on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package org.mozilla.focus on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.photopicker on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.systemui on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.webview on user 0
05-24 20:21:14.493  1434  1434 W PackageSettings: Missing permission state for package com.android.permissioncontroller.overlay on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package app.grapheneos.networklocation on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.google.android.apps.wellbeing on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.coffeemanga on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.android.rkpdapp on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.google.android.dialer on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.launcher on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.android.bips on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.themepicker on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.settings on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.android.intentresolver.auto_generated_rro_product__ on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.android on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package org.eu.droid_ng.jellyfish on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.android.musicfx on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package app.vitune.android on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.google.android.apps.docs on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package ellipi.messenger on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.systemui on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.asurascans on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package app.grapheneos.gmscompat.lib on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package duy.com.text_converter on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.android.customization.themes on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.font.googlesans on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package net.one97.paytm on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.android.cellbroadcastreceiver on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.google.android.webview on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package android.ext.shared on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.android.bluetooth.bthelper.auto_generated_rro_product__ on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.google.android.contactkeys on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.google.android.contacts on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.google.android.syncadapters.contacts on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.android.system.monet.expresso on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.nikgapps.overlay.googleclock on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package org.calyxos.datura on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.themepicker on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.android.systemui.clocks.inflate on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.google.android.calculator on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.adultwebtoon on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package org.voltage.theme.font.manrope on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package com.android.printservice.recommendation on user 0
05-24 20:21:14.494  1434  1434 W PackageSettings: Missing permission state for package app.grapheneos.AppCompatConfig on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package me.jmh.authenticatorpro on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.systemui on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.mangadex on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.kaiscans on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.google.android.gms on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.google.android.ims on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.system.theme.black on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package android.ext.services on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.wifi.resources on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.plumpy.systemui on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.cameraextensions on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.packageinstaller on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.carrierdefaultapp on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.magusmanga on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.gradicon.systemui on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.necroscans on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.font.opsans on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.batoto on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.credentialmanager on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.android on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.font.notoserifsource on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.android on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.proxyhandler on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.launcher3.auto_generated_rro_product__ on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.waterfall on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.intentresolver on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.systemui on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package io.github.muntashirakon.AppManager on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.internal.systemui.navbar.transparent on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.providers.settings.overlay on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.android on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.google.android.apps.photos on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.android on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.managedprovisioning on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.aeinscans on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package io.github.dovecoteescapee.byedpi on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.lorn.systemui on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package com.android.dreams.phototable on user 0
05-24 20:21:14.495  1434  1434 W PackageSettings: Missing permission state for package org.lineageos.settings.doze.auto_generated_rro_product__ on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.networkstack.tethering.mt6893 on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.launcher on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package org.voltage.theme.font.recursive_casual on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package air.kukulive.mailnow on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.looker.droidify on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.android on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.smspush on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.role.notes.enabled on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.berdik.letmedowngrade on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.wallpaper.livepicker on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package org.lineageos.aperture on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.cellbroadcastreceiver.module on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.systemui.clocks.flex on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.apps.tag on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.inputmethod.latin.auto_generated_rro_product__ on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.power.hub.udfps.icons on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.imsserviceentitlement on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.appsearch.apk on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.launcher on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.valirscans on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package org.voltage.theme.font.recursive_linear on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.avoidAppsInCutout on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package org.lineageos.cupida.wifioverlay on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package idm.internet.download.manager.plus on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.plumpy.android on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.arvenscans on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.melody on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.android on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.storagemanager on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.zerodha.kite3 on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.bookmarkprovider on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.fitbit.FitbitMobile on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.aurora.systemui on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.launcher on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package uk.akane.omni on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package org.protonaosp.theme.font.linotte on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.google.android.apps.googlecamera.fishfood on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.voltage.overlay.customization.keyboard.nonavbar on user 0
05-24 20:21:14.496  1434  1434 W PackageSettings: Missing permission state for package com.google.android.apps.turbo on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.enryumanga on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.google.android.safetycore on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.whalemanga on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package app.grapheneos.gmscompat on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.themepicker on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.android.wifi.resources.mt6893 on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package proton.android.pass on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.launcher on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.android.wallpaper on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.nikgapps.overlay.turbo on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.android.vpndialogs on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.goping.user on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.nyxscans on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.google.android.keep on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.android.angle on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.mangareadorg on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.linkbox.plus.android on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.themepicker on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.android.sdksandbox on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.android.wallpaperbackup on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.stevesoltys.seedvault.auto_generated_rro_product__ on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.android.settings.auto_generated_rro_product__ on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.voltageos.Covers on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.android.providers.media.module on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.power.hub.udfps.animations on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package in.swiggy.android on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.android.hotspot2.osulogin on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.solarmtl on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.google.android.gms.location.history on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.intsig.camscanner on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.android.internal.systemui.navbar.gestural on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package co.aospa.sense.settings.overlay on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.themepicker on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.wstxda.viper4android on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.harimanga on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.mangadistrict on user 0
05-24 20:21:14.497  1434  1434 W PackageSettings: Missing permission state for package com.android.bluetoothmidiservice on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.ancient.settingsoverlay.mt6893 on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package org.akanework.gramophone on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.android.permissioncontroller on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.android.storagemanager.auto_generated_rro_product__ on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.zerodha.coin on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.android.dialer.auto_generated_rro_vendor__ on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package app.customerportal.tachyon1 on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.android.imsserviceentitlement.auto_generated_rro_product__ on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.android.phone.auto_generated_rro_product__ on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package android.auto_generated_rro_product__ on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.ezmanga on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.templescan on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.android.dialer.auto_generated_rro_product__ on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.settings on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.themepicker on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.android.ondevicepersonalization.services on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.android.documentsui.overlay on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.anisascans on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.android.captiveportallogin on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.aurora.android on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.android.devicelockcontroller on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.settings on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.tukann.confinedandhorny on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.settings on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.likemanga on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.nikgapps.overlay.wellbeing on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.nikgapps.overlay.dialer on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.google.android.inputmethod.latin on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package org.lineageos.aperture.frameworksbaseoverlay on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.nikgapps.overlay.contacts on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package android.auto_generated_rro_vendor__ on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.android on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for package com.google.android.apps.restore on user 0
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for shared user: android.uid.log
05-24 20:21:14.498  1434  1434 W PackageSettings: Missing permission state for shared user: android.uid.uwb
05-24 20:21:14.626  1434  1434 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-24 20:21:14.634  1434  1434 I PackageManager: /system/apex/com.android.btservices.apex changed; collecting certs
05-24 20:21:14.645  1434  1434 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-24 20:21:14.674  1434  1434 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-24 20:21:14.688  1434  1434 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-24 20:21:14.724  1434  1545 I system_server: Compiler allocated 4688KB to compile com.android.server.pm.ScanResult com.android.server.pm.ScanPackageUtils.scanPackageOnly(com.android.server.pm.ScanRequest, com.android.server.pm.PackageManagerServiceInjector, boolean, long)
05-24 20:21:14.848  1478  1483 E RadioConfig_service: radioConfigService[0] or mRadioConfigIndication is NULL
05-24 20:21:14.883  1625  1625 I HidlServiceManagement: Registered android.hardware.audio@7.1::IDevicesFactory/default
05-24 20:21:14.884  1625  1625 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-24 20:21:14.884  1625  1625 I LegacySupport: Registration complete for android.hardware.audio@7.1::IDevicesFactory/default.
05-24 20:21:14.892  1625  1625 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-24 20:21:14.892  1625  1625 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-24 20:21:14.899  1625  1625 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 20:21:14.914  1241  1241 I LSPosedService: service package is not started, wait 1s.
05-24 20:21:14.932  1632  1632 I auditd  : type=1400 audit(0.0:498): avc:  denied  { execute } for  comm="init" name="vendor.oplus.hardware.olc@2.0-service" dev="dm-3" ino=17 scontext=u:r:init:s0 tcontext=u:object_r:vendor_file:s0 tclass=file permissive=1
05-24 20:21:14.932  1632  1632 I init    : type=1400 audit(0.0:498): avc:  denied  { execute } for  name="vendor.oplus.hardware.olc@2.0-service" dev="dm-3" ino=17 scontext=u:r:init:s0 tcontext=u:object_r:vendor_file:s0 tclass=file permissive=1
05-24 20:21:14.932  1632  1632 I auditd  : type=1400 audit(0.0:499): avc:  denied  { execute_no_trans } for  comm="init" path="/odm/bin/hw/vendor.oplus.hardware.olc@2.0-service" dev="dm-3" ino=17 scontext=u:r:init:s0 tcontext=u:object_r:vendor_file:s0 tclass=file permissive=1
05-24 20:21:14.932  1632  1632 I init    : type=1400 audit(0.0:499): avc:  denied  { execute_no_trans } for  path="/odm/bin/hw/vendor.oplus.hardware.olc@2.0-service" dev="dm-3" ino=17 scontext=u:r:init:s0 tcontext=u:object_r:vendor_file:s0 tclass=file permissive=1
05-24 20:21:14.936  1632  1632 I auditd  : type=1400 audit(0.0:500): avc:  denied  { execute } for  comm="vendor.oplus.ha" path="/vendor/lib64/libbase.so" dev="dm-1" ino=1683 scontext=u:r:init:s0 tcontext=u:object_r:same_process_hal_file:s0 tclass=file permissive=1
05-24 20:21:14.936  1632  1632 I vendor.oplus.ha: type=1400 audit(0.0:500): avc:  denied  { execute } for  path="/vendor/lib64/libbase.so" dev="dm-1" ino=1683 scontext=u:r:init:s0 tcontext=u:object_r:same_process_hal_file:s0 tclass=file permissive=1
05-24 20:21:14.940  1632  1632 I auditd  : type=1400 audit(0.0:501): avc:  denied  { read } for  comm="vendor.oplus.ha" name="vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:14.940  1632  1632 I vendor.oplus.ha: type=1400 audit(0.0:501): avc:  denied  { read } for  name="vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:14.940  1632  1632 I auditd  : type=1400 audit(0.0:502): avc:  denied  { write } for  comm="vendor.oplus.ha" name="vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:14.940  1632  1632 I vendor.oplus.ha: type=1400 audit(0.0:502): avc:  denied  { write } for  name="vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:14.940  1632  1632 I auditd  : type=1400 audit(0.0:503): avc:  denied  { open } for  comm="vendor.oplus.ha" path="/dev/binderfs/vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:14.940  1632  1632 I vendor.oplus.ha: type=1400 audit(0.0:503): avc:  denied  { open } for  path="/dev/binderfs/vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:14.940  1632  1632 I auditd  : type=1400 audit(0.0:504): avc:  denied  { ioctl } for  comm="vendor.oplus.ha" path="/dev/binderfs/vndbinder" dev="binder" ino=6 ioctlcmd=0x6209 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:14.940  1632  1632 I vendor.oplus.ha: type=1400 audit(0.0:504): avc:  denied  { ioctl } for  path="/dev/binderfs/vndbinder" dev="binder" ino=6 ioctlcmd=0x6209 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:14.940  1632  1632 I auditd  : type=1400 audit(0.0:505): avc:  denied  { map } for  comm="vendor.oplus.ha" path="/dev/binderfs/vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:14.940  1632  1632 I vendor.oplus.ha: type=1400 audit(0.0:505): avc:  denied  { map } for  path="/dev/binderfs/vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:14.940  1632  1632 I auditd  : type=1400 audit(0.0:506): avc:  denied  { create } for  comm="vendor.oplus.ha" scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:14.940  1632  1632 I vendor.oplus.ha: type=1400 audit(0.0:506): avc:  denied  { create } for  scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:14.940  1632  1632 I auditd  : type=1400 audit(0.0:507): avc:  denied  { bind } for  comm="vendor.oplus.ha" scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:14.940  1632  1632 I vendor.oplus.ha: type=1400 audit(0.0:507): avc:  denied  { bind } for  scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:14.940  1632  1632 I auditd  : type=1400 audit(0.0:508): avc:  denied  { write } for  comm="vendor.oplus.ha" scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:14.940  1632  1632 I vendor.oplus.ha: type=1400 audit(0.0:508): avc:  denied  { write } for  scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:14.940  1632  1632 I auditd  : type=1400 audit(0.0:509): avc:  denied  { read } for  comm="vendor.oplus.ha" scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:14.940  1632  1632 I vendor.oplus.ha: type=1400 audit(0.0:509): avc:  denied  { read } for  scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:14.941   575   575 I hwservicemanager: getTransport: Cannot find entry vendor.oplus.hardware.olc@2.0::IOplusLogCore/default in either framework or device VINTF manifest.
05-24 20:21:14.940  1632  1632 I auditd  : type=1400 audit(0.0:510): avc:  denied  { call } for  comm="vendor.oplus.ha" scontext=u:r:init:s0 tcontext=u:r:hwservicemanager:s0 tclass=binder permissive=1
05-24 20:21:14.940  1632  1632 I vendor.oplus.ha: type=1400 audit(0.0:510): avc:  denied  { call } for  scontext=u:r:init:s0 tcontext=u:r:hwservicemanager:s0 tclass=binder permissive=1
05-24 20:21:14.942  1632  1632 E HidlServiceManagement: Service vendor.oplus.hardware.olc@2.0::IOplusLogCore/default must be in VINTF manifest in order to register/get.
05-24 20:21:14.942  1632  1632 E OLC_HAL : registerHidlService failed. 
05-24 20:21:14.960  1434  1623 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 20:21:14.961  1434  1623 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 20:21:14.961  1434  1623 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 20:21:14.985  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:14.985  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:14.985  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:14.985  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:14.985  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:14.985  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:14.985  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:14.985  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:14.986  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:14.986  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:14.986  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:14.986  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:14.986  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:14.986  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:14.987  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:14.987  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:14.987  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:14.987  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:14.991  1434  1623 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 20:21:14.998  1434  1623 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 20:21:14.998  1434  1623 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 20:21:15.037  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.037  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.039  1636  1636 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 20:21:15.040  1434  1624 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.CAMERA in package: com.mediatek.engineermode at: Binary XML file line #30
05-24 20:21:15.041  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 20:21:15.042  1636  1636 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 20:21:15.042   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 20:21:15.056  1636  1636 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 20:21:15.057   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 20:21:15.059  1636  1636 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 20:21:15.063  1625  1625 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x989e3cc3)
05-24 20:21:15.065  1434  1553 W android.permission.PermissionManager: Missing ActivityManager; assuming 1041 does not hold android.permission.UPDATE_DEVICE_STATS
05-24 20:21:15.065  1434  1553 W Binder  : java.lang.SecurityException: Access denied, requires: android.permission.UPDATE_DEVICE_STATS
05-24 20:21:15.065  1434  1553 W Binder  : 	at android.os.PermissionEnforcer.enforcePermission(PermissionEnforcer.java:146)
05-24 20:21:15.065  1434  1553 W Binder  : 	at com.android.internal.app.IBatteryStats$Stub.noteResetAudio_enforcePermission(IBatteryStats.java:3472)
05-24 20:21:15.065  1434  1553 W Binder  : 	at com.android.server.am.BatteryStatsService.noteResetAudio(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:1)
05-24 20:21:15.068  1636  1636 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-24 20:21:15.068  1625  1625 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x10efdcb1)
05-24 20:21:15.070  1625  1625 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 20:21:15.072  1625  1625 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x29122193)
05-24 20:21:15.073  1625  1625 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x54724869)
05-24 20:21:15.077  1625  1625 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-24 20:21:15.078   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 20:21:15.078  1625  1625 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-24 20:21:15.079   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 20:21:15.079  1625  1625 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-24 20:21:15.079   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 20:21:15.080  1625  1625 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-24 20:21:15.080  1625  1625 W audiohalservice: Could not register Bluetooth Audio API
05-24 20:21:15.081   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-24 20:21:15.081  1625  1625 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-24 20:21:15.081  1625  1625 W audiohalservice: Could not register Bluetooth Audio Offload API
05-24 20:21:15.097  1625  1625 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-24 20:21:15.098  1625  1625 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-24 20:21:15.098  1625  1625 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-24 20:21:15.102  1636  1636 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-24 20:21:15.102  1636  1636 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-24 20:21:15.118  1434  1624 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.SATELLITE_COMMUNICATION in package: com.android.shell at: Binary XML file line #775
05-24 20:21:15.119  1625  1625 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-24 20:21:15.119  1625  1625 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-24 20:21:15.121  1434  1624 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE in package: com.android.shell at: Binary XML file line #874
05-24 20:21:15.121  1434  1624 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE_SYSTEM_EXEMPTED in package: com.android.shell at: Binary XML file line #875
05-24 20:21:15.130  1625  1625 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 20:21:15.164  1434  1624 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.POST_NOTIFICATIONS in package: com.android.phone at: Binary XML file line #171
05-24 20:21:15.188  1098  1098 I mtkcam-devicemgr: [initialize] -
05-24 20:21:15.188  1098  1098 I mtkcam-camprovider: [initialize] +
05-24 20:21:15.188  1098  1098 I mtkcam-camprovider: [initialize] -
05-24 20:21:15.191  1098  1098 I HidlServiceManagement: Registered android.hardware.camera.provider@2.6::ICameraProvider/internal/0
05-24 20:21:15.197  1085  1234 I CameraService: onDeviceStatusChanged: Status changed for cameraId=4, newStatus=1
05-24 20:21:15.197  1085  1234 I CameraService: onDeviceStatusChanged: Unknown camera ID 4, a new camera is added
05-24 20:21:15.197  1085  1234 I CameraService: onDeviceStatusChanged: Status changed for cameraId=3, newStatus=1
05-24 20:21:15.197  1085  1234 I CameraService: onDeviceStatusChanged: Unknown camera ID 3, a new camera is added
05-24 20:21:15.197  1085  1234 I CameraService: onDeviceStatusChanged: Status changed for cameraId=2, newStatus=1
05-24 20:21:15.197  1085  1234 I CameraService: onDeviceStatusChanged: Unknown camera ID 2, a new camera is added
05-24 20:21:15.197  1085  1234 I CameraService: onDeviceStatusChanged: Status changed for cameraId=1, newStatus=1
05-24 20:21:15.197  1085  1234 I CameraService: onDeviceStatusChanged: Unknown camera ID 1, a new camera is added
05-24 20:21:15.197  1085  1234 I CameraService: onDeviceStatusChanged: Status changed for cameraId=0, newStatus=1
05-24 20:21:15.197  1085  1234 I CameraService: onDeviceStatusChanged: Unknown camera ID 0, a new camera is added
05-24 20:21:15.202  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.202  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.204  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.205  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.205  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.205  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.206  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.206  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.207  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.207  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.207  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.207  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.207  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.207  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.208  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.208  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.208  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.208  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.213  1098  1098 I HidlServiceManagement: Registered vendor.mediatek.hardware.camera.isphal@1.0::IISPModule/internal/0
05-24 20:21:15.219  1098  1098 I MtkCam/BGService: IBGService  into HIDL_FETCH_IBGService
05-24 20:21:15.221  1098  1098 I HidlServiceManagement: Registered vendor.mediatek.hardware.camera.bgservice@1.1::IBGService/internal/0
05-24 20:21:15.222  1098  1098 I LegacySupport: Registration complete for vendor.mediatek.hardware.camera.bgservice@1.1::IBGService/internal/0.
05-24 20:21:15.236  1098  1098 I HidlServiceManagement: Registered vendor.mediatek.hardware.camera.atms@1.0::IATMs/default
05-24 20:21:15.257  1084  1110 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 20:21:15.257  1084  1110 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:5
05-24 20:21:15.260  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.260  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 20:21:15.264  1625  1625 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 20:21:15.287  1625  1625 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xba570995)
05-24 20:21:15.287  1625  1653 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-24 20:21:15.288  1625  1653 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0xba570995)
05-24 20:21:15.290  1625  1653 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0xba570995)
05-24 20:21:15.336  1434  1434 W PackageManager: Failed to scan /product/priv-app/CarrierServices: Package com.google.android.ims at /product/priv-app/CarrierServices ignored: updated version 31144015 better than this 30939330
05-24 20:21:15.344  1625  1625 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-24 20:21:15.349  1625  1625 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 20:21:15.368  1625  1625 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-24 20:21:15.383  1625  1625 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 20:21:15.383  1625  1625 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 20:21:15.383  1625  1625 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 20:21:15.388  1625  1625 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 20:21:15.388  1625  1625 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 20:21:15.389  1625  1625 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-24 20:21:15.389  1091  1127 W ServiceManagerCppClient: Service package_native didn't start. Returning NULL
05-24 20:21:15.390  1091  1127 E ServiceUtilities: getCachedInfo: Cannot find package_native
05-24 20:21:15.393  1625  1625 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-24 20:21:15.393  1625  1625 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-24 20:21:15.394  1636  1636 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 20:21:15.394  1636  1636 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 20:21:15.396  1625  1631 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-24 20:21:15.396  1625  1631 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-24 20:21:15.397  1625  1631 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 20:21:15.397  1636  1636 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-24 20:21:15.399  1636  1636 I AudioFlinger: openOutput() this 0xb4000074457cd900, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-24 20:21:15.400  1636  1636 I AudioFlinger: operator(): Using 3000 ms as standby time
05-24 20:21:15.401  1636  1636 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 20:21:15.401  1636  1636 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 20:21:15.402   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 20:21:15.403  1636  1636 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 20:21:15.411  1636  1669 I AudioFlinger: AudioFlinger's thread 0xb4000075b53df760 tid=1669 ready to run
05-24 20:21:15.411  1636  1669 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 20:21:15.412  1636  1636 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 20:21:15.415  1636  1669 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.416  1636  1636 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 20:21:15.416  1636  1636 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 20:21:15.416  1636  1636 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 20:21:15.416  1636  1636 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 20:21:15.416  1636  1636 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 20:21:15.416  1636  1636 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 20:21:15.416  1636  1636 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 20:21:15.416  1636  1636 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 20:21:15.416  1636  1636 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 20:21:15.416  1636  1636 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 20:21:15.416  1636  1636 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 20:21:15.416  1636  1636 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 20:21:15.416  1636  1636 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 20:21:15.416  1636  1636 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 20:21:15.416  1636  1636 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 20:21:15.417  1636  1636 W AudioFlinger: moveEffects() AUDIO_SESSION_OUTPUT_MIX not found in orphans, checking other mix
05-24 20:21:15.417  1636  1636 W AudioFlinger: moveEffects() bad srcIo 0
05-24 20:21:15.418  1636  1636 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 20:21:15.440  1625  1630 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x2, output_devices == current_output_devices(0x00000002), return
05-24 20:21:15.443  1636  1636 I AudioFlinger: openOutput() this 0xb4000074457cd900, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x8
05-24 20:21:15.444  1625  1630 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 20:21:15.445  1636  1636 I AudioFlinger: HAL output buffer size 2048 frames, normal sink buffer size 2048 frames
05-24 20:21:15.446  1636  1672 I AudioFlinger: AudioFlinger's thread 0xb4000075b5247760 tid=1672 ready to run
05-24 20:21:15.446  1636  1672 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.447  1636  1672 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.448  1636  1636 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 20:21:15.499  1093  1093 W ServiceManagerCppClient: Service package_native didn't start. Returning NULL
05-24 20:21:15.499  1093  1093 E storaged: getService package_native failed
05-24 20:21:15.506  1093  1674 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder'...
05-24 20:21:15.512  1625  1630 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x8, output_devices == current_output_devices(0x00000002), return
05-24 20:21:15.514  1636  1636 I AudioFlinger: openOutput() this 0xb4000074457cd900, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x4
05-24 20:21:15.514  1625  1630 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 20:21:15.515  1636  1636 I AudioFlinger: HAL output buffer size 256 frames, normal sink buffer size 768 frames
05-24 20:21:15.515  1636  1677 I AudioFlinger: AudioFlinger's thread 0xb4000075b518e760 tid=1677 ready to run
05-24 20:21:15.515  1636  1677 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.516  1636  1677 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.517  1636  1636 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 20:21:15.539  1625  1630 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x4, output_devices == current_output_devices(0x00000002), return
05-24 20:21:15.540  1636  1636 I AudioFlinger: openOutput() this 0xb4000074457cd900, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x8000
05-24 20:21:15.541  1625  1630 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 20:21:15.542  1636  1636 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 20:21:15.543  1636  1678 I AudioFlinger: AudioFlinger's thread 0xb4000075b111d760 tid=1678 ready to run
05-24 20:21:15.543  1636  1678 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.543  1636  1678 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.546  1636  1636 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 20:21:15.578  1625  1630 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x8000, output_devices == current_output_devices(0x00000002), return
05-24 20:21:15.581  1636  1636 I AudioFlinger: openOutput() this 0xb4000074457cd900, module 10 Device AUDIO_DEVICE_OUT_TELEPHONY_TX, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x10000
05-24 20:21:15.581  1625  1630 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 20:21:15.582  1636  1636 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 20:21:15.583  1636  1679 I AudioFlinger: AudioFlinger's thread 0xb4000075b1021760 tid=1679 ready to run
05-24 20:21:15.584  1636  1679 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.586  1636  1679 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.619  1636  1680 I AudioFlinger: AudioFlinger's thread 0xb4000075b4e01a78 tid=1680 ready to run
05-24 20:21:15.619  1636  1680 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.621  1636  1680 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.625  1636  1682 I AudioFlinger: AudioFlinger's thread 0xb4000075b4e01a78 tid=1682 ready to run
05-24 20:21:15.625  1636  1682 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.627  1636  1682 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.630  1636  1684 I AudioFlinger: AudioFlinger's thread 0xb4000075b4e01a78 tid=1684 ready to run
05-24 20:21:15.630  1636  1684 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.631  1636  1684 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.634  1636  1686 I AudioFlinger: AudioFlinger's thread 0xb4000075b4e01a78 tid=1686 ready to run
05-24 20:21:15.635  1636  1686 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.636  1636  1686 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.642  1636  1689 I AudioFlinger: AudioFlinger's thread 0xb4000075b4e01a78 tid=1689 ready to run
05-24 20:21:15.642  1636  1689 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.642  1636  1689 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.643  1625  1631 E DevicesFactoryHAL: loadAudioInterface couldn't load audio hw module audio.binaural_record (No such file or directory)
05-24 20:21:15.644  1636  1636 E AudioFlinger: loadHwModule() error -22 loading module binaural_record
05-24 20:21:15.644  1636  1636 W APM_AudioPolicyManager: could not load HW module binaural_record
05-24 20:21:15.649  1636  1636 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 20:21:15.649  1636  1636 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 20:21:15.650  1636  1636 I AudioFlinger: loadHwModule() Loaded bluetooth audio interface, handle 18
05-24 20:21:15.650  1625  1631 E DevicesFactoryHAL: loadAudioInterface couldn't load audio hw module audio.a2dp (No such file or directory)
05-24 20:21:15.650  1636  1636 E AudioFlinger: loadHwModule() error -22 loading module a2dp
05-24 20:21:15.650  1636  1636 W APM_AudioPolicyManager: could not load HW module a2dp
05-24 20:21:15.650  1625  1631 E DevicesFactoryHAL: loadAudioInterface couldn't load audio hw module audio.usb (No such file or directory)
05-24 20:21:15.650  1636  1636 E AudioFlinger: loadHwModule() error -22 loading module usb
05-24 20:21:15.651  1636  1636 W APM_AudioPolicyManager: could not load HW module usb
05-24 20:21:15.651  1625  1631 E DevicesFactoryHAL: loadAudioInterface couldn't load audio hw module audio.virtual (No such file or directory)
05-24 20:21:15.651  1636  1636 E AudioFlinger: loadHwModule() error -22 loading module virtual
05-24 20:21:15.651  1636  1636 W APM_AudioPolicyManager: could not load HW module virtual
05-24 20:21:15.654  1625  1631 I r_submix: adev_open(name=audio_hw_if)
05-24 20:21:15.655  1636  1636 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 20:21:15.655  1636  1636 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 20:21:15.655  1625  1631 I r_submix: adev_init_check()
05-24 20:21:15.656  1636  1636 I AudioFlinger: loadHwModule() Loaded r_submix audio interface, handle 26
05-24 20:21:15.659  1636  1692 I AudioFlinger: AudioFlinger's thread 0xb4000075b4e01a78 tid=1692 ready to run
05-24 20:21:15.659  1636  1692 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.660  1636  1692 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:15.662  1636  1636 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 20:21:15.662   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 20:21:15.663  1636  1636 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 20:21:15.915  1241  1241 I LSPosedService: service package is not started, wait 1s.
05-24 20:21:16.064  1434  1621 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.CONFIGURE_WIFI_DISPLAY in package: com.android.systemui at: Binary XML file line #174
05-24 20:21:16.065  1434  1621 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.GET_RUNTIME_PERMISSIONS in package: com.android.systemui at: Binary XML file line #252
05-24 20:21:16.067  1434  1621 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.READ_DEVICE_CONFIG in package: com.android.systemui at: Binary XML file line #372
05-24 20:21:16.067  1434  1621 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.MODIFY_AUDIO_SETTINGS in package: com.android.systemui at: Binary XML file line #405
05-24 20:21:16.068  1434  1621 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FORCE_STOP_PACKAGES in package: com.android.systemui at: Binary XML file line #426
05-24 20:21:16.227  1434  1434 I ApexManager: Registering com.android.bluetooth as apk-in-apex of com.android.btservices
05-24 20:21:16.239  1434  1434 I ApexManager: Registering com.android.safetycenter.resources as apk-in-apex of com.android.permission
05-24 20:21:16.258  1084  1110 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 20:21:16.258  1084  1110 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:6
05-24 20:21:16.290  1434  1434 I ApexManager: Registering com.android.permissioncontroller as apk-in-apex of com.android.permission
05-24 20:21:16.313  1434  1434 I ApexManager: Registering com.android.ondevicepersonalization.services as apk-in-apex of com.android.ondevicepersonalization
05-24 20:21:16.323  1434  1434 I ApexManager: Registering com.android.federatedcompute.services as apk-in-apex of com.android.ondevicepersonalization
05-24 20:21:16.355  1434  1434 I ApexManager: Registering com.android.cellbroadcastservice as apk-in-apex of com.android.cellbroadcast
05-24 20:21:16.473  1434  1434 I ApexManager: Registering android.ext.services as apk-in-apex of com.android.extservices
05-24 20:21:16.667  1636  1636 W ServiceManagerCppClient: Waited one second for activity (is service started? Number of threads started in the threadpool: 16. Are binder threads started and available?)
05-24 20:21:16.812  1434  1621 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.RECEIVE_BOOT_COMPLETED in package: com.android.adservices.api at: Binary XML file line #159
05-24 20:21:16.817  1434  1434 I ApexManager: Registering com.android.adservices.api as apk-in-apex of com.android.adservices
05-24 20:21:16.827  1434  1434 I ApexManager: Registering com.android.sdksandbox as apk-in-apex of com.android.adservices
05-24 20:21:16.898  1434  1621 W PackageParsing: com.android.healthconnect.controller defines a background permission. Only the 'android' packages can do that.
05-24 20:21:16.898  1434  1621 W PackageParsing: com.android.healthconnect.controller defines a background permission. Only the 'android' packages can do that.
05-24 20:21:16.898  1434  1621 W PackageParsing: com.android.healthconnect.controller defines a background permission. Only the 'android' packages can do that.
05-24 20:21:16.916  1241  1241 I LSPosedService: service package is not started, wait 1s.
05-24 20:21:16.928  1434  1624 W PackageParsing: Unknown element under <manifest>: meta-data at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #102
05-24 20:21:16.928  1434  1624 W PackageParsing: Unknown element under <manifest>: provider at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #106
05-24 20:21:16.928  1434  1624 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #116
05-24 20:21:16.928  1434  1624 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #122
05-24 20:21:16.928  1434  1624 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #129
05-24 20:21:16.928  1434  1624 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #135
05-24 20:21:16.928  1434  1624 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #140
05-24 20:21:16.928  1434  1624 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #147
05-24 20:21:16.928  1434  1624 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #153
05-24 20:21:16.928  1434  1624 W PackageParsing: Unknown element under <manifest>: receiver at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #158
05-24 20:21:16.928  1434  1624 W PackageParsing: Unknown element under <manifest>: service at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #166
05-24 20:21:16.928  1434  1624 W PackageParsing: Unknown element under <manifest>: provider at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #168
05-24 20:21:16.928  1434  1624 W PackageParsing: Unknown element under <manifest>: receiver at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #177
05-24 20:21:16.933  1434  1434 I ApexManager: Registering com.android.nfc as apk-in-apex of com.android.nfcservices
05-24 20:21:17.152  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.MANAGE_OWN_CALLS in package: com.truecaller at: Binary XML file line #141
05-24 20:21:17.202  1434  1624 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.READ_EXTERNAL_STORAGE in package: ellipi.messenger at: Binary XML file line #111
05-24 20:21:17.203  1434  1624 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WRITE_EXTERNAL_STORAGE in package: ellipi.messenger at: Binary XML file line #179
05-24 20:21:17.255  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.INTERNET in package: com.intsig.camscanner at: Binary XML file line #34
05-24 20:21:17.258  1084  1110 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 20:21:17.258  1084  1110 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:7
05-24 20:21:17.363  1434  1623 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #9
05-24 20:21:17.363  1434  1623 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #10
05-24 20:21:17.363  1434  1623 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #11
05-24 20:21:17.363  1434  1623 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #12
05-24 20:21:17.668  1636  1636 W ServiceManagerCppClient: Waited one second for activity (is service started? Number of threads started in the threadpool: 16. Are binder threads started and available?)
05-24 20:21:17.917  1241  1241 I LSPosedService: service package is not started, wait 1s.
05-24 20:21:18.051   884   891 W ServiceManagerCppClient: Service statscompanion didn't start. Returning NULL
05-24 20:21:18.051   884   891 E statsd  : Uid 1000 does not have the android.permission.REGISTER_STATS_PULL_ATOM permission when registering atom 10205 (-1)
05-24 20:21:18.258  1084  1110 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 20:21:18.258  1084  1110 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:8
05-24 20:21:18.342  1125  1577 W mtk_agpsd: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.system.suspend.ISystemSuspend/default
05-24 20:21:18.549  1434  1622 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 20:21:18.550  1434  1622 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 20:21:18.551  1434  1622 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 20:21:18.553  1434  1622 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #34
05-24 20:21:18.554  1434  1622 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 20:21:18.555  1434  1622 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 20:21:18.556  1434  1622 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 20:21:18.671  1636  1636 W ServiceManagerCppClient: Waited one second for activity (is service started? Number of threads started in the threadpool: 16. Are binder threads started and available?)
05-24 20:21:18.859  1434  1623 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.USE_FINGERPRINT in package: org.mozilla.focus at: Binary XML file line #73
05-24 20:21:18.884  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.ACCESS_NETWORK_STATE in package: com.google.android.gm at: Binary XML file line #311
05-24 20:21:18.884  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE in package: com.google.android.gm at: Binary XML file line #314
05-24 20:21:18.884  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE_DATA_SYNC in package: com.google.android.gm at: Binary XML file line #315
05-24 20:21:18.885  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.INTERNET in package: com.google.android.gm at: Binary XML file line #316
05-24 20:21:18.885  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.RECEIVE_BOOT_COMPLETED in package: com.google.android.gm at: Binary XML file line #317
05-24 20:21:18.885  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.VIBRATE in package: com.google.android.gm at: Binary XML file line #318
05-24 20:21:18.885  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WAKE_LOCK in package: com.google.android.gm at: Binary XML file line #319
05-24 20:21:18.885  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WRITE_EXTERNAL_STORAGE in package: com.google.android.gm at: Binary XML file line #321
05-24 20:21:18.885  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.POST_NOTIFICATIONS in package: com.google.android.gm at: Binary XML file line #322
05-24 20:21:18.885  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.c2dm.permission.RECEIVE in package: com.google.android.gm at: Binary XML file line #324
05-24 20:21:18.885  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.GET_PACKAGE_SIZE in package: com.google.android.gm at: Binary XML file line #325
05-24 20:21:18.885  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.providers.gsf.permission.READ_GSERVICES in package: com.google.android.gm at: Binary XML file line #326
05-24 20:21:18.885  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.providers.gsf.permission.READ_GSERVICES in package: com.google.android.gm at: Binary XML file line #331
05-24 20:21:18.885  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.GET_PACKAGE_SIZE in package: com.google.android.gm at: Binary XML file line #332
05-24 20:21:18.886  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.ACCESS_NETWORK_STATE in package: com.google.android.gm at: Binary XML file line #334
05-24 20:21:18.886  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.CAMERA in package: com.google.android.gm at: Binary XML file line #345
05-24 20:21:18.886  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE in package: com.google.android.gm at: Binary XML file line #360
05-24 20:21:18.886  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.INTERNET in package: com.google.android.gm at: Binary XML file line #361
05-24 20:21:18.886  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.RECORD_AUDIO in package: com.google.android.gm at: Binary XML file line #363
05-24 20:21:18.886  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WAKE_LOCK in package: com.google.android.gm at: Binary XML file line #364
05-24 20:21:18.886  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.POST_NOTIFICATIONS in package: com.google.android.gm at: Binary XML file line #373
05-24 20:21:18.887  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE_DATA_SYNC in package: com.google.android.gm at: Binary XML file line #379
05-24 20:21:18.887  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.gm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION in package: com.google.android.gm at: Binary XML file line #385
05-24 20:21:18.887  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.VIBRATE in package: com.google.android.gm at: Binary XML file line #386
05-24 20:21:18.887  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.READ_CONTACTS in package: com.google.android.gm at: Binary XML file line #387
05-24 20:21:18.887  1434  1622 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.hangouts.START_HANGOUT in package: com.google.android.gm at: Binary XML file line #388
05-24 20:21:18.919  1241  1241 I LSPosedService: service package is not started, wait 1s.
05-24 20:21:19.258  1084  1110 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 20:21:19.258  1084  1110 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:9
05-24 20:21:19.672  1636  1636 W ServiceManagerCppClient: Waited one second for activity (is service started? Number of threads started in the threadpool: 16. Are binder threads started and available?)
05-24 20:21:19.920  1241  1241 I LSPosedService: service package is not started, wait 1s.
05-24 20:21:19.940  1747  1747 I auditd  : type=1400 audit(0.0:511): avc:  denied  { execute } for  comm="init" name="vendor.oplus.hardware.olc@2.0-service" dev="dm-3" ino=17 scontext=u:r:init:s0 tcontext=u:object_r:vendor_file:s0 tclass=file permissive=1
05-24 20:21:19.940  1747  1747 I init    : type=1400 audit(0.0:511): avc:  denied  { execute } for  name="vendor.oplus.hardware.olc@2.0-service" dev="dm-3" ino=17 scontext=u:r:init:s0 tcontext=u:object_r:vendor_file:s0 tclass=file permissive=1
05-24 20:21:19.940  1747  1747 I auditd  : type=1400 audit(0.0:512): avc:  denied  { execute_no_trans } for  comm="init" path="/odm/bin/hw/vendor.oplus.hardware.olc@2.0-service" dev="dm-3" ino=17 scontext=u:r:init:s0 tcontext=u:object_r:vendor_file:s0 tclass=file permissive=1
05-24 20:21:19.940  1747  1747 I init    : type=1400 audit(0.0:512): avc:  denied  { execute_no_trans } for  path="/odm/bin/hw/vendor.oplus.hardware.olc@2.0-service" dev="dm-3" ino=17 scontext=u:r:init:s0 tcontext=u:object_r:vendor_file:s0 tclass=file permissive=1
05-24 20:21:19.960  1747  1747 I auditd  : type=1400 audit(0.0:513): avc:  denied  { execute } for  comm="vendor.oplus.ha" path="/vendor/lib64/libhidlbase.so" dev="dm-1" ino=1840 scontext=u:r:init:s0 tcontext=u:object_r:same_process_hal_file:s0 tclass=file permissive=1
05-24 20:21:19.960  1747  1747 I vendor.oplus.ha: type=1400 audit(0.0:513): avc:  denied  { execute } for  path="/vendor/lib64/libhidlbase.so" dev="dm-1" ino=1840 scontext=u:r:init:s0 tcontext=u:object_r:same_process_hal_file:s0 tclass=file permissive=1
05-24 20:21:19.972  1747  1747 I auditd  : type=1400 audit(0.0:514): avc:  denied  { call } for  comm="vendor.oplus.ha" scontext=u:r:init:s0 tcontext=u:r:hwservicemanager:s0 tclass=binder permissive=1
05-24 20:21:19.972  1747  1747 I vendor.oplus.ha: type=1400 audit(0.0:514): avc:  denied  { call } for  scontext=u:r:init:s0 tcontext=u:r:hwservicemanager:s0 tclass=binder permissive=1
05-24 20:21:19.975   575   575 I hwservicemanager: getTransport: Cannot find entry vendor.oplus.hardware.olc@2.0::IOplusLogCore/default in either framework or device VINTF manifest.
05-24 20:21:19.976  1747  1747 E HidlServiceManagement: Service vendor.oplus.hardware.olc@2.0::IOplusLogCore/default must be in VINTF manifest in order to register/get.
05-24 20:21:19.976  1747  1747 E OLC_HAL : registerHidlService failed. 
05-24 20:21:20.548  1093  1674 W ServiceManagerCppClient: Service package_native didn't start. Returning NULL
05-24 20:21:20.548  1093  1674 E storaged: getService package_native failed
05-24 20:21:20.638  1434  1434 W AppIdPermissionPolicy: Ignoring permission com.google.android.gtalkservice.permission.GTALK_SERVICE declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-24 20:21:20.638  1434  1434 W AppIdPermissionPolicy: Ignoring permission com.android.vending.INTENT_VENDING_ONLY declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-24 20:21:20.638  1434  1434 W AppIdPermissionPolicy: Ignoring permission com.google.android.providers.settings.permission.WRITE_GSETTINGS declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-24 20:21:20.638  1434  1434 W AppIdPermissionPolicy: Ignoring permission com.google.android.providers.gsf.permission.WRITE_GSERVICES declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-24 20:21:20.639  1434  1434 W AppIdPermissionPolicy: Ignoring permission lineageos.permission.MANAGE_REMOTE_PREFERENCES declared in system package com.android.settings: already declared in another system package io.chaldeaprjkt.gamespace
05-24 20:21:20.641  1434  1434 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_TOPICS declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 20:21:20.641  1434  1434 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_ATTRIBUTION declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 20:21:20.641  1434  1434 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 20:21:20.641  1434  1434 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_AD_SELECTION declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 20:21:20.642  1434  1434 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_PROTECTED_SIGNALS declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 20:21:20.642  1434  1434 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_AD_ID declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 20:21:20.673  1636  1636 W ServiceManagerCppClient: Waited one second for activity (is service started? Number of threads started in the threadpool: 16. Are binder threads started and available?)
05-24 20:21:20.764  1434  1434 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.adservices/appid
05-24 20:21:20.766  1434  1434 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.permission/appid
05-24 20:21:20.771  1434  1434 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.btservices/appid
05-24 20:21:20.783  1434  1434 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.extservices/appid
05-24 20:21:20.788  1434  1434 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.nfcservices/appid
05-24 20:21:20.912  1434  1434 I SystemServiceManager: Starting com.android.server.pm.UserManagerService$LifeCycle
05-24 20:21:20.946  1241  1241 E LSPosedService: failed to change feature flags
05-24 20:21:20.946  1241  1241 E LSPosedService: java.lang.NoSuchFieldException: No field systemui_is_cached in class Landroid/app/FeatureFlagsImpl; (declaration of 'android.app.FeatureFlagsImpl' appears in /system/framework/framework.jar)
05-24 20:21:20.946  1241  1241 E LSPosedService: 	at java.lang.Class.getDeclaredField(Native Method)
05-24 20:21:20.946  1241  1241 E LSPosedService: 	at org.lsposed.lspd.Main.main(Unknown Source:411)
05-24 20:21:20.946  1241  1241 E LSPosedService: 	at com.android.internal.os.RuntimeInit.nativeFinishInit(Native Method)
05-24 20:21:20.946  1241  1241 E LSPosedService: 	at com.android.internal.os.RuntimeInit.main(RuntimeInit.java:375)
05-24 20:21:20.949  1434  1757 W PackageManager: Skipping preparing app data for com.android.adservices
05-24 20:21:20.950  1434  1757 W PackageManager: Skipping preparing app data for com.android.permission
05-24 20:21:20.950  1241  1241 I LSPosedService: sent service to bridge
05-24 20:21:20.950  1434  1757 W PackageManager: Skipping preparing app data for com.android.btservices
05-24 20:21:20.951  1434  1757 W PackageManager: Skipping preparing app data for com.android.extservices
05-24 20:21:20.951  1434  1757 W PackageManager: Skipping preparing app data for com.android.nfcservices
05-24 20:21:21.125  1091  1665 W AudioAnalytics: onAudioServerStart: (key=audio.policy) AudioPolicy ctor, loadTimeMs:6057.529297
05-24 20:21:21.126  1636  1636 I audioserver: main: initialization done in 6089.272 ms, joining thread pool
05-24 20:21:21.126  1434  1434 I SystemServiceManager: Starting com.android.server.sensors.SensorService
05-24 20:21:21.128  1434  1434 I SystemServiceManager: Starting com.android.server.SystemConfigService
05-24 20:21:21.130  1434  1434 I SystemServiceManager: Starting com.android.server.BatteryService
05-24 20:21:21.131   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.sensors@2.1::ISensors/default in either framework or device VINTF manifest.
05-24 20:21:21.143  1434  1767 W SensorService: lsm6dso ACCELEROMETER's max range 78.453201293945 is not a multiple of the resolution 0.001200000057 - updated to 78.453605651855
05-24 20:21:21.143  1434  1767 I SensorService: lsm6dso ACCELEROMETER's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.143  1434  1767 W SensorService: mmc5603 MAGNETOMETER's max range 4912.000000000000 is not a multiple of the resolution 0.150000005960 - updated to 4912.050292968750
05-24 20:21:21.143  1434  1767 I SensorService: mmc5603 MAGNETOMETER's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.143  1434  1767 W SensorService: lsm6dso GYROSCOPE's max range 34.906600952148 is not a multiple of the resolution 0.001099999994 - updated to 34.906299591064
05-24 20:21:21.143  1434  1767 I SensorService: lsm6dso GYROSCOPE's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.143  1434  1767 I SensorService: tcs3701 PROXIMITY's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.143  1434  1767 W SensorService: UNCALI_MAG's max range 4912.000000000000 is not a multiple of the resolution 0.150000005960 - updated to 4912.050292968750
05-24 20:21:21.143  1434  1767 I SensorService: UNCALI_MAG's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.143  1434  1767 W SensorService: UNCALI_GYRO's max range 34.906600952148 is not a multiple of the resolution 0.001099999994 - updated to 34.906299591064
05-24 20:21:21.143  1434  1767 I SensorService: UNCALI_GYRO's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.143  1434  1767 I SensorService: SIGNIFICANT_MOTION's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.143  1434  1767 I SensorService: STEP_DETECTOR's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.143  1434  1767 I SensorService: STEP_COUNTER's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.143  1434  1767 I SensorService: DEVICE_ORIENTATION's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.143  1434  1767 I SensorService: STATIONARY_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.143  1434  1767 I SensorService: MOTION_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.144  1434  1767 W SensorService: UNCALI_ACC's max range 39.226600646973 is not a multiple of the resolution 0.001200000057 - updated to 39.226802825928
05-24 20:21:21.144  1434  1767 I SensorService: UNCALI_ACC's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.144  1434  1767 I SensorService: tcs3701 LIGHT's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.144  1434  1767 I SensorService: RAW_MAG's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.144  1434  1767 I SensorService: mn29005 rear_als's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.144  1434  1767 I SensorService: ai_shutter's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.144  1434  1767 I SensorService: STEP_DETECTOR_WAKEUP's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.144  1434  1767 I SensorService: PICKUP_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.144  1434  1767 I SensorService: FP_DISPLAY's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.144  1434  1767 I SensorService: LUX_AOD's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.144  1434  1767 I SensorService: PEDO_MINUTE's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.144  1434  1767 I SensorService: OPLUS_ACTIVITY_RECOGNITION's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.144  1434  1767 I SensorService: ELEVATOR_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-24 20:21:21.147   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.light@2.0::ILight/default in either framework or device VINTF manifest.
05-24 20:21:21.155  1434  1434 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 20:21:21.157  1434  1434 I HealthServiceWrapperHidl: health: HealthServiceWrapper listening to instance default
05-24 20:21:21.157  1434  1434 I BatteryService: health: Waited 0ms and received the update.
05-24 20:21:21.161  1434  1434 I SystemServiceManager: Starting com.android.server.usage.UsageStatsService
05-24 20:21:21.169  1434  1434 I SystemServiceManager: Starting com.android.server.webkit.WebViewUpdateService
05-24 20:21:21.173  1434  1434 I SystemServiceManager: Starting com.android.server.CachedDeviceStateService
05-24 20:21:21.173  1434  1434 I SystemServiceManager: Starting com.android.server.BinderCallsStatsService$LifeCycle
05-24 20:21:21.175  1434  1434 I SystemServiceManager: Starting com.android.server.LooperStatsService$Lifecycle
05-24 20:21:21.177  1434  1434 I SystemServiceManager: Starting com.android.server.rollback.RollbackManagerService
05-24 20:21:21.178  1241  1433 I LSPosedService: manager is not installed
05-24 20:21:21.189  1434  1434 I SystemServiceManager: Starting com.android.server.os.NativeTombstoneManagerService
05-24 20:21:21.191  1434  1434 I SystemServiceManager: Starting com.android.server.os.BugreportManagerService
05-24 20:21:21.193  1434  1434 I SystemServiceManager: Starting com.android.server.gpu.GpuService
05-24 20:21:21.194  1434  1434 I SystemServiceManager: Starting com.android.server.security.rkp.RemoteProvisioningService
05-24 20:21:21.201  1434  1434 I SystemServiceManager: Starting com.android.server.security.KeyChainSystemService
05-24 20:21:21.202  1434  1434 I SystemServiceManager: Starting com.android.server.BinaryTransparencyService
05-24 20:21:21.203  1434  1434 I TransparencyService: Started BinaryTransparencyService
05-24 20:21:21.205  1434  1434 I SystemServiceManager: Starting com.android.server.telecom.TelecomLoaderService
05-24 20:21:21.217  1434  1779 I SchedulingPolicyService: Moving 1178 back to group default
05-24 20:21:21.217  1434  1434 I SystemServiceManager: Starting com.android.server.accounts.AccountManagerService$Lifecycle
05-24 20:21:21.225  1434  1434 I SystemServiceManager: Starting com.android.server.content.ContentService$Lifecycle
05-24 20:21:21.341   886   886 E ActivityRecognitionHardware: activity_recognition HAL is deprecated. class_init is effectively a no-op
05-24 20:21:21.356  1434  1434 I Freezer : Cannot open freezer path "/sys/fs/cgroup/uid_1000/pid_1434/frozen/freezer.state": No such file or directory
05-24 20:21:21.357  1434  1434 I SystemServiceManager: Starting com.android.server.deviceconfig.DeviceConfigInit$Lifecycle
05-24 20:21:21.359  1434  1434 I SystemServiceManager: Starting com.android.server.DropBoxManagerService
05-24 20:21:21.360  1434  1434 I SystemServiceManager: Starting com.android.ecm.EnhancedConfirmationService
05-24 20:21:21.367  1434  1434 I SystemServiceManager: Starting com.android.server.power.hint.HintManagerService
05-24 20:21:21.373  1434  1434 I SystemServiceManager: Starting com.android.role.RoleService
05-24 20:21:21.379  1434  1434 I SystemServiceManager: Starting com.android.server.vibrator.VibratorManagerService$Lifecycle
05-24 20:21:21.403  1434  1434 I SystemServiceManager: Starting com.android.server.alarm.AlarmManagerService
05-24 20:21:21.428  1434  1434 I InputManager: Initializing input manager, mUseDevInputEventForAudioJack=true
05-24 20:21:21.429  1434  1434 I SystemServiceManager: Starting com.android.server.devicestate.DeviceStateManagerService
05-24 20:21:21.434   886   886 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlags
05-24 20:21:21.434   886   886 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlagsImpl
05-24 20:21:21.434   886   886 W Zygote  : Class not found for preloading: android.media.audio.Flags
05-24 20:21:21.435  1434  1434 E DeviceStateManagerService: Cannot notify device state info change before the initial state has been committed.
05-24 20:21:21.435  1434  1434 I DeviceStateManagerService: Cannot notify device state info change when pending state is present.
05-24 20:21:21.439  1434  1434 I SystemServiceManager: Starting com.android.server.camera.CameraServiceProxy
05-24 20:21:21.444  1434  1434 I SystemServiceManager: Starting phase 200
05-24 20:21:21.579  1434  1793 I HidlServiceManagement: Registered android.frameworks.sensorservice@1.0::ISensorManager/default
05-24 20:21:21.580  1434  1794 I HidlServiceManagement: Registered android.frameworks.schedulerservice@1.0::ISchedulingPolicyService/default
05-24 20:21:21.582  1434  1434 I SystemServiceManager: Starting com.android.server.bluetooth.BluetoothService
05-24 20:21:21.596  1434  1434 I SystemServiceManager: Starting com.android.server.connectivity.IpConnectivityMetrics
05-24 20:21:21.597  1434  1434 I SystemServiceManager: Starting com.android.server.net.watchlist.NetworkWatchlistService$Lifecycle
05-24 20:21:21.602  1434  1434 I SystemServiceManager: Starting com.android.server.pinner.PinnerService
05-24 20:21:21.605  1434  1434 I SystemServiceManager: Starting com.android.server.integrity.AppIntegrityManagerService
05-24 20:21:21.608  1434  1434 I SystemServiceManager: Starting com.android.server.logcat.LogcatManagerService
05-24 20:21:21.612  1434  1434 I SystemServiceManager: Starting com.android.server.inputmethod.InputMethodManagerService$Lifecycle
05-24 20:21:21.643  1434  1434 I SystemServiceManager: Starting com.android.server.accessibility.AccessibilityManagerService$Lifecycle
05-24 20:21:21.666  1434  1434 I SystemServiceManager: Starting com.android.server.StorageManagerService$Lifecycle
05-24 20:21:21.677  1434  1595 W PinnerService: Could not find pinlist.meta for "/system_ext/priv-app/SystemUI/SystemUI.apk": pinning as blob
05-24 20:21:21.678  1434  1434 I SystemServiceManager: Starting com.android.server.usage.StorageStatsService$Lifecycle
05-24 20:21:21.688  1434  1434 I SystemServiceManager: Starting com.android.server.UiModeManagerService
05-24 20:21:21.692  1434  1434 I SystemServiceManager: Starting com.android.server.locales.LocaleManagerService
05-24 20:21:21.702  1434  1434 I SystemServiceManager: Starting com.android.server.grammaticalinflection.GrammaticalInflectionService
05-24 20:21:21.703  1434  1434 I SystemServiceManager: Starting com.android.server.apphibernation.AppHibernationService
05-24 20:21:21.708  1434  1434 I SystemServiceManager: Starting com.android.server.locksettings.LockSettingsService$Lifecycle
05-24 20:21:21.718  1434  1434 I SystemServiceManager: Starting com.android.server.pdb.PersistentDataBlockService
05-24 20:21:21.720  1434  1434 I SystemServiceManager: Starting com.android.server.testharness.TestHarnessModeService
05-24 20:21:21.720  1434  1434 I SystemServiceManager: Starting com.android.server.oemlock.OemLockService
05-24 20:21:21.724   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.oemlock@1.0::IOemLock/default in either framework or device VINTF manifest.
05-24 20:21:21.725  1434  1434 I SystemServiceManager: Starting com.android.server.DeviceIdleController
05-24 20:21:21.729  1434  1592 I PersistentDataBlockService: FRP secret matched, FRP deactivated.
05-24 20:21:21.732  1434  1434 I SystemServiceManager: Starting com.android.server.devicepolicy.DevicePolicyManagerService$Lifecycle
05-24 20:21:21.746  1434  1434 I SystemServiceManager: Starting com.android.server.systemcaptions.SystemCaptionsManagerService
05-24 20:21:21.746  1434  1434 I SystemServiceManager: Starting com.android.server.texttospeech.TextToSpeechManagerService
05-24 20:21:21.748  1434  1434 I SystemServiceManager: Starting com.android.server.wearable.WearableSensingManagerService
05-24 20:21:21.750  1434  1434 I SystemServiceManager: Starting com.android.server.ondeviceintelligence.OnDeviceIntelligenceManagerService
05-24 20:21:21.751  1434  1434 I SystemServiceManager: Starting com.android.server.speech.SpeechRecognitionManagerService
05-24 20:21:21.752  1434  1434 I SystemServiceManager: Starting com.android.server.appprediction.AppPredictionManagerService
05-24 20:21:21.753  1434  1434 I SystemServiceManager: Starting com.android.server.contentsuggestions.ContentSuggestionsManagerService
05-24 20:21:21.754  1434  1434 I SystemServiceManager: Starting com.android.server.contextualsearch.ContextualSearchManagerService
05-24 20:21:21.759  1434  1434 I FontManagerService: Using optimized boot-time font loading.
05-24 20:21:21.759  1434  1434 I SystemServiceManager: Starting com.android.server.textservices.TextServicesManagerService$Lifecycle
05-24 20:21:21.760  1434  1434 I SystemServiceManager: Starting com.android.server.textclassifier.TextClassificationManagerService$Lifecycle
05-24 20:21:21.762  1434  1434 I SystemServiceManager: Starting com.android.server.NetworkScoreService$Lifecycle
05-24 20:21:21.763  1434  1434 I NetworkScoreService: Registering network_score
05-24 20:21:21.765  1434  1434 I SystemServiceManager: Starting com.android.server.NetworkStatsServiceInitializer
05-24 20:21:21.800  1434  1434 I NetworkStatsServiceInitializer: Registering netstats
05-24 20:21:21.805  1434  1434 I SystemServiceManager: Starting com.android.server.wifi.WifiService
05-24 20:21:21.809  1434  1434 I WifiContext: Got resolveInfos for com.android.server.wifi.intent.action.SERVICE_WIFI_RESOURCES_APK: [ResolveInfo{353dc7b com.android.wifi.resources/android.app.Activity m=0x108000 userHandle=UserHandle{0}}]
05-24 20:21:21.815  1434  1434 E WifiScoringParams: 	at com.android.server.wifi.ScoringParams.<init>(ScoringParams.java:262)
05-24 20:21:21.815  1434  1434 E WifiScoringParams: 	at com.android.server.wifi.WifiInjector.<init>(WifiInjector.java:319)
05-24 20:21:21.815  1434  1434 E WifiScoringParams: 	at com.android.server.wifi.WifiService.<init>(WifiService.java:44)
05-24 20:21:21.815  1434  1434 E WifiScoringParams: 	at com.android.server.SystemServiceManager.startService(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:9)
05-24 20:21:21.815  1434  1434 E WifiScoringParams: 	at com.android.server.SystemServiceManager.startServiceFromJar(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:88)
05-24 20:21:21.815  1434  1434 E WifiScoringParams: 	at com.android.server.SystemServer.startOtherServices(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:322)
05-24 20:21:21.815  1434  1434 E WifiScoringParams: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:597)
05-24 20:21:21.815  1434  1434 E WifiScoringParams: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:889)
05-24 20:21:21.822  1434  1434 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.2::IServiceManager/default
05-24 20:21:21.827  1434  1434 I SupplicantStaIfaceHal: Initializing SupplicantStaIfaceHal using AIDL implementation.
05-24 20:21:21.828  1434  1434 I SupplicantP2pIfaceHal: Initializing SupplicantP2pIfaceHal using AIDL implementation.
05-24 20:21:21.842  1434  1595 W PinnerService: Could not find pinlist.meta for "/system_ext/priv-app/Launcher3QuickStep/Launcher3QuickStep.apk": pinning as blob
05-24 20:21:21.852  1434  1434 I WifiService: Registering wifi
05-24 20:21:21.853  1434  1434 I SystemServiceManager: Starting com.android.server.wifi.scanner.WifiScanningService
05-24 20:21:21.853  1434  1434 I WifiScanningService: Creating wifiscanner
05-24 20:21:21.855  1434  1434 I WifiScanningService: Publishing wifiscanner
05-24 20:21:21.856  1434  1434 I SystemServiceManager: Starting com.android.server.wifi.p2p.WifiP2pService
05-24 20:21:21.857  1434  1434 I WifiContext: Got resolveInfos for com.android.server.wifi.intent.action.SERVICE_WIFI_RESOURCES_APK: [ResolveInfo{38ff348 com.android.wifi.resources/android.app.Activity m=0x108000 userHandle=UserHandle{0}}]
05-24 20:21:21.859  1434  1434 I WifiP2pService: Registering wifip2p
05-24 20:21:21.861  1434  1434 I SystemServiceManager: Starting com.android.server.ConnectivityServiceInitializer
05-24 20:21:21.892  1434  1434 I MulticastRoutingCoordinatorService: socket created for multicast routing: java.io.FileDescriptor@f4747f8
05-24 20:21:21.896  1434  1828 W BroadcastLoopers: Found previously unknown looper Thread[NsdService,5,main]
05-24 20:21:21.900  1434  1434 I ConnectivityServiceInitializer: Registering ethernet
05-24 20:21:21.901  1434  1434 I ConnectivityServiceInitializer: Registering connectivity
05-24 20:21:21.901  1434  1434 I ConnectivityServiceInitializer: Registering ipsec
05-24 20:21:21.901  1434  1434 I ConnectivityServiceInitializer: Registering connectivity_native
05-24 20:21:21.901  1434  1434 I ConnectivityServiceInitializer: Registering servicediscovery
05-24 20:21:21.902  1434  1434 I ConnectivityServiceInitializer: Registering nearby
05-24 20:21:21.905  1434  1434 I SystemServiceManager: Starting com.android.server.ConnectivityServiceInitializerB
05-24 20:21:21.907  1434  1434 I ConnectivityServiceInitializerB: Registering vcn_management
05-24 20:21:21.908  1434  1434 I SystemUpdateManagerService: No existing info file /data/system/system-update-info.xml
05-24 20:21:21.909  1434  1434 I SystemServiceManager: Starting com.android.server.notification.NotificationManagerService
05-24 20:21:21.956  1434  1434 I NotificationManagerService.NotificationListeners: Read notification listener permissions from xml
05-24 20:21:21.957  1434  1434 W ActivityManager: Unable to start service Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener } U=0: not found
05-24 20:21:21.957  1434  1434 W NotificationManagerService.NotificationListeners: Unable to bind notification listener service: Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener (has extras) } in user 0
05-24 20:21:21.957  1434  1434 I NotificationManagerService.NotificationAssistants: Read notification assistant permissions from xml
05-24 20:21:21.960  1434  1434 I ConditionProviders: Read condition provider permissions from xml
05-24 20:21:21.960  1434  1434 I ConditionProviders: Read condition provider permissions from xml
05-24 20:21:21.961  1434  1434 I ConditionProviders:  Allowing condition provider android.ext.services/android.ext.services.notification.Assistant (userSet: true)
05-24 20:21:21.969  1434  1434 W SystemServiceManager: Service com.android.server.notification.NotificationManagerService took 59 ms in onStart
05-24 20:21:21.971  1434  1434 I SystemServiceManager: Starting com.android.server.storage.DeviceStorageMonitorService
05-24 20:21:21.972  1434  1434 I SystemServiceManager: Starting com.android.server.timedetector.TimeDetectorService$Lifecycle
05-24 20:21:21.976  1434  1434 I SystemServiceManager: Starting com.android.server.location.LocationManagerService$Lifecycle
05-24 20:21:21.981  1434  1434 I SystemServiceManager: Starting com.android.server.timezonedetector.TimeZoneDetectorService$Lifecycle
05-24 20:21:21.985  1434  1434 I SystemServiceManager: Starting com.android.server.location.altitude.AltitudeService$Lifecycle
05-24 20:21:21.986  1434  1434 I SystemServiceManager: Starting com.android.server.timezonedetector.location.LocationTimeZoneManagerService$Lifecycle
05-24 20:21:21.986  1434  1434 I SystemServiceManager: Starting com.android.server.search.SearchManagerService$Lifecycle
05-24 20:21:21.988  1434  1434 I SystemServiceManager: Starting com.android.server.wallpaper.WallpaperManagerService$Lifecycle
05-24 20:21:21.991  1434  1434 I SystemServiceManager: Starting com.android.server.audio.AudioService$Lifecycle
05-24 20:21:21.997  1636  1669 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:21.997  1636  1672 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:21.997  1636  1678 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:21.997  1636  1677 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:21.997  1636  1679 W AudioFlinger: no wake lock to update, system not ready yet
05-24 20:21:22.004  1625  1667 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 20:21:22.004  1625  1667 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 20:21:22.004  1625  1667 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 20:21:22.004  1625  1667 W audio_engineer_test: unknown enum value string receiver for ctl TFA98XX Profile
05-24 20:21:22.005  1625  1667 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 20:21:22.005  1625  1667 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 20:21:22.005  1625  1667 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 20:21:22.005  1625  1667 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-24 20:21:22.012  1434  1434 I AS.AudioService: Stream 5: using max vol of 7
05-24 20:21:22.012  1434  1434 I AS.AudioService: Stream 5: using default vol of 5
05-24 20:21:22.012  1434  1434 I AS.AudioService: Stream 2: using max vol of 7
05-24 20:21:22.012  1434  1434 I AS.AudioService: Stream 2: using default vol of 5
05-24 20:21:22.016  1434  1434 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 20:21:22.032  1434  1840 I AS.AudioService: updateIndexFactors() stream:0 index min/max:1/15 indexStepFactor:2.3333333
05-24 20:21:22.033  1434  1434 I SystemServiceManager: Starting com.android.server.soundtrigger_middleware.SoundTriggerMiddlewareService$Lifecycle
05-24 20:21:22.034  1434  1840 I AS.AudioService: updateIndexFactors() stream:1 index min/max:0/7 indexStepFactor:1.0
05-24 20:21:22.034  1434  1840 I AS.AudioService: updateIndexFactors() stream:2 index min/max:0/7 indexStepFactor:1.0
05-24 20:21:22.036  1434  1840 I AS.AudioService: updateIndexFactors() stream:3 index min/max:0/15 indexStepFactor:1.0
05-24 20:21:22.039  1434  1840 I AS.AudioService: updateIndexFactors() stream:4 index min/max:1/7 indexStepFactor:1.0
05-24 20:21:22.041  1434  1840 I AS.AudioService: updateIndexFactors() stream:5 index min/max:0/7 indexStepFactor:1.0
05-24 20:21:22.042  1434  1840 I AS.AudioService: updateIndexFactors() stream:7 index min/max:0/7 indexStepFactor:1.0
05-24 20:21:22.042  1434  1840 I AS.AudioService: updateIndexFactors() stream:8 index min/max:0/15 indexStepFactor:1.0
05-24 20:21:22.043  1434  1840 I AS.AudioService: updateIndexFactors() stream:9 index min/max:0/15 indexStepFactor:1.0
05-24 20:21:22.045  1434  1840 I AS.AudioService: updateIndexFactors() stream:10 index min/max:1/15 indexStepFactor:1.0
05-24 20:21:22.045  1434  1434 I SystemServiceManager: Starting com.android.server.DockObserver
05-24 20:21:22.046  1434  1434 W WiredAccessoryManager: This kernel does not have usb audio support
05-24 20:21:22.046  1434  1434 W WiredAccessoryManager: This kernel does not have HDMI audio support
05-24 20:21:22.046  1434  1434 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/3/0 does not have DP audio support
05-24 20:21:22.047  1434  1434 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/2/0 does not have DP audio support
05-24 20:21:22.047  1434  1434 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/1/0 does not have DP audio support
05-24 20:21:22.047  1434  1434 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/0/0 does not have DP audio support
05-24 20:21:22.047  1434  1434 I SystemServiceManager: Starting com.android.server.midi.MidiService$Lifecycle
05-24 20:21:22.048  1434  1434 I SystemServiceManager: Starting com.android.server.adb.AdbService$Lifecycle
05-24 20:21:22.048  1434  1840 I AS.AudioService: updateIndexFactors() stream:11 index min/max:0/15 indexStepFactor:1.0
05-24 20:21:22.050  1434  1434 I SystemServiceManager: Starting com.android.server.usb.UsbService$Lifecycle
05-24 20:21:22.052  1434  1434 I SystemServiceManager: Starting com.android.server.SerialService$Lifecycle
05-24 20:21:22.053  1434  1434 I HardwarePropertiesManagerService-JNI: Thermal AIDL service is not declared, trying HIDL
05-24 20:21:22.058  1434  1779 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 20:21:22.059  1434  1594 I UsbDeviceManager: Usb gadget hal service started android.hardware.usb.gadget@1.0::IUsbGadget default
05-24 20:21:22.063  1434  1779 W StorageManagerService: No primary storage defined yet; hacking together a stub
05-24 20:21:22.065  1434  1434 I SystemServiceManager: Starting com.android.server.twilight.TwilightService
05-24 20:21:22.066  1434  1434 I SystemServiceManager: Starting com.android.server.display.color.ColorDisplayService
05-24 20:21:22.067  1434  1434 I SystemServiceManager: Starting com.android.server.job.JobSchedulerService
05-24 20:21:22.071  1434  1779 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 20:21:22.073  1434  1848 I UsbPortManager: Usb hal service started android.hardware.usb@1.0::IUsb default
05-24 20:21:22.073  1434  1587 W JobInfo : Job 'com.google.android.setupwizard/.deviceorigin.provider.DeviceOriginWipeOutJobService#8580' has a deadline with no functional constraints. The deadline won't improve job execution latency. Consider removing the deadline.
05-24 20:21:22.077   926   960 I android.hardware.usb@1.3-service-mediatekv2: Registering 1.2 callback
05-24 20:21:22.077   926   960 I android.hardware.usb@1.3-service-mediatekv2: registering callback
05-24 20:21:22.077  1434  1587 W JobInfo : Job 'android/com.android.server.usage.UsageStatsIdleService#0' has a deadline with no functional constraints. The deadline won't improve job execution latency. Consider removing the deadline.
05-24 20:21:22.077  1434  1434 I SystemServiceManager: Starting com.android.server.soundtrigger.SoundTriggerService
05-24 20:21:22.077   926   960 I android.hardware.usb@1.3-service-mediatekv2: port0
05-24 20:21:22.078   926   960 I android.hardware.usb@1.3-service-mediatekv2: HAL version V1_2
05-24 20:21:22.078   926   960 I android.hardware.usb@1.3-service-mediatekv2: 0:port0 connected:1 canChangeMode:1 canChagedata:0 canChangePower:0 supportedModes:3
05-24 20:21:22.078   926   960 I android.hardware.usb@1.3-service-mediatekv2: ContaminantDetectionStatus:2 ContaminantProtectionStatus:0
05-24 20:21:22.078  1434  1434 I SystemServiceManager: Starting com.android.server.trust.TrustManagerService
05-24 20:21:22.078   926  1852 E android.hardware.usb@1.3-service-mediatekv2: creating thread
05-24 20:21:22.079  1434  1434 I SystemServiceManager: Starting com.android.server.backup.BackupManagerService$Lifecycle
05-24 20:21:22.088  1434  1434 I SystemServiceManager: Starting com.android.server.appwidget.AppWidgetService
05-24 20:21:22.092  1434  1434 I SystemServiceManager: Starting com.android.server.voiceinteraction.VoiceInteractionManagerService
05-24 20:21:22.097  1434  1434 I SystemServiceManager: Starting com.android.server.GestureLauncherService
05-24 20:21:22.097  1434  1434 I SystemServiceManager: Starting com.android.server.SensorNotificationService
05-24 20:21:22.099  1434  1587 W JobInfo : Job 'com.brave.browser/org.chromium.components.background_task_scheduler.internal.BackgroundTaskJobService#53' has a deadline with functional constraints and an extremely short time window of 0 ms (delay=0, deadline=0). The functional constraints are not likely to be satisfied when the job runs.
05-24 20:21:22.099  1434  1587 W JobInfo : Job 'com.brave.browser/org.chromium.components.background_task_scheduler.internal.BackgroundTaskJobService#53' has a deadline with functional constraints and an extremely short time window of 0 ms (delay=0, deadline=0). The functional constraints are not likely to be satisfied when the job runs.
05-24 20:21:22.104  1434  1840 W BroadcastLoopers: Found previously unknown looper Thread[AudioService,5,main]
05-24 20:21:22.108  1434  1434 I SystemServiceManager: Starting com.android.server.emergency.EmergencyAffordanceService
05-24 20:21:22.109  1434  1434 I SystemServiceManager: Starting com.android.server.blob.BlobStoreManagerService
05-24 20:21:22.111  1434  1434 I SystemServiceManager: Starting com.android.server.dreams.DreamManagerService
05-24 20:21:22.115  1434  1434 I SystemServiceManager: Starting com.android.server.print.PrintManagerService
05-24 20:21:22.116  1434  1434 I SystemServiceManager: Starting com.android.server.security.AttestationVerificationManagerService
05-24 20:21:22.117  1434  1434 I SystemServiceManager: Starting com.android.server.companion.CompanionDeviceManagerService
05-24 20:21:22.119   884   891 I ServiceManagerCppClient: Waiting for service 'statscompanion' on '/dev/binder'...
05-24 20:21:22.130  1434  1434 I SystemServiceManager: Starting com.android.server.companion.virtual.VirtualDeviceManagerService
05-24 20:21:22.133  1434  1434 I SystemServiceManager: Starting com.android.server.restrictions.RestrictionsManagerService
05-24 20:21:22.133  1434  1434 I SystemServiceManager: Starting com.android.server.media.MediaSessionService
05-24 20:21:22.137  1434  1434 I SystemServiceManager: Starting com.android.server.media.MediaResourceMonitorService
05-24 20:21:22.140  1434  1434 I SystemServiceManager: Starting com.android.server.biometrics.sensors.face.FaceService
05-24 20:21:22.141  1636  1636 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_HEADSET, connection: bt-le}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 20:21:22.141  1636  1636 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x20000000, enabled 1, streamToDriveAbs 3
05-24 20:21:22.141  1636  1636 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_HEARING_AID, connection: wireless}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 20:21:22.141  1636  1636 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x8000000, enabled 1, streamToDriveAbs 3
05-24 20:21:22.141  1636  1636 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_SPEAKER, connection: bt-le}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 20:21:22.141  1636  1636 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x20000001, enabled 1, streamToDriveAbs 3
05-24 20:21:22.141  1636  1636 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_BROADCAST, connection: bt-le}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 20:21:22.141  1636  1636 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x20000002, enabled 1, streamToDriveAbs 3
05-24 20:21:22.142  1434  1434 I SystemServiceManager: Starting com.android.server.biometrics.sensors.fingerprint.FingerprintService
05-24 20:21:22.143  1434  1840 E BluetoothAdapter: Bluetooth service is null
05-24 20:21:22.144  1434  1840 E BluetoothAdapter: Bluetooth service is null
05-24 20:21:22.144  1434  1434 I SystemServiceManager: Starting com.android.server.biometrics.BiometricService
05-24 20:21:22.145  1434  1840 E BluetoothAdapter: Bluetooth service is null
05-24 20:21:22.145  1434  1840 E BluetoothAdapter: Bluetooth service is null
05-24 20:21:22.145  1434  1840 I AS.SpatializerHelper: init effectExpected=false
05-24 20:21:22.145  1434  1840 I AS.SpatializerHelper: init(): setting state to STATE_NOT_SUPPORTED due to effect not expected
05-24 20:21:22.147  1434  1434 I CameraManagerGlobal: Connecting to camera service
05-24 20:21:22.152  1434  1434 I SystemServiceManager: Starting com.android.server.biometrics.AuthService
05-24 20:21:22.153  1434  1434 I FingerprintService: Before:getDeclaredInstances: IFingerprint instance found, a.length=0
05-24 20:21:22.153  1434  1434 I FingerprintService: After:getDeclaredInstances: a.length=1
05-24 20:21:22.154  1434  1434 I FaceService: Before:getDeclaredInstances: IFace instance found, a.length=0
05-24 20:21:22.154  1434  1434 I FaceService: After:getDeclaredInstances: a.length=1
05-24 20:21:22.156  1434  1434 E AuthService: Unknown modality: 2
05-24 20:21:22.157  1434  1434 I SystemServiceManager: Starting com.android.server.security.authenticationpolicy.AuthenticationPolicyService
05-24 20:21:22.159  1434  1434 I SystemServiceManager: Starting com.android.server.app.AppLockManagerService$Lifecycle
05-24 20:21:22.162  1434  1434 I SystemServiceManager: Starting com.android.server.display.FreeformService
05-24 20:21:22.181  1434  1434 I SystemServiceManager: Starting com.android.server.pm.ShortcutService$Lifecycle
05-24 20:21:22.187  1434  1434 I SystemServiceManager: Starting com.android.server.pm.LauncherAppsService
05-24 20:21:22.193  1434  1434 I SystemServiceManager: Starting com.android.server.pm.CrossProfileAppsService
05-24 20:21:22.194  1434  1434 I SystemServiceManager: Starting com.android.server.pocket.PocketService
05-24 20:21:22.204  1434  1434 I SystemServiceManager: Starting com.android.server.people.PeopleService
05-24 20:21:22.205  1434  1434 I SystemServiceManager: Starting com.android.server.media.metrics.MediaMetricsManagerService
05-24 20:21:22.207  1434  1434 I SystemServiceManager: Starting com.android.server.pm.BackgroundInstallControlService
05-24 20:21:22.210  1434  1434 I SystemServiceManager: Starting com.android.server.voltage.CustomDeviceConfigService
05-24 20:21:22.210  1434  1434 I SystemServiceManager: Starting com.android.server.custom.LineageHardwareService
05-24 20:21:22.212  1434  1434 I SystemServiceManager: Starting com.android.server.custom.display.LiveDisplayService
05-24 20:21:22.214  1434  1434 I SystemServiceManager: Starting com.android.server.custom.health.HealthInterfaceService
05-24 20:21:22.218  1434  1434 I SystemServiceManager: Starting com.android.server.HideAppListService
05-24 20:21:22.218  1434  1434 I HideAppListService: Starting HideAppListService
05-24 20:21:22.218  1434  1434 I SystemServiceManager: Starting com.android.server.GameSpaceManagerService
05-24 20:21:22.220  1434  1434 I SystemServiceManager: Starting com.android.server.media.projection.MediaProjectionManagerService
05-24 20:21:22.225  1434  1434 I SystemServiceManager: Starting com.android.server.slice.SliceManagerService$Lifecycle
05-24 20:21:22.312  1434  1434 I SystemServiceManager: Starting com.android.server.stats.StatsCompanion$Lifecycle
05-24 20:21:22.316  1434  1434 I SystemServiceManager: Starting com.android.server.scheduling.RebootReadinessManagerService$Lifecycle
05-24 20:21:22.320  1434  1434 I SystemServiceManager: Starting com.android.server.stats.pull.StatsPullAtomService
05-24 20:21:22.320  1434  1434 I SystemServiceManager: Starting com.android.server.stats.bootstrap.StatsBootstrapAtomService$Lifecycle
05-24 20:21:22.320   884   891 I ServiceManagerCppClient: Waiting for service 'statscompanion' on '/dev/binder' successful after waiting 201ms
05-24 20:21:22.321  1434  1434 I SystemServiceManager: Starting com.android.server.incident.IncidentCompanionService
05-24 20:21:22.322  1434  1434 I SystemServiceManager: Starting com.android.server.sdksandbox.SdkSandboxManagerService$Lifecycle
05-24 20:21:22.328  1434  1434 I SystemServiceManager: Starting com.android.server.adservices.AdServicesManagerService$Lifecycle
05-24 20:21:22.330  1434  1434 I SystemServiceManager: Starting com.android.server.ondevicepersonalization.OnDevicePersonalizationSystemService$Lifecycle
05-24 20:21:22.331  1434  1434 I ondevicepersonalization: OnDevicePersonalizationSystemService started!
05-24 20:21:22.332  1434  1434 I SystemServiceManager: Starting android.os.profiling.ProfilingService$Lifecycle
05-24 20:21:22.336  1434  1434 I SystemServiceManager: Starting com.android.server.MmsServiceBroker
05-24 20:21:22.337  1434  1434 I SystemServiceManager: Starting com.android.server.autofill.AutofillManagerService
05-24 20:21:22.340  1434  1434 E AutofillManagerServiceImpl: Bad service name: proton.android.pass/proton.android.pass.autofill.ProtonPassAutofillService
05-24 20:21:22.342  1434  1434 I SystemServiceManager: Starting com.android.server.credentials.CredentialManagerService
05-24 20:21:22.343  1434  1434 I SystemServiceManager: Starting com.android.server.clipboard.ClipboardService
05-24 20:21:22.346  1434  1434 I SystemServiceManager: Starting com.android.server.appbinding.AppBindingService$Lifecycle
05-24 20:21:22.347  1434  1434 I SystemServiceManager: Starting com.android.server.tracing.TracingServiceProxy
05-24 20:21:22.351   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.authsecret@1.0::IAuthSecret/default in either framework or device VINTF manifest.
05-24 20:21:22.352  1434  1434 I LockSettingsService: Device doesn't implement AuthSecret HAL
05-24 20:21:22.357  1434  1434 I SystemServiceManager: Starting phase 480
05-24 20:21:22.366  1434  1434 W PocketService: Un-handled boot phase:480
05-24 20:21:22.366  1434  1434 I SystemServiceManager: Starting phase 500
05-24 20:21:22.367  1434  1434 E StatsPullAtomCallbackImpl: Failed to start PowerStatsService statsd pullers
05-24 20:21:22.369  1434  1434 E BatteryStatsService: Could not register PowerStatsInternal
05-24 20:21:22.375   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.power.stats@1.0::IPowerStats/default in either framework or device VINTF manifest.
05-24 20:21:22.375  1434  1605 E BatteryStatsService: Unable to load Power.Stats.HAL. Setting rail availability to false
05-24 20:21:22.376  1434  1605 E BluetoothAdapter: Bluetooth service is null
05-24 20:21:22.389  1434  1611 W KeyguardServiceDelegate: onScreenTurningOn(): no keyguard service!
05-24 20:21:22.424  1434  1595 E AppWidgetManager: Notify service of inheritance info
05-24 20:21:22.424  1434  1595 E AppWidgetManager: 	at com.android.server.appwidget.AppWidgetServiceImpl.ensureGroupStateLoadedLocked(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:22)
05-24 20:21:22.424  1434  1595 E AppWidgetManager: 	at com.android.server.appwidget.AppWidgetServiceImpl.getInstalledProvidersForProfile(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:76)
05-24 20:21:22.492  1434  1434 W SystemServiceManager: Service com.android.server.alarm.AlarmManagerService took 67 ms in onBootPhase
05-24 20:21:22.526  1434  1434 I WifiScanningService: Starting wifiscanner
05-24 20:21:22.527  1434  1434 I EthernetServiceImpl: Starting Ethernet service
05-24 20:21:22.531  1434  1821 I WifiContext: Got resolveInfos for com.android.server.wifi.intent.action.SERVICE_WIFI_RESOURCES_APK: [ResolveInfo{52370de com.android.wifi.resources/android.app.Activity m=0x108000 userHandle=UserHandle{0}}]
05-24 20:21:22.541  1434  1817 I WifiService: WifiService starting up with Wi-Fi disabled
05-24 20:21:22.548  1434  1817 I WifiHalHidlImpl: Initializing the WiFi HAL
05-24 20:21:22.548  1434  1817 I WifiHalHidlImpl: initServiceManagerIfNecessaryLocked
05-24 20:21:22.550  1434  1817 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.2::IServiceManager/default
05-24 20:21:22.553  1434  1817 I WifiHalHidlImpl: initWifiIfNecessaryLocked
05-24 20:21:22.554   575   575 I hwservicemanager: Since android.hardware.wifi@1.0::IWifi/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 20:21:22.555   575   575 I hwservicemanager: Since android.hardware.wifi@1.0::IWifi/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 20:21:22.555  1434  1817 I HidlServiceManagement: getService: Trying again for android.hardware.wifi@1.0::IWifi/default...
05-24 20:21:22.588  1434  1595 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 20:21:22.589  1434  1595 I HealthServiceWrapperHidl: health: HealthServiceWrapper listening to instance default
05-24 20:21:22.592   575   575 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IKeySwapper/default in either framework or device VINTF manifest.
05-24 20:21:22.599  1434  1595 I StatsPullAtomService: register thermal listener successfully
05-24 20:21:22.603  1434  1434 I SystemServiceManager: Starting com.android.server.policy.PermissionPolicyService
05-24 20:21:22.611  1434  1434 E UserManagerService: Auto-lock preference updated but private space user not found
05-24 20:21:22.615  1434  1434 I AS.AudioService: registerAudioPolicy for android.media.audiopolicy.AudioPolicy$1@91e6bfc u/pid:1000/1434 with config:reg:32:ap:0
05-24 20:21:22.617   575   575 I hwservicemanager: getTransport: Cannot find entry vendor.oplus.hardware.commondcs@1.0::ICommonDcsHalService/commondcsservice in either framework or device VINTF manifest.
05-24 20:21:22.617  1214  1425 E android.hardware.biometrics.fingerprint@2.1-service: service NULL
05-24 20:21:22.624  1890  1890 I android.hardware.wifi@1.0-service-lazy: Wifi Hal is booting up...
05-24 20:21:22.630  1890  1890 I HidlServiceManagement: Registered android.hardware.wifi@1.5::IWifi/default
05-24 20:21:22.630  1890  1890 I HidlServiceManagement: Removing namespace from process name android.hardware.wifi@1.0-service-lazy to wifi@1.0-service-lazy.
05-24 20:21:22.632  1434  1615 W DefaultPermGrantPolicy: No such package:com.google.android.apps.camera.services
05-24 20:21:22.632  1434  1615 W DefaultPermGrantPolicy: No such package:com.verizon.mips.services
05-24 20:21:22.633  1434  1615 W DefaultPermGrantPolicy: No such package:com.google.android.adservices
05-24 20:21:22.634  1434  1434 I SystemServiceManager: Starting com.android.server.crashrecovery.CrashRecoveryModule$Lifecycle
05-24 20:21:22.635  1434  1615 W DefaultPermGrantPolicy: No such package:com.google.android.apps.actionsservice
05-24 20:21:22.635   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.wifi@1.6::IWifi/default in either framework or device VINTF manifest.
05-24 20:21:22.643  1434  1434 I BrightnessSynchronizer: Initial brightness readings: 88(int), 0.34251967(float)
05-24 20:21:22.644  1434  1434 I SystemServiceManager: Starting com.android.server.app.GameManagerService$Lifecycle
05-24 20:21:22.650  1434  1434 I SystemServiceManager: Starting phase 520
05-24 20:21:22.666   575   575 I hwservicemanager: Notifying android.hardware.wifi@1.5::IWifi/default they have clients: 1
05-24 20:21:22.669  1434  1434 W PocketService: Un-handled boot phase:520
05-24 20:21:22.671  1434  1434 I SystemServiceManager: Starting com.android.safetycenter.SafetyCenterService
05-24 20:21:22.688  1434  1434 I SystemServiceManager: Starting com.android.server.appsearch.AppSearchModule$Lifecycle
05-24 20:21:22.702  1434  1434 I AppSearchModule: AppsIndexer service is disabled.
05-24 20:21:22.702  1434  1434 I AppSearchModule: AppOpenEventIndexer service is disabled.
05-24 20:21:22.702  1434  1434 I SystemServiceManager: Starting com.android.server.media.MediaCommunicationService
05-24 20:21:22.704  1434  1434 I SystemServiceManager: Starting com.android.server.compat.overrides.AppCompatOverridesService$Lifecycle
05-24 20:21:22.705  1434  1434 I SystemServiceManager: Starting com.android.server.power.SleepModeService
05-24 20:21:22.707  1434  1434 I SystemServiceManager: Starting com.android.server.healthconnect.HealthConnectManagerService
05-24 20:21:22.716  1434  1434 I SystemServiceManager: Starting com.android.server.devicelock.DeviceLockService
05-24 20:21:22.719  1434  1434 I DeviceLockService: Registering device_lock
05-24 20:21:22.720  1434  1434 I SystemServiceManager: Starting com.android.server.SensitiveContentProtectionManagerService
05-24 20:21:22.746  1434  1434 E ActivityManager: Unable to find com.android.overlay.permissioncontroller/u0
05-24 20:21:22.748  1434  1434 E ActivityManager: Unable to find com.google.android.printservice.recommendation/u0
05-24 20:21:22.777  1434  1434 I SystemServer: Making services ready
05-24 20:21:22.778  1434  1434 I SystemServiceManager: Starting phase 550
05-24 20:21:22.786  1434  1434 I ThermalManagerService$ThermalHalWrapper: Thermal HAL 2.0 service connected.
05-24 20:21:22.787   925   925 I <EMAIL>: thermal_zone_num are changed0
05-24 20:21:22.787   925   925 W <EMAIL>: tz_data_v1[2].tz_idx:0
05-24 20:21:22.787   925   925 W <EMAIL>: tz_data_v1[3].tz_idx:2
05-24 20:21:22.787   925   925 W <EMAIL>: tz_data_v1[5].tz_idx:3
05-24 20:21:22.787   925   925 W <EMAIL>: tz_data_v1[0].tz_idx:5
05-24 20:21:22.787   925   925 W <EMAIL>: tz_data_v1[1].tz_idx:5
05-24 20:21:22.787   925   925 W <EMAIL>: tz_data_v1[9].tz_idx:5
05-24 20:21:22.788   925   925 W <EMAIL>: init_tz_path_v1:find out tz path
05-24 20:21:22.788   925   925 W <EMAIL>: get_tz_map: tz0, name=mtktscpu, label=CPU, muti_tz_num=1
05-24 20:21:22.788   925   925 W <EMAIL>: tz_idx0:5, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 20:21:22.788   925   925 W <EMAIL>: get_tz_map: tz1, name=mtktscpu, label=GPU, muti_tz_num=1
05-24 20:21:22.788   925   925 W <EMAIL>: tz_idx0:5, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 20:21:22.788   925   925 W <EMAIL>: get_tz_map: tz2, name=mtktsbattery, label=BATTERY, muti_tz_num=1
05-24 20:21:22.788   925   925 W <EMAIL>: tz_idx0:0, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 20:21:22.788   925   925 W <EMAIL>: get_tz_map: tz3, name=mtktsAP, label=SKIN, muti_tz_num=1
05-24 20:21:22.788   925   925 W <EMAIL>: tz_idx0:2, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 20:21:22.788   925   925 W <EMAIL>: get_tz_map: tz4, name=notsupport, label=USB_PORT, muti_tz_num=1
05-24 20:21:22.788   925   925 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 20:21:22.788   925   925 W <EMAIL>: get_tz_map: tz5, name=mtktsbtsmdpa, label=POWER_AMPLIFIER, muti_tz_num=1
05-24 20:21:22.788   925   925 W <EMAIL>: tz_idx0:3, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 20:21:22.788   925   925 W <EMAIL>: get_tz_map: tz6, name=notsupport, label=BCL_VOLTAGE, muti_tz_num=1
05-24 20:21:22.788   925   925 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 20:21:22.788   925   925 W <EMAIL>: get_tz_map: tz7, name=notsupport, label=BCL_CURRENT, muti_tz_num=1
05-24 20:21:22.788   925   925 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 20:21:22.788   925   925 W <EMAIL>: get_tz_map: tz8, name=notsupport, label=BCL_PERCENTAGE, muti_tz_num=1
05-24 20:21:22.788   925   925 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 20:21:22.788   925   925 W <EMAIL>: get_tz_map: tz9, name=mtktscpu, label=NPU, muti_tz_num=1
05-24 20:21:22.788   925   925 W <EMAIL>: tz_idx0:5, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 20:21:22.788   925   925 I <EMAIL>: fill_temperatures filterType0 name: CPU type: CPU throttlingStatus: NONE value: 62.194 ret_temps size 0
05-24 20:21:22.788   925   925 I <EMAIL>: fill_temperatures filterType0 name: GPU type: GPU throttlingStatus: NONE value: 62.194 ret_temps size 1
05-24 20:21:22.788   925   925 I <EMAIL>: fill_temperatures filterType0 name: BATTERY type: BATTERY throttlingStatus: NONE value: 37.8 ret_temps size 2
05-24 20:21:22.789   925   925 I <EMAIL>: fill_temperatures filterType0 name: SKIN type: SKIN throttlingStatus: NONE value: 47.332 ret_temps size 3
05-24 20:21:22.789   925   925 I <EMAIL>: fill_temperatures filterType0 name: POWER_AMPLIFIER type: POWER_AMPLIFIER throttlingStatus: NONE value: 45.937 ret_temps size 4
05-24 20:21:22.789   925   925 I <EMAIL>: fill_temperatures filterType0 name: NPU type: NPU throttlingStatus: NONE value: 62.194 ret_temps size 5
05-24 20:21:22.790   925   925 I <EMAIL>: fill_thresholds filterType1 name: SKIN type: SKIN hotThrottlingThresholds: 50 vrThrottlingThreshold: 50 ret_thresholds size 0
05-24 20:21:22.807  1434  1434 W ActivityManager: Too early to start/bind service in system_server: Phase=550 ComponentInfo{com.android.server.telecom/com.android.server.telecom.components.TelecomService}
05-24 20:21:22.807  1434  1434 W ActivityManager: Too early to start/bind service in system_server: Phase=550 ComponentInfo{com.android.server.telecom/com.android.server.telecom.components.TelecomService}
05-24 20:21:22.881  1434  1434 W SystemServiceManager: Service com.android.server.content.ContentService$Lifecycle took 73 ms in onBootPhase
05-24 20:21:22.885   575   575 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IGloveMode/default in either framework or device VINTF manifest.
05-24 20:21:22.887   575   575 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IStylusMode/default in either framework or device VINTF manifest.
05-24 20:21:22.888   575   575 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IHighTouchPollingRate/default in either framework or device VINTF manifest.
05-24 20:21:22.902  1434  1807 W StorageManagerService: Failed to get storage lifetime
05-24 20:21:22.914  1092  1269 I OMXClient: IOmx service obtained
05-24 20:21:22.916  1095  1135 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.aac.decoder'  err=ComponentNotFound(0x80001003)
05-24 20:21:22.916  1092  1269 W OmxInfoBuilder: Fail to add media type audio/mp4a-latm to codec OMX.google.aac.decoder
05-24 20:21:22.918  1092  1269 I OMXClient: IOmx service obtained
05-24 20:21:22.919  1095  1135 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.amrnb.decoder'  err=ComponentNotFound(0x80001003)
05-24 20:21:22.920  1092  1269 W OmxInfoBuilder: Fail to add media type audio/3gpp to codec OMX.google.amrnb.decoder
05-24 20:21:22.922  1092  1269 I OMXClient: IOmx service obtained
05-24 20:21:22.922  1095  1135 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.amrwb.decoder'  err=ComponentNotFound(0x80001003)
05-24 20:21:22.923  1092  1269 W OmxInfoBuilder: Fail to add media type audio/amr-wb to codec OMX.google.amrwb.decoder
05-24 20:21:22.925  1092  1269 I OMXClient: IOmx service obtained
05-24 20:21:22.926  1095  1135 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.flac.decoder'  err=ComponentNotFound(0x80001003)
05-24 20:21:22.926  1092  1269 W OmxInfoBuilder: Fail to add media type audio/flac to codec OMX.google.flac.decoder
05-24 20:21:22.929  1092  1269 I OMXClient: IOmx service obtained
05-24 20:21:22.930  1095  1135 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.g711.alaw.decoder'  err=ComponentNotFound(0x80001003)
05-24 20:21:22.931  1092  1269 W OmxInfoBuilder: Fail to add media type audio/g711-alaw to codec OMX.google.g711.alaw.decoder
05-24 20:21:22.932  1092  1269 I OMXClient: IOmx service obtained
05-24 20:21:22.934  1095  1135 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.g711.mlaw.decoder'  err=ComponentNotFound(0x80001003)
05-24 20:21:22.934  1092  1269 W OmxInfoBuilder: Fail to add media type audio/g711-mlaw to codec OMX.google.g711.mlaw.decoder
05-24 20:21:22.938  1092  1269 I OMXClient: IOmx service obtained
05-24 20:21:22.939  1095  1135 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.mp3.decoder'  err=ComponentNotFound(0x80001003)
05-24 20:21:22.940  1092  1269 W OmxInfoBuilder: Fail to add media type audio/mpeg to codec OMX.google.mp3.decoder
05-24 20:21:22.942  1092  1269 I OMXClient: IOmx service obtained
05-24 20:21:22.943  1095  1135 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.opus.decoder'  err=ComponentNotFound(0x80001003)
05-24 20:21:22.943  1092  1269 W OmxInfoBuilder: Fail to add media type audio/opus to codec OMX.google.opus.decoder
05-24 20:21:22.946  1092  1269 I OMXClient: IOmx service obtained
05-24 20:21:22.947  1095  1135 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.raw.decoder'  err=ComponentNotFound(0x80001003)
05-24 20:21:22.947  1092  1269 W OmxInfoBuilder: Fail to add media type audio/raw to codec OMX.google.raw.decoder
05-24 20:21:22.949  1092  1269 I OMXClient: IOmx service obtained
05-24 20:21:22.951  1095  1135 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.vorbis.decoder'  err=ComponentNotFound(0x80001003)
05-24 20:21:22.951  1092  1269 W OmxInfoBuilder: Fail to add media type audio/vorbis to codec OMX.google.vorbis.decoder
05-24 20:21:22.954  1092  1269 I OMXClient: IOmx service obtained
05-24 20:21:22.955  1095  1135 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.aac.encoder'  err=ComponentNotFound(0x80001003)
05-24 20:21:22.956  1092  1269 W OmxInfoBuilder: Fail to add media type audio/mp4a-latm to codec OMX.google.aac.encoder
05-24 20:21:22.959  1092  1269 I OMXClient: IOmx service obtained
05-24 20:21:22.959  1095  1135 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.amrnb.encoder'  err=ComponentNotFound(0x80001003)
05-24 20:21:22.960  1092  1269 W OmxInfoBuilder: Fail to add media type audio/3gpp to codec OMX.google.amrnb.encoder
05-24 20:21:22.961  1092  1269 I OMXClient: IOmx service obtained
05-24 20:21:22.963  1095  1135 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.amrwb.encoder'  err=ComponentNotFound(0x80001003)
05-24 20:21:22.963  1092  1269 W OmxInfoBuilder: Fail to add media type audio/amr-wb to codec OMX.google.amrwb.encoder
05-24 20:21:22.965  1092  1269 I OMXClient: IOmx service obtained
05-24 20:21:22.966  1095  1135 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.flac.encoder'  err=ComponentNotFound(0x80001003)
05-24 20:21:22.966  1092  1269 W OmxInfoBuilder: Fail to add media type audio/flac to codec OMX.google.flac.encoder
05-24 20:21:22.970  1092  1269 I Codec2Client: Available Codec2 services: "default" "software"
05-24 20:21:22.988  1434  1434 W SystemServiceManager: Service com.android.server.NetworkStatsServiceInitializer took 84 ms in onBootPhase
05-24 20:21:22.988  1434  1434 I ConnectivityServiceInitializerB: Starting vcn_management
05-24 20:21:22.998  1434  1831 I VcnManagementService: new snapshot: TelephonySubscriptionSnapshot{ mActiveDataSubId=-1, mSubIdToInfoMap={}, mSubIdToCarrierConfigMap={}, mPrivilegedPackages={} }
05-24 20:21:23.010  1434  1840 I AS.AudioDeviceBroker: setBluetoothScoOn: false, mBluetoothScoOn: false, btScoRequesterUId: -1, from: resetBluetoothSco
05-24 20:21:23.013   926   960 I android.hardware.usb@1.3-service-mediatekv2: port0
05-24 20:21:23.013   926   960 I android.hardware.usb@1.3-service-mediatekv2: HAL version V1_2
05-24 20:21:23.013   926   960 I android.hardware.usb@1.3-service-mediatekv2: 0:port0 connected:1 canChangeMode:1 canChagedata:0 canChangePower:0 supportedModes:3
05-24 20:21:23.013   926   960 I android.hardware.usb@1.3-service-mediatekv2: ContaminantDetectionStatus:2 ContaminantProtectionStatus:0
05-24 20:21:23.014  1434  1597 I ActivityManager: Start proc 1911:com.android.systemui/u0a226 for service {com.android.systemui/com.android.systemui.wallpapers.ImageWallpaper}
05-24 20:21:23.015  1434  1837 W BroadcastLoopers: Found previously unknown looper Thread[AudioDeviceBroker,5,main]
05-24 20:21:23.023  1636  1636 I AudioFlinger: systemReady
05-24 20:21:23.032  1914  1914 I zygiskd64: [KSU] Unmounted /system/etc/audio_effects.xml
05-24 20:21:23.032  1914  1914 I zygiskd64: [KSU] Unmounted /vendor/etc/audio_effects.xml
05-24 20:21:23.033  1434  1434 I AppLockManagerService: onBootCompleted
05-24 20:21:23.037  1434  1434 I LMOFreeform/LMOFreeformUIService: add SystemService: com.libremobileos.freeform.server.LMOFreeformUIService@82f7b9c
05-24 20:21:23.038  1434  1434 W PocketService: Un-handled boot phase:550
05-24 20:21:23.040  1434  1434 I AppBindingService: Updating constants with: null
05-24 20:21:23.066  1911  1911 W ndroid.systemui: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 20:21:23.103  1911  1911 W BpBinder: Linking to death on org.lsposed.lspd.service.ILSPApplicationService but there are no threads (yet?) listening to incoming transactions. See ProcessState::startThreadPool and ProcessState::setThreadPoolMaxThreadCount. Generally you should setup the binder threadpool before other initialization steps.
05-24 20:21:23.160  1434  1434 W SystemServiceManager: Service com.android.server.policy.PermissionPolicyService took 120 ms in onBootPhase
05-24 20:21:23.195  1434  1793 W PinnerService: Could not find pinlist.meta for "/product/app/webview/webview.apk": pinning as blob
05-24 20:21:23.247  1092  1269 I Codec2InfoBuilder: adding type 'audio/x-adpcm-dvi-ima'
05-24 20:21:23.248  1092  1269 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-dvi-ima
05-24 20:21:23.250  1092  1269 I Codec2InfoBuilder: adding type 'audio/x-adpcm-ms'
05-24 20:21:23.251  1092  1269 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-ms
05-24 20:21:23.256  1092  1269 I Codec2InfoBuilder: adding type 'audio/alac'
05-24 20:21:23.257  1092  1269 W AudioCapabilities: Unsupported mediaType audio/alac
05-24 20:21:23.260  1092  1269 I Codec2InfoBuilder: adding type 'audio/ape'
05-24 20:21:23.261  1092  1269 W AudioCapabilities: Unsupported mediaType audio/ape
05-24 20:21:23.292   883   883 I netd    : networkSetPermissionForUser(1, [1002, 10160, 10191, 10204, 10221, 10222, 10271, 10272, 10273, 10282, 10355]) <0.03ms>
05-24 20:21:23.293   883   883 I netd    : networkSetPermissionForUser(2, [1000, 1001, 1073, 2000, 10152, 10171, 10183, 10199, 10201, 10205, 10226, 10234, 10251]) <0.01ms>
05-24 20:21:23.365  1434  1434 I SystemServiceManager: Starting phase 600
05-24 20:21:23.381  1434  1434 I ServiceWatcher: [network] chose new implementation 10369/app.grapheneos.networklocation/.NetworkLocationService@0
05-24 20:21:23.390  1434  1434 I ServiceWatcher: [fused] chose new implementation 1000/com.android.location.fused/.FusedLocationService@0
05-24 20:21:23.412  1084  1116 W MNLD    : hal_gps_init: hal_gps_init
05-24 20:21:23.412  1434  1434 I GnssLocationProviderJni: Unable to initialize IGnssGeofencing interface.
05-24 20:21:23.416  1434  1434 I GnssManager: gnss hal initialized
05-24 20:21:23.418  1434  1434 I ServiceWatcher: [GeocoderProxy] chose new implementation null
05-24 20:21:23.419  1434  1434 I ServiceWatcher: [HardwareActivityRecognitionProxy] chose new implementation null
05-24 20:21:23.419  1434  1434 I ServiceWatcher: [GeofenceProxy] chose new implementation null
05-24 20:21:23.439  1092  1269 I Codec2InfoBuilder: adding type 'audio/mp4a-latm'
05-24 20:21:23.444  1434  1597 I ActivityManager: Start proc 2001:app.grapheneos.networklocation/u0a369 for service {app.grapheneos.networklocation/app.grapheneos.networklocation.NetworkLocationService}
05-24 20:21:23.445  1092  1269 I Codec2InfoBuilder: adding type 'audio/mp4a-latm'
05-24 20:21:23.449  1092  1269 I Codec2InfoBuilder: adding type 'audio/3gpp'
05-24 20:21:23.451  1092  1269 I Codec2InfoBuilder: adding type 'audio/3gpp'
05-24 20:21:23.455  1092  1269 I Codec2InfoBuilder: adding type 'audio/amr-wb'
05-24 20:21:23.457  1092  1269 I Codec2InfoBuilder: adding type 'audio/amr-wb'
05-24 20:21:23.464  2007  2007 I zygiskd32: [KSU] Unmounted /system/etc/audio_effects.xml
05-24 20:21:23.465  2007  2007 I zygiskd32: [KSU] Unmounted /vendor/etc/audio_effects.xml
05-24 20:21:23.481  1092  1269 I Codec2InfoBuilder: adding type 'audio/flac'
05-24 20:21:23.485  1092  1269 I Codec2InfoBuilder: adding type 'audio/flac'
05-24 20:21:23.487  2001  2001 W networklocation: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 20:21:23.487  1092  1269 I Codec2InfoBuilder: adding type 'audio/g711-alaw'
05-24 20:21:23.491  1092  1269 I Codec2InfoBuilder: adding type 'audio/g711-mlaw'
05-24 20:21:23.509  1434  1434 I ServiceWatcher: [TrustManagerService] chose new implementation null
05-24 20:21:23.509  1434  1434 W SystemServiceManager: Service com.android.server.trust.TrustManagerService took 61 ms in onBootPhase
05-24 20:21:23.515  1092  1269 I Codec2InfoBuilder: adding type 'audio/mpeg'
05-24 20:21:23.524  1434  1434 W PocketService: Un-handled boot phase:600
05-24 20:21:23.525  1434  1434 W AlarmManager: Unrecognized alarm listener com.android.server.stats.StatsCompanionService$PullingAlarmListener@8d09453
05-24 20:21:23.525  1434  1434 W AlarmManager: Unrecognized alarm listener com.android.server.stats.StatsCompanionService$PeriodicAlarmListener@932a190
05-24 20:21:23.527  1092  1269 I Codec2InfoBuilder: adding type 'audio/opus'
05-24 20:21:23.530  1092  1269 I Codec2InfoBuilder: adding type 'audio/opus'
05-24 20:21:23.534  1092  1269 I Codec2InfoBuilder: adding type 'audio/raw'
05-24 20:21:23.537  1092  1269 I Codec2InfoBuilder: adding type 'audio/vorbis'
05-24 20:21:23.537  1434  1434 I StatsCompanionService: Told statsd that StatsCompanionService is alive.
05-24 20:21:23.552  2001  2001 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-24 20:21:23.557  1434  1807 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-dvi-ima
05-24 20:21:23.558  1434  1807 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-ms
05-24 20:21:23.558  1434  1807 W AudioCapabilities: Unsupported mediaType audio/alac
05-24 20:21:23.559  1434  1807 W AudioCapabilities: Unsupported mediaType audio/ape
05-24 20:21:23.561  1434  1597 I ActivityManager: Start proc 2045:com.android.networkstack.process/1073 for service {com.android.networkstack/com.android.server.NetworkStackService}
05-24 20:21:23.564  1434  1922 I Codec2Client: Available Codec2 services: "default" "software"
05-24 20:21:23.572  1434  1434 I MR2ServiceImpl: switchUser | user: 0
05-24 20:21:23.572  1434  1434 I MmsServiceBroker: Delay connecting to MmsService until an API is called
05-24 20:21:23.585  2045  2045 W rkstack.process: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 20:21:23.587  1434  1807 W AudioCapabilities: Unsupported mime audio/x-adpcm-dvi-ima
05-24 20:21:23.595  1911  1936 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 20:21:23.610  1434  1807 W AudioCapabilities: Unsupported mime audio/x-adpcm-dvi-ima
05-24 20:21:23.610  1434  1807 W AudioCapabilities: Unsupported mime audio/x-adpcm-ms
05-24 20:21:23.610  1434  1807 W AudioCapabilities: Unsupported mime audio/x-adpcm-ms
05-24 20:21:23.610  1434  1807 W AudioCapabilities: Unsupported mime audio/alac
05-24 20:21:23.610  1434  1807 W AudioCapabilities: Unsupported mime audio/alac
05-24 20:21:23.610  1434  1807 W AudioCapabilities: Unsupported mime audio/ape
05-24 20:21:23.610  1434  1807 W AudioCapabilities: Unsupported mime audio/ape
05-24 20:21:23.611  1434  1434 I SystemServiceManager: Calling onStartUser 0
05-24 20:21:23.615  1434  1585 I ServiceWatcher: [network] connected to {app.grapheneos.networklocation/app.grapheneos.networklocation.NetworkLocationService}
05-24 20:21:23.637  1434  2039 E StatsCompanionService: Could not get installer for package: com.google.android.trichromelibrary
05-24 20:21:23.637  1434  2039 E StatsCompanionService: android.content.pm.PackageManager$NameNotFoundException: com.google.android.trichromelibrary
05-24 20:21:23.637  1434  2039 E StatsCompanionService: 	at android.app.ApplicationPackageManager.getInstallSourceInfo(ApplicationPackageManager.java:2772)
05-24 20:21:23.637  1434  2039 E StatsCompanionService: 	at com.android.server.stats.StatsCompanionService.getInstallerPackageName(StatsCompanionService.java:153)
05-24 20:21:23.637  1434  2039 E StatsCompanionService: 	at com.android.server.stats.StatsCompanionService.$r8$lambda$MBPStrBhgnmbybdtzkoTAe-YOYw(StatsCompanionService.java:229)
05-24 20:21:23.637  1434  2039 E StatsCompanionService: 	at com.android.server.stats.StatsCompanionService$$ExternalSyntheticLambda1.run(R8$$SyntheticClass:0)
05-24 20:21:23.637  1434  2039 E StatsCompanionService: 	at android.os.Handler.handleCallback(Handler.java:991)
05-24 20:21:23.637  1434  2039 E StatsCompanionService: 	at android.os.Handler.dispatchMessage(Handler.java:102)
05-24 20:21:23.637  1434  2039 E StatsCompanionService: 	at android.os.Looper.loopOnce(Looper.java:232)
05-24 20:21:23.637  1434  2039 E StatsCompanionService: 	at android.os.Looper.loop(Looper.java:317)
05-24 20:21:23.637  1434  2039 E StatsCompanionService: 	at android.os.HandlerThread.run(HandlerThread.java:85)
05-24 20:21:23.652  1434  1798 I BluetoothSystemServer: AirplaneModeListener: Init completed. isOn=false, isOnOverrode=false
05-24 20:21:23.653  1434  1798 I BluetoothSystemServer: SatelliteModeListener: Initialized successfully with state: false
05-24 20:21:23.691  2088  2088 I WebViewZygoteInit: Starting WebViewZygoteInit
05-24 20:21:23.711  2088  2088 I WebViewZygoteInit: Beginning application preload for com.android.webview
05-24 20:21:23.739  1434  1434 W SystemServiceManager: Service com.android.server.StorageManagerService$Lifecycle took 91 ms in onStartUser-0
05-24 20:21:23.743  2088  2088 I WebViewZygoteInit: Application preload done
05-24 20:21:23.756  1434  1434 W VoiceInteractionManager: no available voice recognition services found for user 0
05-24 20:21:23.757  1434  1434 I AppLockManagerService: onUserStarting: userId = 0
05-24 20:21:23.800  2124  2124 W com.android.se: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 20:21:23.821  1434  1434 I SystemServiceManager: Starting com.android.server.scheduling.RebootReadinessManagerService$Lifecycle
05-24 20:21:23.821  1434  1434 I SystemServiceManager: Not starting an already started service com.android.server.scheduling.RebootReadinessManagerService$Lifecycle
05-24 20:21:23.839  2136  2136 W m.android.phone: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 20:21:23.842  2124  2124 W ContextImpl: Failed to ensure /data/user/0/com.android.se/cache: mkdir failed: ENOENT (No such file or directory)
05-24 20:21:23.842  2124  2124 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-24 20:21:23.854  2124  2124 I SecureElementService: main onCreate
05-24 20:21:23.856  2124  2124 I SecureElementService: Check if terminal eSE1 is available.
05-24 20:21:23.858   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.secure_element@1.2::ISecureElement/eSE1 in either framework or device VINTF manifest.
05-24 20:21:23.858   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.secure_element@1.1::ISecureElement/eSE1 in either framework or device VINTF manifest.
05-24 20:21:23.859   575   575 I hwservicemanager: getTransport: Cannot find entry android.hardware.secure_element@1.0::ISecureElement/eSE1 in either framework or device VINTF manifest.
05-24 20:21:23.859  2124  2124 I SecureElementService: No HAL implementation for eSE1
05-24 20:21:23.860  2124  2124 I SecureElementService: Check if terminal SIM1 is available.
05-24 20:21:23.861   575   575 I hwservicemanager: Since android.hardware.secure_element@1.2::ISecureElement/SIM1 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 20:21:23.862   575   575 I hwservicemanager: Since android.hardware.secure_element@1.2::ISecureElement/SIM1 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 20:21:23.863  2124  2124 I HidlServiceManagement: getService: Trying again for android.hardware.secure_element@1.2::ISecureElement/SIM1...
05-24 20:21:23.869   575  2157 I hwservicemanager: Tried to start android.hardware.secure_element@1.2::ISecureElement/SIM1 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 20:21:23.870   575  2155 I hwservicemanager: Tried to start android.hardware.secure_element@1.2::ISecureElement/SIM1 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 20:21:23.872  2138  2138 W ndroid.settings: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 20:21:23.874  1434  1585 I ServiceWatcher: [fused] connected to {com.android.location.fused/com.android.location.fused.FusedLocationService}
05-24 20:21:23.876  1434  1434 I ExplicitHealthCheckController: Service not ready to get health check supported packages. Binding...
05-24 20:21:23.878  1434  1434 I ExplicitHealthCheckController: Explicit health check service is bound
05-24 20:21:23.879  1434  1434 I ConnectivityModuleConnector: Networking module service connected
05-24 20:21:23.879  1434  1434 I NetworkStackClient: Network stack service connected
05-24 20:21:23.887  1434  1434 W TrustManagerService: EXTRA_USER_HANDLE missing or invalid, value=0
05-24 20:21:23.889  1434  1585 I ServiceWatcher: [GeocoderProxy] chose new implementation null
05-24 20:21:23.889  1434  1585 I ServiceWatcher: [HardwareActivityRecognitionProxy] chose new implementation null
05-24 20:21:23.889  1434  1585 I ServiceWatcher: [GeofenceProxy] chose new implementation null
05-24 20:21:23.889  1434  1585 I ServiceWatcher: [TrustManagerService] chose new implementation null
05-24 20:21:23.890  1434  1434 I ConnectivityModuleConnector: Networking module service connected
05-24 20:21:23.895  1434  1835 W BroadcastLoopers: Found previously unknown looper Thread[AudioService Broadcast,5,main]
05-24 20:21:23.905  1911  1911 E SharedConnectivityManager: To support shared connectivity service on this device, the service's package name and intent action strings must not be empty
05-24 20:21:23.908  1434  1434 I Telecom : SystemStateHelper: Registering broadcast receiver: android.content.IntentFilter@e6fc9fb: TS.init@AAA
05-24 20:21:23.908  1434  1434 I Telecom : SystemStateHelper: Registering broadcast receiver: android.content.IntentFilter@845aa18: TS.init@AAA
05-24 20:21:23.918  1434  1434 I Telecom : CallAudioRouteController: calculateSupportedRouteMaskInit: is wired headset plugged in - false: TS.init@AAA
05-24 20:21:23.919  1434  1434 I Telecom : AudioRoute$Factory: createRetry; type=TYPE_SPEAKER, address=null, retryCount=2: TS.init@AAA
05-24 20:21:23.919  1434  1434 I Telecom : AudioRoute$Factory: type: 1: TS.init@AAA
05-24 20:21:23.919  1434  1434 I Telecom : AudioRoute$Factory: type: 2: TS.init@AAA
05-24 20:21:23.919  1434  1434 I Telecom : AudioRoute$Factory: createRetry; type=TYPE_EARPIECE, address=null, retryCount=2: TS.init@AAA
05-24 20:21:23.919  1434  1434 I Telecom : AudioRoute$Factory: type: 1: TS.init@AAA
05-24 20:21:23.920  2136  2136 E CarrierIdProvider: read carrier list from ota pb failure: java.io.FileNotFoundException: /data/misc/carrierid/carrier_list.pb: open failed: ENOENT (No such file or directory)
05-24 20:21:23.925  1434  2179 I Telecom : CallAudioModeStateMachine: Audio focus entering UNFOCUSED state
05-24 20:21:23.926  1434  2179 I Telecom : CallAudioModeStateMachine: Message received: null.: TS.init->CAMSM.pM_1@AAA
05-24 20:21:23.932  1434  1434 I Telecom : MissedCallNotifierImpl: reloadFromDatabase: Boot not yet complete -- call log db may not be available. Deferring loading until boot complete for user 0: TS.init@AAA
05-24 20:21:23.936  1434  1434 I ContentSuggestionsManagerService: Updating for user 0: disabled=false
05-24 20:21:23.937  1434  1434 E ContentSuggestionsPerUserService: Bad service name: com.google.android.as/com.google.android.apps.miphone.aiai.app.AiAiContentSuggestionsService
05-24 20:21:23.937  1434  1434 I AutofillManagerService: Updating for user 0: disabled=false
05-24 20:21:23.937  1434  1434 E AutofillManagerServiceImpl: Bad service name: proton.android.pass/proton.android.pass.autofill.ProtonPassAutofillService
05-24 20:21:23.942  1434  1434 W ActivityManager: Unable to start service Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener } U=0: not found
05-24 20:21:23.942  1434  1434 W NotificationManagerService.NotificationListeners: Unable to bind notification listener service: Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener (has extras) } in user 0
05-24 20:21:23.957  1911  1985 E AppWidgetManager: Notify service of inheritance info
05-24 20:21:23.957  1911  1985 E AppWidgetManager: 	at com.android.internal.appwidget.IAppWidgetService$Stub$Proxy.getInstalledProvidersForProfile(IAppWidgetService.java:1071)
05-24 20:21:23.961  1434  1597 I ActivityManager: Start proc 2186:com.android.permissioncontroller/u0a266 for broadcast {com.android.permissioncontroller/com.android.permissioncontroller.privacysources.SafetyCenterReceiver}
05-24 20:21:23.968  1434  1585 W PermissionService: getPermissionFlags: Unknown user -1
05-24 20:21:23.968  1434  1585 W PermissionService: getPermissionFlags: Unknown user -1
05-24 20:21:23.977  2186  2186 W ssioncontroller: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 20:21:24.014  2136  2136 E RILJ    : Feature android.hardware.telephony.calling is declared, but service VOICE is missing [PHONE0]
05-24 20:21:24.015  2136  2136 E RILJ    : Feature android.hardware.telephony.data is declared, but service DATA is missing [PHONE0]
05-24 20:21:24.016  2136  2136 E RILJ    : Feature android.hardware.telephony.messaging is declared, but service MESSAGING is missing [PHONE0]
05-24 20:21:24.017  2136  2136 E RILJ    : Feature android.hardware.telephony.ims is declared, but service IMS is missing [PHONE0]
05-24 20:21:24.018  1911  1993 I CameraManagerGlobal: Connecting to camera service
05-24 20:21:24.022  1085  2170 I AttributionAndPermissionUtils: checkPermission checkPermission (forDataDelivery 0 startDataDelivery 0): Permission soft denied for client attribution [uid 10226, pid 1911, packageName "<unknown>"]
05-24 20:21:24.054   978  2212 W gpuservice: AIBinder_linkToDeath is being called with a non-null cookie and no onUnlink callback set. This might not be intended. AIBinder_DeathRecipient_setOnUnlinked should be called first.
05-24 20:21:24.058  1911  1911 I SystemUIService: Found SurfaceFlinger's GPU Priority: 13143
05-24 20:21:24.058  1911  1911 I SystemUIService: Setting SysUI's GPU Context priority to: 12545
05-24 20:21:24.067  2136  2136 E RILJ    : Feature android.hardware.telephony.calling is declared, but service VOICE is missing [PHONE1]
05-24 20:21:24.068  2136  2136 E RILJ    : Feature android.hardware.telephony.data is declared, but service DATA is missing [PHONE1]
05-24 20:21:24.070  2136  2136 E RILJ    : Feature android.hardware.telephony.messaging is declared, but service MESSAGING is missing [PHONE1]
05-24 20:21:24.076  2136  2136 E RILJ    : Feature android.hardware.telephony.ims is declared, but service IMS is missing [PHONE1]
05-24 20:21:24.082  2138  2138 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 20:21:24.101  1434  1759 W WifiService: Attempt to retrieve WifiConfiguration with invalid scanResult List
05-24 20:21:24.102  1434  2188 W WifiService: Attempt to retrieve WifiConfiguration with invalid scanResult List
05-24 20:21:24.103  1434  2188 E WifiService: Attempt to retrieve passpoint with invalid scanResult List
05-24 20:21:24.103  1434  2188 W WifiService: Attempt to retrieve OsuProviders with invalid scanResult List
05-24 20:21:24.135  1434  1588 W KeyguardServiceDelegate: onScreenTurningOn(): no keyguard service!
05-24 20:21:24.152  1911  2251 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 20:21:24.158  1434  1597 I ActivityManager: Start proc 2254:com.android.launcher3/u0a230 for service {com.android.launcher3/com.android.quickstep.TouchInteractionService}
05-24 20:21:24.162  2252  2252 W receiver.module: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 20:21:24.167  1911  2251 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 20:21:24.188  2254  2254 W droid.launcher3: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 20:21:24.194  2136  2136 E SatelliteController: SatelliteController was not yet initialized.
05-24 20:21:24.220  1434  1597 I ActivityManager: Start proc 2280:com.android.smspush/u0a237 for service {com.android.smspush/com.android.smspush.WapPushManager}
05-24 20:21:24.227  2254  2254 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-24 20:21:24.241  2254  2254 I QuickstepProtoLogGroup: Initializing ProtoLog.
05-24 20:21:24.251  2280  2280 W android.smspush: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: activity
05-24 20:21:24.300  2254  2313 E FileLog : java.io.FileNotFoundException: /data/user/0/com.android.launcher3/files/log-0: open failed: ENOENT (No such file or directory)
05-24 20:21:24.300  2254  2313 E FileLog : 	at java.io.FileOutputStream.<init>(FileOutputStream.java:259)
05-24 20:21:24.300  2254  2313 E FileLog : 	at java.io.FileWriter.<init>(FileWriter.java:113)
05-24 20:21:24.300  2254  2313 E FileLog : Caused by: android.system.ErrnoException: open failed: ENOENT (No such file or directory)
05-24 20:21:24.311  2280  2280 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-24 20:21:24.370  1434  1616 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.adservices/appid
05-24 20:21:24.373  1434  1616 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.permission/appid
05-24 20:21:24.382  1434  1616 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.btservices/appid
05-24 20:21:24.392  1434  1616 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.extservices/appid
05-24 20:21:24.397  1434  1616 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.nfcservices/appid
05-24 20:21:24.407  1434  2177 W Telecom : BluetoothDeviceManager: getBluetoothHeadset: Acquire BluetoothHeadset service failed due to: java.util.concurrent.TimeoutException
05-24 20:21:24.407  1434  2177 I Telecom : BluetoothRouteManager: getBluetoothAudioConnectedDevice: no service available.
05-24 20:21:24.409  1434  2178 I Telecom : CallAudioRouteController: Message received: BT_AUDIO_DISCONNECTED=1301, arg1=0
05-24 20:21:24.482  2136  2136 E EmergencyNumberTracker: [0]Cache ota emergency database IOException: java.io.FileNotFoundException: /data/misc/emergencynumberdb/emergency_number_db: open failed: ENOENT (No such file or directory)
05-24 20:21:24.484  1911  1911 E SharedConnectivityManager: To support shared connectivity service on this device, the service's package name and intent action strings must not be empty
05-24 20:21:24.489  2252  2322 E CellBroadcastConfigService: fails to setCellBroadcastRanges
05-24 20:21:24.498  2252  2322 E CellBroadcastConfigService: fails to setCellBroadcastRanges
05-24 20:21:24.498  1434  1763 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-24 20:21:24.499  1434  1552 I DefaultPermGrantPolicy: Granting permissions to enabled data services for user:0
05-24 20:21:24.507  1434  2318 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-24 20:21:24.552  2136  2136 E SatelliteController: SatelliteController was not yet initialized.
05-24 20:21:24.590  2136  2136 E EmergencyNumberTracker: [1]Cache ota emergency database IOException: java.io.FileNotFoundException: /data/misc/emergencynumberdb/emergency_number_db: open failed: ENOENT (No such file or directory)
05-24 20:21:24.598  1434  2040 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-24 20:21:24.599  1434  2040 I DefaultPermGrantPolicy: Granting permissions to enabled data services for user:0
05-24 20:21:24.604  1434  2040 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-24 20:21:24.657  1434  2122 I MR2ServiceImpl: registerManager | callerUid: 10226, callerPid: 1911, callerPackage: com.android.systemui, targetPackageName: null, targetUserId: UserHandle{0}, hasMediaRoutingControl: false
05-24 20:21:24.661  1434  1863 I MR2ServiceImpl: addProviderRoutes | provider: com.android.server.media/.SystemMediaRoute2Provider, routes: [ROUTE_ID_BUILTIN_SPEAKER | Phone]
05-24 20:21:24.665  1434  1863 I AS.AudioService: removePreferredDevicesForStrategy strat:5
05-24 20:21:24.683  2136  2343 I ImsResolver: Initializing cache.
05-24 20:21:24.690  2136  2136 E SatelliteModemInterface: Unable to bind to the satellite service because the package is undefined.
05-24 20:21:24.753  2136  2136 I TelephonyRcsService: updateFeatureControllers: oldSlots=0, newNumSlots=2
05-24 20:21:24.801  1434  1434 I AS.AudioService: onSubscriptionsChanged()
05-24 20:21:24.811  1434  1434 I AS.AudioService: onSubscriptionsChanged()
05-24 20:21:24.830  2136  2136 E RILJ    : getImei not supported on service MODEM < 2.1. [PHONE0]
05-24 20:21:24.831  1434  1831 I VcnManagementService: new snapshot: TelephonySubscriptionSnapshot{ mActiveDataSubId=2, mSubIdToInfoMap={}, mSubIdToCarrierConfigMap={}, mPrivilegedPackages={} }
05-24 20:21:24.834  1434  1595 I StatsPullAtomService: subId 2 added into historical sub list
05-24 20:21:24.837  2136  2136 E RILJ    : setNullCipherAndIntegrityEnabled not supported on service NETWORK < 2.1. [PHONE0]
05-24 20:21:24.838  2136  2136 E RILJ    : setCellularIdentifierTransparencyEnabled not supported on service NETWORK < 2.2. [PHONE0]
05-24 20:21:24.839  2136  2136 E RILJ    : setSecurityAlgorithmsUpdatedEnabled not supported on service NETWORK < 2.2. [PHONE0]
05-24 20:21:24.850  2136  2136 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-24 20:21:24.862  2136  2136 E NRM-C-0 : service not connected. Domain = PS
05-24 20:21:24.862  2136  2136 E NRM-C-0 : service not connected. Domain = CS
05-24 20:21:24.862  2136  2136 E NRM-I-0 : service not connected. Domain = PS
05-24 20:21:24.863  2124  2124 W HidlServiceManagement: Waited one second for android.hardware.secure_element@1.2::ISecureElement/SIM1
05-24 20:21:24.864   575   575 I hwservicemanager: Since android.hardware.secure_element@1.2::ISecureElement/SIM1 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 20:21:24.866  2124  2124 I HidlServiceManagement: getService: Trying again for android.hardware.secure_element@1.2::ISecureElement/SIM1...
05-24 20:21:24.870  1911  2365 I Codec2Client: Available Codec2 services: "default" "software"
05-24 20:21:24.872  2136  2136 E NRM-C-0 : service not connected. Domain = PS
05-24 20:21:24.872  2136  2136 E NRM-C-0 : service not connected. Domain = CS
05-24 20:21:24.872  2136  2136 E NRM-I-0 : service not connected. Domain = PS
05-24 20:21:24.883  1434  1595 I StatsPullAtomService: subId 1 added into historical sub list
05-24 20:21:24.903  2136  2136 E RILJ    : getImei not supported on service MODEM < 2.1. [PHONE1]
05-24 20:21:24.907   575  2366 I hwservicemanager: Tried to start android.hardware.secure_element@1.2::ISecureElement/SIM1 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 20:21:24.909  2136  2136 E RILJ    : setNullCipherAndIntegrityEnabled not supported on service NETWORK < 2.1. [PHONE1]
05-24 20:21:24.910  2136  2136 E RILJ    : setCellularIdentifierTransparencyEnabled not supported on service NETWORK < 2.2. [PHONE1]
05-24 20:21:24.911  2136  2136 E RILJ    : setSecurityAlgorithmsUpdatedEnabled not supported on service NETWORK < 2.2. [PHONE1]
05-24 20:21:24.920  2136  2136 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-24 20:21:24.922  1434  1763 W Binder  : java.lang.SecurityException: Need REGISTER_STATS_PULL_ATOM permission.: Neither user 10226 nor current process has android.permission.REGISTER_STATS_PULL_ATOM.
05-24 20:21:24.922  1434  1763 W Binder  : 	at android.app.ContextImpl.enforceCallingOrSelfPermission(ContextImpl.java:2630)
05-24 20:21:24.922  1434  1763 W Binder  : 	at com.android.server.stats.StatsManagerService.enforceRegisterStatsPullAtomPermission(StatsManagerService.java:678)
05-24 20:21:24.922  1434  1763 W Binder  : 	at com.android.server.stats.StatsManagerService.registerPullAtomCallback(StatsManagerService.java:219)
05-24 20:21:24.922  1434  1763 W Binder  : 	at android.os.IStatsManagerService$Stub.onTransact(IStatsManagerService.java:434)
05-24 20:21:24.926  1434  1553 I StatusBarManagerService: registerStatusBar bar=com.android.internal.statusbar.IStatusBar$Stub$Proxy@d34cd3
05-24 20:21:24.927  2136  2136 E NRM-C-1 : service not connected. Domain = PS
05-24 20:21:24.927  2136  2136 E NRM-C-1 : service not connected. Domain = CS
05-24 20:21:24.927  2136  2136 E NRM-I-1 : service not connected. Domain = PS
05-24 20:21:24.936  1911  1911 I KeyguardSecurityView: Switching mode from Uninitialized to Default
05-24 20:21:24.939  2136  2136 E NRM-C-1 : service not connected. Domain = PS
05-24 20:21:24.939  2136  2136 E NRM-C-1 : service not connected. Domain = CS
05-24 20:21:24.939  2136  2136 E NRM-I-1 : service not connected. Domain = PS
05-24 20:21:24.948  2379  2379 I auditd  : type=1400 audit(0.0:518): avc:  denied  { execute } for  comm="init" name="vendor.oplus.hardware.olc@2.0-service" dev="dm-3" ino=17 scontext=u:r:init:s0 tcontext=u:object_r:vendor_file:s0 tclass=file permissive=1
05-24 20:21:24.948  2379  2379 I init    : type=1400 audit(0.0:518): avc:  denied  { execute } for  name="vendor.oplus.hardware.olc@2.0-service" dev="dm-3" ino=17 scontext=u:r:init:s0 tcontext=u:object_r:vendor_file:s0 tclass=file permissive=1
05-24 20:21:24.948  2379  2379 I auditd  : type=1400 audit(0.0:519): avc:  denied  { execute_no_trans } for  comm="init" path="/odm/bin/hw/vendor.oplus.hardware.olc@2.0-service" dev="dm-3" ino=17 scontext=u:r:init:s0 tcontext=u:object_r:vendor_file:s0 tclass=file permissive=1
05-24 20:21:24.948  2379  2379 I init    : type=1400 audit(0.0:519): avc:  denied  { execute_no_trans } for  path="/odm/bin/hw/vendor.oplus.hardware.olc@2.0-service" dev="dm-3" ino=17 scontext=u:r:init:s0 tcontext=u:object_r:vendor_file:s0 tclass=file permissive=1
05-24 20:21:24.957  1911  1911 E OverviewProxyService: Failed to get overview proxy for disable flags.
05-24 20:21:24.968  2379  2379 I auditd  : type=1400 audit(0.0:520): avc:  denied  { execute } for  comm="vendor.oplus.ha" path="/vendor/lib64/libcutils.so" dev="dm-1" ino=1789 scontext=u:r:init:s0 tcontext=u:object_r:same_process_hal_file:s0 tclass=file permissive=1
05-24 20:21:24.968  2379  2379 I vendor.oplus.ha: type=1400 audit(0.0:520): avc:  denied  { execute } for  path="/vendor/lib64/libcutils.so" dev="dm-1" ino=1789 scontext=u:r:init:s0 tcontext=u:object_r:same_process_hal_file:s0 tclass=file permissive=1
05-24 20:21:24.981  1911  1911 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 20:21:25.000  2379  2379 I auditd  : type=1400 audit(0.0:521): avc:  denied  { read } for  comm="vendor.oplus.ha" name="vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:25.000  2379  2379 I vendor.oplus.ha: type=1400 audit(0.0:521): avc:  denied  { read } for  name="vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:25.000  2379  2379 I auditd  : type=1400 audit(0.0:522): avc:  denied  { write } for  comm="vendor.oplus.ha" name="vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:25.000  2379  2379 I vendor.oplus.ha: type=1400 audit(0.0:522): avc:  denied  { write } for  name="vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:25.000  2379  2379 I auditd  : type=1400 audit(0.0:523): avc:  denied  { open } for  comm="vendor.oplus.ha" path="/dev/binderfs/vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:25.000  2379  2379 I vendor.oplus.ha: type=1400 audit(0.0:523): avc:  denied  { open } for  path="/dev/binderfs/vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:25.000  2379  2379 I auditd  : type=1400 audit(0.0:524): avc:  denied  { ioctl } for  comm="vendor.oplus.ha" path="/dev/binderfs/vndbinder" dev="binder" ino=6 ioctlcmd=0x6209 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:25.000  2379  2379 I vendor.oplus.ha: type=1400 audit(0.0:524): avc:  denied  { ioctl } for  path="/dev/binderfs/vndbinder" dev="binder" ino=6 ioctlcmd=0x6209 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:25.000  2379  2379 I auditd  : type=1400 audit(0.0:525): avc:  denied  { map } for  comm="vendor.oplus.ha" path="/dev/binderfs/vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:25.000  2379  2379 I vendor.oplus.ha: type=1400 audit(0.0:525): avc:  denied  { map } for  path="/dev/binderfs/vndbinder" dev="binder" ino=6 scontext=u:r:init:s0 tcontext=u:object_r:vndbinder_device:s0 tclass=chr_file permissive=1
05-24 20:21:25.000  2379  2379 I auditd  : type=1400 audit(0.0:526): avc:  denied  { create } for  comm="vendor.oplus.ha" scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:25.000  2379  2379 I vendor.oplus.ha: type=1400 audit(0.0:526): avc:  denied  { create } for  scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:25.000  2379  2379 I auditd  : type=1400 audit(0.0:527): avc:  denied  { bind } for  comm="vendor.oplus.ha" scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:25.000  2379  2379 I vendor.oplus.ha: type=1400 audit(0.0:527): avc:  denied  { bind } for  scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:25.000  2379  2379 I auditd  : type=1400 audit(0.0:528): avc:  denied  { write } for  comm="vendor.oplus.ha" scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:25.000  2379  2379 I vendor.oplus.ha: type=1400 audit(0.0:528): avc:  denied  { write } for  scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:25.000  2379  2379 I auditd  : type=1400 audit(0.0:529): avc:  denied  { read } for  comm="vendor.oplus.ha" scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:25.000  2379  2379 I vendor.oplus.ha: type=1400 audit(0.0:529): avc:  denied  { read } for  scontext=u:r:init:s0 tcontext=u:r:init:s0 tclass=netlink_generic_socket permissive=1
05-24 20:21:25.000  2379  2379 I auditd  : type=1400 audit(0.0:530): avc:  denied  { call } for  comm="vendor.oplus.ha" scontext=u:r:init:s0 tcontext=u:r:hwservicemanager:s0 tclass=binder permissive=1
05-24 20:21:25.000  2379  2379 I vendor.oplus.ha: type=1400 audit(0.0:530): avc:  denied  { call } for  scontext=u:r:init:s0 tcontext=u:r:hwservicemanager:s0 tclass=binder permissive=1
05-24 20:21:25.002   575   575 I hwservicemanager: getTransport: Cannot find entry vendor.oplus.hardware.olc@2.0::IOplusLogCore/default in either framework or device VINTF manifest.
05-24 20:21:25.002  2379  2379 E HidlServiceManagement: Service vendor.oplus.hardware.olc@2.0::IOplusLogCore/default must be in VINTF manifest in order to register/get.
05-24 20:21:25.002  2379  2379 E OLC_HAL : registerHidlService failed. 
05-24 20:21:25.008  1911  1911 E OverviewProxyService: Failed to get overview proxy for disable flags.
05-24 20:21:25.029  1911  1911 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 20:21:25.040  1911  1911 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 20:21:25.044  2136  2136 I Telephony: TelecomAccountRegistry$AccountEntry: registerPstnPhoneAccount: Registering account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with Telecom. subId=-1
05-24 20:21:25.047  1434  2318 I Telecom : TelecomServiceImpl$1: registerPhoneAccount: account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@AAo
05-24 20:21:25.048  1434  2318 I Telecom : PhoneAccountRegistrar: New phone account registered: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@AAo
05-24 20:21:25.052  1434  2318 I Telecom : CallsManager: Sending phone-account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} registered intent as user: TSI.rPA(cap)@AAo
05-24 20:21:25.056  1434  2318 I Telecom : PhoneAccountRegistrar: Notifying telephony of voice service override change for 1 SIMs, hasService = false: TSI.rPA(cap)@AAo
05-24 20:21:25.056  1434  2318 I Telecom : PhoneAccountRegistrar: maybeNotifyTelephonyForVoiceServiceState: simTm is null.: TSI.rPA(cap)@AAo
05-24 20:21:25.056  2136  2136 I Telephony: TelecomAccountRegistry$AccountEntry: Registered phoneAccount: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with handle: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}
05-24 20:21:25.058  2136  2136 I Telephony: TelecomAccountRegistry: Unregistering phone account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 2, UserHandle{0}.
05-24 20:21:25.061  1434  2318 I Telecom : CallsManager: Sending phone-account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 2, UserHandle{0} unregistered intent as user: TSI.uPA(cap)@AAw
05-24 20:21:25.063  2136  2136 I Telephony: TelecomAccountRegistry: Unregistering phone account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 1, UserHandle{0}.
05-24 20:21:25.064  1434  1763 I Telecom : CallsManager: Sending phone-account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 1, UserHandle{0} unregistered intent as user: TSI.uPA(cap)@AA0
05-24 20:21:25.079  1434  1552 W UserManagerService: Requested status bar icon for non-badged user 0
05-24 20:21:25.080  1434  1434 I AS.AudioService: onSubscriptionsChanged()
05-24 20:21:25.083  1434  1434 I AS.AudioService: onSubscriptionsChanged()
05-24 20:21:25.086  1434  1434 I AS.AudioService: onSubscriptionsChanged()
05-24 20:21:25.089  1434  1434 I AS.AudioService: onSubscriptionsChanged()
05-24 20:21:25.110  1911  1911 I SystemUIService: Topological CoreStartables completed in 2 iterations
05-24 20:21:25.110  1911  1911 E OverviewProxyService: Failed to get overview proxy for disable flags.
05-24 20:21:25.121  1434  2318 W UserManagerService: Requested status bar icon for non-badged user 0
05-24 20:21:25.124  1911  1911 W SimLog  : invalid subId in handleServiceStateChange()
05-24 20:21:25.142  1257  1257 E bootanimation: === MALI DEBUG ===eglp_check_display_valid_and_initialized_and_retain retun EGL_NOT_INITIALIZED
05-24 20:21:25.143  2136  2136 I Telephony: TelecomAccountRegistry$AccountEntry: registerPstnPhoneAccount: Registering account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with Telecom. subId=-1
05-24 20:21:25.144  1434  1763 I Telecom : TelecomServiceImpl$1: registerPhoneAccount: account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@AA8
05-24 20:21:25.145  1434  1763 I Telecom : PhoneAccountRegistrar: Modify account: [ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}]: TSI.rPA(cap)@AA8
05-24 20:21:25.146  1434  1763 I Telecom : CallsManager: handlePhoneAccountChanged: phoneAccount=[[X] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@AA8
05-24 20:21:25.149  1434  1763 I Telecom : PhoneAccountRegistrar: Notifying telephony of voice service override change for 1 SIMs, hasService = false: TSI.rPA(cap)@AA8
05-24 20:21:25.149  1434  1763 I Telecom : PhoneAccountRegistrar: maybeNotifyTelephonyForVoiceServiceState: simTm is null.: TSI.rPA(cap)@AA8
05-24 20:21:25.149  2136  2136 I Telephony: TelecomAccountRegistry$AccountEntry: Registered phoneAccount: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with handle: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}
05-24 20:21:25.168  2136  2136 E SST     : [0] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-24 20:21:25.168  2136  2136 E SST     : [0] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-24 20:21:25.168  2136  2136 E SST     : [0] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-24 20:21:25.170  2136  2136 E NRM-C-0 : service not connected. Domain = PS
05-24 20:21:25.171  2136  2136 E NRM-C-0 : service not connected. Domain = CS
05-24 20:21:25.171  2136  2136 E NRM-I-0 : service not connected. Domain = PS
05-24 20:21:25.178  2136  2136 E SST     : [1] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-24 20:21:25.179  2136  2136 E SST     : [1] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-24 20:21:25.179  2136  2136 E SST     : [1] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-24 20:21:25.179  2136  2136 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-24 20:21:25.180  2136  2136 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-24 20:21:25.199  2136  2136 E NRM-C-1 : service not connected. Domain = PS
05-24 20:21:25.199  2136  2136 E NRM-C-1 : service not connected. Domain = CS
05-24 20:21:25.199  2136  2136 E NRM-I-1 : service not connected. Domain = PS
05-24 20:21:25.231  1434  1553 W UserManagerService: Requested status bar icon for non-badged user 0
05-24 20:21:25.273  2136  2136 I Telephony: TelecomAccountRegistry$AccountEntry: registerPstnPhoneAccount: Registering account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with Telecom. subId=-1
05-24 20:21:25.274  1434  1552 I Telecom : TelecomServiceImpl$1: registerPhoneAccount: account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABI
05-24 20:21:25.276  1434  1552 I Telecom : PhoneAccountRegistrar: Modify account: [ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}]: TSI.rPA(cap)@ABI
05-24 20:21:25.277  1434  1552 I Telecom : CallsManager: handlePhoneAccountChanged: phoneAccount=[[X] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABI
05-24 20:21:25.279  1434  1552 I Telecom : PhoneAccountRegistrar: Notifying telephony of voice service override change for 1 SIMs, hasService = false: TSI.rPA(cap)@ABI
05-24 20:21:25.279  1434  1552 I Telecom : PhoneAccountRegistrar: maybeNotifyTelephonyForVoiceServiceState: simTm is null.: TSI.rPA(cap)@ABI
05-24 20:21:25.280  2136  2136 I Telephony: TelecomAccountRegistry$AccountEntry: Registered phoneAccount: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with handle: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}
05-24 20:21:25.336  2136  2136 I Telephony: TelecomAccountRegistry$AccountEntry: registerPstnPhoneAccount: Registering account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with Telecom. subId=-1
05-24 20:21:25.337  1434  1552 I Telecom : TelecomServiceImpl$1: registerPhoneAccount: account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABQ
05-24 20:21:25.339  1434  1552 I Telecom : PhoneAccountRegistrar: Modify account: [ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}]: TSI.rPA(cap)@ABQ
05-24 20:21:25.340  1434  1552 I Telecom : CallsManager: handlePhoneAccountChanged: phoneAccount=[[X] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABQ
05-24 20:21:25.344  1434  1552 I Telecom : PhoneAccountRegistrar: Notifying telephony of voice service override change for 1 SIMs, hasService = false: TSI.rPA(cap)@ABQ
05-24 20:21:25.344  1434  1552 I Telecom : PhoneAccountRegistrar: maybeNotifyTelephonyForVoiceServiceState: simTm is null.: TSI.rPA(cap)@ABQ
05-24 20:21:25.344  2136  2136 I Telephony: TelecomAccountRegistry$AccountEntry: Registered phoneAccount: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with handle: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}
05-24 20:21:25.400  1434  1588 I SystemServiceManager: Starting phase 1000
05-24 20:21:25.400  1434  1588 E PowerStatsService: Failed to start PowerStatsService loggers
05-24 20:21:25.401  2136  2136 I Telephony: TelecomAccountRegistry$AccountEntry: registerPstnPhoneAccount: Registering account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with Telecom. subId=-1
05-24 20:21:25.402  1434  1553 I Telecom : TelecomServiceImpl$1: registerPhoneAccount: account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABY
05-24 20:21:25.403  1434  1553 I Telecom : PhoneAccountRegistrar: Modify account: [ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}]: TSI.rPA(cap)@ABY
05-24 20:21:25.404  1434  1553 I Telecom : CallsManager: handlePhoneAccountChanged: phoneAccount=[[X] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABY
05-24 20:21:25.406  1434  1553 I Telecom : PhoneAccountRegistrar: Notifying telephony of voice service override change for 1 SIMs, hasService = false: TSI.rPA(cap)@ABY
05-24 20:21:25.406  1434  1553 I Telecom : PhoneAccountRegistrar: maybeNotifyTelephonyForVoiceServiceState: simTm is null.: TSI.rPA(cap)@ABY
05-24 20:21:25.407  2136  2136 I Telephony: TelecomAccountRegistry$AccountEntry: Registered phoneAccount: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with handle: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}
05-24 20:21:25.408   985  2400 I PowerHalLoader: Successfully connected to Power HAL AIDL service.
05-24 20:21:25.411  1434  1588 I TransparencyService: Boot completed. Getting boot integrity data.
05-24 20:21:25.412  1434  1588 I TransparencyService: Boot completed. Collecting biometric system properties.
05-24 20:21:25.413  1434  1588 I TransparencyService: Scheduling measurements to be taken.
05-24 20:21:25.414  1434  1588 I TransparencyService: Scheduling binary content-digest computation job
05-24 20:21:25.422  1434  1807 I StorageSessionController: Started resetting external storage service...
05-24 20:21:25.422  1434  1807 I StorageSessionController: Finished resetting external storage service
05-24 20:21:25.422  1434  1807 I StorageManagerService: Resetting vold...
05-24 20:21:25.426  1434  1807 I StorageManagerService: Reset vold
