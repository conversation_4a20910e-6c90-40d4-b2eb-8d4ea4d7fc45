05-24 15:39:32.542   579   579 I hwservicemanager: getFrameworkHalManifest: Reading VINTF information.
05-24 15:39:31.928     1     1 I auditd  : type=1107 audit(0.0:4): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_cam_stereo_camera_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-24 15:39:31.928     1     1 W /system/bin/init: type=1107 audit(0.0:4): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_cam_stereo_camera_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-24 15:39:31.928     1     1 I auditd  : type=1107 audit(0.0:5): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_cam_dualzoom_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-24 15:39:31.928     1     1 W /system/bin/init: type=1107 audit(0.0:5): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_cam_dualzoom_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-24 15:39:31.928     1     1 I auditd  : type=1107 audit(0.0:6): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_key_manager_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-24 15:39:31.928     1     1 W /system/bin/init: type=1107 audit(0.0:6): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_key_manager_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-24 15:39:31.928     1     1 I auditd  : type=1107 audit(0.0:7): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.build.fingerprint pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:fingerprint_prop:s0 tclass=property_service permissive=0'
05-24 15:39:31.928     1     1 W /system/bin/init: type=1107 audit(0.0:7): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.build.fingerprint pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:fingerprint_prop:s0 tclass=property_service permissive=0'
05-24 15:39:32.551   579   579 I hwservicemanager: Loaded APEX Infos from /bootstrap-apex/apex-info-list.xml
05-24 15:39:32.551   579   579 I hwservicemanager: getDeviceHalManifest: Reading VINTF information.
05-24 15:39:32.559   577   577 I auditd  : SELinux: Loaded service context from:
05-24 15:39:32.560   577   577 I auditd  : 		/system/etc/selinux/plat_service_contexts
05-24 15:39:32.560   577   577 I auditd  : 		/system_ext/etc/selinux/system_ext_service_contexts
05-24 15:39:32.560   577   577 I auditd  : 		/product/etc/selinux/product_service_contexts
05-24 15:39:32.560   577   577 I auditd  : 		/vendor/etc/selinux/vendor_service_contexts
05-24 15:39:32.560   577   577 I auditd  : 		/odm/etc/selinux/odm_service_contexts
05-24 15:39:32.571   579   579 I hwservicemanager: Loaded APEX Infos from /bootstrap-apex/apex-info-list.xml
05-24 15:39:32.574   579   579 I hwservicemanager: getDeviceHalManifest: Successfully processed VINTF information
05-24 15:39:32.574   579   579 I hwservicemanager: getFrameworkHalManifest: Successfully processed VINTF information
05-24 15:39:32.579   579   579 I hwservicemanager: hwservicemanager is ready now.
05-24 15:39:32.850     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvdata" to "42": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-24 15:39:32.892     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvdata" to "40": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-24 15:39:32.935     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvcfg" to "38": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-24 15:39:32.980     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvcfg" to "43": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-24 15:39:33.475   635   635 I HidlServiceManagement: Registered android.system.suspend@1.0::ISystemSuspend/default
05-24 15:39:33.475   635   635 I HidlServiceManagement: Removing namespace from process name android.system.suspend-service to suspend-service.
05-24 15:39:33.487   637   637 I <EMAIL>: Trustonic Keymaster 4.1 Service starts
05-24 15:39:33.489   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.boot@1.0::IBootControl/default in either framework or device VINTF manifest.
05-24 15:39:33.496   637   637 I HidlServiceManagement: Registered android.hardware.keymaster@4.1::IKeymasterDevice/default
05-24 15:39:33.497   637   637 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 15:39:33.497   637   637 I <EMAIL>: Trustonic Keymaster 4.1 Service registered
05-24 15:39:33.535   636   636 I keystore2: system/security/keystore2/src/keystore2_main.rs:154 - Successfully registered Keystore 2.0 service.
05-24 15:39:33.662   577   644 W libc    : Unable to set property "ctl.interface_start" to "aidl/android.system.keystore2.IKeystoreService/default": PROP_ERROR_HANDLE_CONTROL_MESSAGE (0x20)
05-24 15:39:33.681   654   654 I libperfmgr: Pixel Power HAL AIDL Service with Extension is starting with config: /vendor/etc/powerhint.json
05-24 15:39:33.685   654   654 I libperfmgr: Failed to read Node[18]'s ResetOnInit, set to 'false'
05-24 15:39:33.685   654   654 I libperfmgr: Failed to read Node[19]'s ResetOnInit, set to 'false'
05-24 15:39:33.685   654   654 I libperfmgr: Failed to read Node[20]'s ResetOnInit, set to 'false'
05-24 15:39:33.685   654   654 I libperfmgr: Failed to read Node[21]'s ResetOnInit, set to 'false'
05-24 15:39:33.688   654   654 I libperfmgr: PowerHint AUDIO_STREAMING_LOW_LATENCY has 3 node actions, and 0 hint actions parsed
05-24 15:39:33.689   654   654 I libperfmgr: Initialized HintManager from JSON config: /vendor/etc/powerhint.json
05-24 15:39:33.690   654   654 I powerhal-libperfmgr: Initialize PowerHAL
05-24 15:39:33.691   654   654 I powerhal-libperfmgr: Pixel Power HAL AIDL Service with Extension is started.
05-24 15:39:33.718   592   592 I vold    : fscrypt_initialize_systemwide_keys
05-24 15:39:33.791   592   592 I incfs   : Initial API level of the device: 30
05-24 15:39:33.822   636   646 E keystore2:     1: system/security/keystore2/src/globals.rs:264: Trying to get Legacy wrapper. Attempt to get keystore compat service for security level r#STRONGBOX
05-24 15:39:33.822   592   592 E vold    : keystore2 Keystore earlyBootEnded returned service specific error: -68
05-24 15:39:33.832   664   664 I tombstoned: tombstoned successfully initialized
05-24 15:39:34.240   806   806 I derive_sdk: extension ad_services version is 15
05-24 15:39:34.465   810   810 I derive_classpath: ReadClasspathFragment /apex/com.android.adservices/etc/classpaths/bootclasspath.pb
05-24 15:39:34.467   810   810 I derive_classpath: ReadClasspathFragment /apex/com.android.btservices/etc/classpaths/bootclasspath.pb
05-24 15:39:34.472   810   810 I derive_classpath: ReadClasspathFragment /apex/com.android.nfcservices/etc/classpaths/bootclasspath.pb
05-24 15:39:34.474   810   810 I derive_classpath: ReadClasspathFragment /apex/com.android.permission/etc/classpaths/bootclasspath.pb
05-24 15:39:34.483   810   810 I derive_classpath: ReadClasspathFragment /apex/com.android.adservices/etc/classpaths/systemserverclasspath.pb
05-24 15:39:34.485   810   810 I derive_classpath: ReadClasspathFragment /apex/com.android.btservices/etc/classpaths/systemserverclasspath.pb
05-24 15:39:34.489   810   810 I derive_classpath: ReadClasspathFragment /apex/com.android.permission/etc/classpaths/systemserverclasspath.pb
05-24 15:39:34.493   810   810 I derive_classpath: export BOOTCLASSPATH /apex/com.android.art/javalib/core-oj.jar:/apex/com.android.art/javalib/core-libart.jar:/apex/com.android.art/javalib/okhttp.jar:/apex/com.android.art/javalib/bouncycastle.jar:/apex/com.android.art/javalib/apache-xml.jar:/system/framework/framework.jar:/system/framework/framework-graphics.jar:/system/framework/framework-location.jar:/system/framework/framework-connectivity-b.jar:/system/framework/ext.jar:/system/framework/telephony-common.jar:/system/framework/voip-common.jar:/system/framework/ims-common.jar:/system/framework/framework-platformcrashrecovery.jar:/system/framework/framework-ondeviceintelligence-platform.jar:/system/framework/mediatek-common.jar:/system/framework/mediatek-framework.jar:/system/framework/mediatek-ims-base.jar:/system/framework/mediatek-ims-common.jar:/system/framework/mediatek-telecom-common.jar:/system/framework/mediatek-telephony-base.jar:/system/framework/mediatek-telephony-common.jar:/apex/com.android.i18n/javalib/core-icu4j.jar:/apex/com.android.adservices/javalib/framework-adservices.jar:/apex/com.android.adservices/javalib/framework-sdksandbox.jar:/apex/com.android.appsearch/javalib/framework-appsearch.jar:/apex/com.android.btservices/javalib/framework-bluetooth.jar:/apex/com.android.configinfrastructure/javalib/framework-configinfrastructure.jar:/apex/com.android.conscrypt/javalib/conscrypt.jar:/apex/com.android.devicelock/javalib/framework-devicelock.jar:/apex/com.android.healthfitness/javalib/framework-healthfitness.jar:/apex/com.android.ipsec/javalib/android.net.ipsec.ike.jar:/apex/com.android.media/javalib/updatable-media.jar:/apex/com.android.mediaprovider/javalib/framework-mediaprovider.jar:/apex/com.android.mediaprovider/javalib/framework-pdf.jar:/apex/com.android.mediaprovider/javalib/framework-pdf-v.jar:/apex/com.android.mediaprovider/javalib/framework-photopicker.jar:/apex/com.android.nfcservices/javalib/framework-nfc.jar:/apex/com.android.ondevicepersonalization/javalib/framework-ondevicepersonalization.jar:/apex/com.android.os.statsd/javalib/framework-statsd.jar:/apex/com.android.permission/javalib/framework-permission.jar:/apex/com.android.permission/javalib/framework-permission-s.jar:/apex/com.android.profiling/javalib/framework-profiling.jar:/apex/com.android.scheduling/javalib/framework-scheduling.jar:/apex/com.android.sdkext/javalib/framework-sdkextensions.jar:/apex/com.android.tethering/javalib/framework-connectivity.jar:/apex/com.android.tethering/javalib/framework-connectivity-t.jar:/apex/com.android.tethering/javalib/framework-tethering.jar:/apex/com.android.uwb/javalib/framework-uwb.jar:/apex/com.android.virt/javalib/framework-virtualization.jar:/apex/com.android.wifi/javalib/framework-wifi.jar
05-24 15:39:34.493   810   810 I derive_classpath: export SYSTEMSERVERCLASSPATH /system/framework/com.android.location.provider.jar:/system/framework/services.jar:/apex/com.android.adservices/javalib/service-adservices.jar:/apex/com.android.adservices/javalib/service-sdksandbox.jar:/apex/com.android.appsearch/javalib/service-appsearch.jar:/
05-24 15:39:34.525   811   811 I art_boot: Property persist.device_config.runtime_native_boot.useartservice not set
05-24 15:39:34.635   812   812 W odsign  : Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.system.keystore2.IKeystoreService/default
05-24 15:39:34.651   812   812 I odsign  : Initialized Keystore key.
05-24 15:39:36.134   825   825 I netdClient: Skipping libnetd_client init since *we* are netd
05-24 15:39:36.136   579   579 I hwservicemanager: getFrameworkHalManifest: Reloading VINTF information.
05-24 15:39:36.136   579   579 I hwservicemanager: getFrameworkHalManifest: Reading VINTF information.
05-24 15:39:36.140   579   579 I hwservicemanager: Loaded APEX Infos from /apex/apex-info-list.xml
05-24 15:39:36.141   579   579 I hwservicemanager: getDeviceHalManifest: Reloading VINTF information.
05-24 15:39:36.142   579   579 I hwservicemanager: getDeviceHalManifest: Reading VINTF information.
05-24 15:39:36.156   579   579 I hwservicemanager: Loaded APEX Infos from /apex/apex-info-list.xml
05-24 15:39:36.158   579   579 I hwservicemanager: getDeviceHalManifest: Successfully processed VINTF information
05-24 15:39:36.158   579   579 I hwservicemanager: getFrameworkHalManifest: Successfully processed VINTF information
05-24 15:39:36.164   837   837 I mtk.hal.bt@1.0-impl: Init IBluetoothHCI
05-24 15:39:36.165   825   825 I NetdUpdatable: libnetd_updatable_init: Initializing
05-24 15:39:36.167   835   835 I HidlServiceManagement: Registered android.hidl.allocator@1.0::IAllocator/ashmem
05-24 15:39:36.168   835   835 I HidlServiceManagement: Removing namespace from process name android.hidl.allocator@1.0-service to allocator@1.0-service.
05-24 15:39:36.169   825   825 I NetdUpdatable: initMaps successfully
05-24 15:39:36.169   825   825 I netd    : libnetd_updatable_init success
05-24 15:39:36.172   838   838 I HidlServiceManagement: Registered android.hardware.cas@1.2::IMediaCasService/default
05-24 15:39:36.172   838   838 I HidlServiceManagement: Removing namespace from process name android.hardware.cas@1.2-service to cas@1.2-service.
05-24 15:39:36.174   837   837 I HidlServiceManagement: Registered android.hardware.bluetooth@1.1::IBluetoothHci/default
05-24 15:39:36.174   837   837 I HidlServiceManagement: Removing namespace from process name android.hardware.bluetooth@1.1-service-mediatek to bluetooth@1.1-service-mediatek.
05-24 15:39:36.190   852   852 W <EMAIL>: ThermalHelper:tz_map_version 1
05-24 15:39:36.191   852   852 I <EMAIL>: ThermalWatcherThread started
05-24 15:39:36.193   852   852 I HidlServiceManagement: Registered android.hardware.thermal@2.0::IThermal/default
05-24 15:39:36.193   852   852 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 15:39:36.194   845   845 I android.hardware.health@2.1-service: default instance initializing with healthd_config...
05-24 15:39:36.197   856   856 I vtservice_hidl: [VT][SRV]before VTService_HiDL_instantiate
05-24 15:39:36.197   845   845 I HidlServiceManagement: Registered android.hardware.health@2.1::IHealth/default
05-24 15:39:36.197   845   845 I HidlServiceManagement: Removing namespace from process name android.hardware.health@2.1-service to health@2.1-service.
05-24 15:39:36.197   845   845 I android.hardware.health@2.1-service: default: Hal init done
05-24 15:39:36.201   862   862 W <EMAIL>: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.hardware.power.IPower/default
05-24 15:39:36.201   862   862 I <EMAIL>: Connected to power AIDL HAL
05-24 15:39:36.209   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_agps
05-24 15:39:36.216   862   862 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkpower@1.2::IMtkPower/default
05-24 15:39:36.216   862   862 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 15:39:36.229   878   878 W MTK_FG_FUEL: fd < 0, init first!
05-24 15:39:36.230   878   878 W MTK_FG_FUEL: init failed, return!
05-24 15:39:36.230   878   878 W MTK_FG  : fd < 0, init first!
05-24 15:39:36.230   878   878 E MTK_FG  : init failed, return!
05-24 15:39:36.230   878   878 W MTK_FG  : fd < 0, init first!
05-24 15:39:36.230   878   878 E MTK_FG  : init failed, return!
05-24 15:39:36.230   878   878 W MTK_FG  : fd < 0, init first!
05-24 15:39:36.230   878   878 E MTK_FG  : init failed, return!
05-24 15:39:36.234   853   853 I android.hardware.usb@1.3-service-mediatekv2: UsbGadget
05-24 15:39:36.236   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_wlan
05-24 15:39:36.236   840   840 I HidlServiceManagement: Registered android.hardware.drm@1.4::IDrmFactory/widevine
05-24 15:39:36.237   840   840 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 15:39:36.239   842   842 E android.hardware.gnss-service.mediatek: safe_sendto: safe_sendto() sendto() failed path=[mtk_hal2mnl] ret=-1 reason=[Connection refused]111
05-24 15:39:36.240   842   842 E android.hardware.gnss-service.mediatek: gps_device__get_gps_interface: hal2mnl_hal_reboot failed because of safe_sendto fail ,strerror:Connection refused 
05-24 15:39:36.241   862   862 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkpower@1.2::IMtkPerf/default
05-24 15:39:36.241   862   862 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 15:39:36.242   873   873 E vendor.oplus.hardware.charger@1.0-service: notifyScreenStatus: 0
05-24 15:39:36.244   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.videotelephony@1.0::IVideoTelephony/default
05-24 15:39:36.248   836   836 I HidlServiceManagement: Registered android.hardware.audio@7.1::IDevicesFactory/default
05-24 15:39:36.249   836   836 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-24 15:39:36.250   836   836 I LegacySupport: Registration complete for android.hardware.audio@7.1::IDevicesFactory/default.
05-24 15:39:36.250   867   867 I HidlServiceManagement: Registered vendor.oplus.hardware.cammidasservice@1.0::IMIDASService/default
05-24 15:39:36.250   873   873 E vendor.oplus.hardware.charger@1.0-service: setChgStatusToBcc: 0
05-24 15:39:36.250   867   867 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.cammidasservice@1.0-service to cammidasservice@1.0-service.
05-24 15:39:36.251   867   867 I vendor.oplus.hardware.cammidasservice@1.0-service: midasservice register successfully
05-24 15:39:36.252   865   865 I HidlServiceManagement: Registered vendor.trustonic.tee@1.1::ITee/default
05-24 15:39:36.253   865   865 I HidlServiceManagement: Removing namespace from process name vendor.trustonic.tee@1.1-service to tee@1.1-service.
05-24 15:39:36.255   842   842 E android.hardware.gnss-service.mediatek: safe_sendto: safe_sendto() sendto() failed path=[mtk_hal2mnl] ret=-1 reason=[Connection refused]111
05-24 15:39:36.255   842   842 E android.hardware.gnss-service.mediatek: gps_device__get_gps_interface: hal2mnl_hal_reboot failed because of safe_sendto fail ,strerror:Connection refused 
05-24 15:39:36.255   825   825 I netd    : Initializing RouteController: 2433us
05-24 15:39:36.256   853   853 I HidlServiceManagement: Registered android.hardware.usb@1.3::IUsb/default
05-24 15:39:36.256   853   853 I HidlServiceManagement: Removing namespace from process name android.hardware.usb@1.3-service-mediatekv2 to usb@1.3-service-mediatekv2.
05-24 15:39:36.257   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_bt
05-24 15:39:36.259   840   840 I HidlServiceManagement: Registered android.hardware.drm@1.4::ICryptoFactory/widevine
05-24 15:39:36.259   840   840 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-24 15:39:36.260   843   843 I HidlServiceManagement: Registered android.hardware.graphics.allocator@4.0::IAllocator/default
05-24 15:39:36.260   844   844 W hwcomposer: [DRMDEV] 0xcccccccc[64] property[MSYNC2_0_ENABLE] does not do initialize  
05-24 15:39:36.260   844   844 W hwcomposer: [DRMDEV] 0xcccccccc[64] property[SKIP_CONFIG] does not do initialize  
05-24 15:39:36.260   844   844 W hwcomposer: [DRMDEV] failed to initialize crtc[0]: 64  
05-24 15:39:36.260   844   844 W hwcomposer: [DRMDEV] 0xcccccccc[88] property[MSYNC2_0_ENABLE] does not do initialize  
05-24 15:39:36.260   844   844 W hwcomposer: [DRMDEV] 0xcccccccc[88] property[SKIP_CONFIG] does not do initialize  
05-24 15:39:36.260   843   843 I HidlServiceManagement: Removing namespace from process name android.hardware.graphics.allocator@4.0-service-mediatek to allocator@4.0-service-mediatek.
05-24 15:39:36.260   844   844 W hwcomposer: [DRMDEV] failed to initialize crtc[1]: 88  
05-24 15:39:36.261   844   844 W hwcomposer: [DRMDEV] 0xcccccccc[99] property[MSYNC2_0_ENABLE] does not do initialize  
05-24 15:39:36.261   844   844 W hwcomposer: [DRMDEV] 0xcccccccc[99] property[SKIP_CONFIG] does not do initialize  
05-24 15:39:36.261   844   844 W hwcomposer: [DRMDEV] failed to initialize crtc[2]: 99  
05-24 15:39:36.261   844   844 W hwcomposer: [DRMDEV] failed to initialize all crtc: -19  
05-24 15:39:36.262   844   844 E hwcomposer: [DRMDEV] failed to initialize drm resource  
05-24 15:39:36.262   863   863 I HidlServiceManagement: Registered vendor.mediatek.hardware.nvram@1.1::INvram/default
05-24 15:39:36.263   863   863 I HidlServiceManagement: Removing namespace from process name vendor.mediatek.hardware.nvram@1.1-service to nvram@1.1-service.
05-24 15:39:36.264   877   877 I HidlServiceManagement: Registered vendor.oplus.hardware.performance@1.0::IPerformance/default
05-24 15:39:36.264   877   877 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.performance@1.0-service to performance@1.0-service.
05-24 15:39:36.265   844   844 I hwcomposer: [PqXmlParser] [PQ_SERVICE] mDeviceVersion = AMS644VA04_MTK04_20615  
05-24 15:39:36.265   844   844 I hwcomposer: [PqXmlParser] [PQ_SERVICE] mDeviceManufacture = samsung1024  
05-24 15:39:36.265   844   844 I hwcomposer: [PqXmlParser] [PQ_SERVICE] prjName:20662  
05-24 15:39:36.265   844   844 I hwcomposer: [PqXmlParser] init: failed to open file: /vendor/etc/cust_pq.xml  
05-24 15:39:36.267   865   865 I HidlServiceManagement: Registered vendor.trustonic.tee.tui@1.0::ITui/default
05-24 15:39:36.270   853   853 I HidlServiceManagement: Registered android.hardware.usb.gadget@1.1::IUsbGadget/default
05-24 15:39:36.270   853   853 I android.hardware.usb@1.3-service-mediatekv2: USB HAL Ready.
05-24 15:39:36.270   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_sensor
05-24 15:39:36.271   873   873 I HidlServiceManagement: Registered vendor.oplus.hardware.charger@1.0::ICharger/default
05-24 15:39:36.271   873   873 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.charger@1.0-service to charger@1.0-service.
05-24 15:39:36.272   847   847 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/acc_cali.json) open failed: -2 (No such file or directory)
05-24 15:39:36.273   873   873 E vendor.oplus.hardware.charger@1.0-service: ERR:Failed to get bms heating config file
05-24 15:39:36.273   873   873 E vendor.oplus.hardware.charger@1.0-service: can't parse config, rc=-1
05-24 15:39:36.276   847   847 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/gyro_cali.json) open failed: -2 (No such file or directory)
05-24 15:39:36.277   842   842 I HidlServiceManagement: Registered android.hardware.gnss@2.1::IGnss/default
05-24 15:39:36.277   842   842 I HidlServiceManagement: Removing namespace from process name android.hardware.gnss-service.mediatek to gnss-service.mediatek.
05-24 15:39:36.278   579   579 I hwservicemanager: Since vendor.mediatek.hardware.pq@2.14::IPictureQuality/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 15:39:36.279   844   844 E hwcomposer: [IPqDevice] Can't get PQ service tried (0) times  
05-24 15:39:36.281   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_network
05-24 15:39:36.282   876   876 I HidlServiceManagement: Registered vendor.oplus.hardware.oplusSensor@1.0::ISensorFeature/default
05-24 15:39:36.282   876   876 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.oplusSensor@1.0-service to oplusSensor@1.0-service.
05-24 15:39:36.284   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_ipaddr
05-24 15:39:36.285   825   825 I netd    : Initializing XfrmController: 29222us
05-24 15:39:36.285   883   883 I credstore: Registered binder service
05-24 15:39:36.285   844   844 I HidlServiceManagement: Registered vendor.mediatek.hardware.composer_ext@1.0::IComposerExt/default
05-24 15:39:36.285   844   844 I hwcomposer: [HWC] IComposerExt service registration completed.  
05-24 15:39:36.286   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_lbs
05-24 15:39:36.287   844   844 I HidlServiceManagement: Registered android.hardware.graphics.composer@2.3::IComposer/default
05-24 15:39:36.287   844   844 I HidlServiceManagement: Removing namespace from process name android.hardware.graphics.composer@2.3-service to composer@2.3-service.
05-24 15:39:36.288   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_framework2agps
05-24 15:39:36.288   847   847 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/als_cali.json) open failed: -2 (No such file or directory)
05-24 15:39:36.288   847   847 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/ps_cali.json) open failed: -2 (No such file or directory)
05-24 15:39:36.289   847   847 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/sar_cali.json) open failed: -2 (No such file or directory)
05-24 15:39:36.289   847   847 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/ois_cali.json) open failed: -2 (No such file or directory)
05-24 15:39:36.289   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_agps2framework
05-24 15:39:36.290   825   825 I resolv  : resolv_init: Initializing resolver
05-24 15:39:36.290   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mnld2nlputils
05-24 15:39:36.292   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mnld2debugService
05-24 15:39:36.292   836   836 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-24 15:39:36.293   836   836 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-24 15:39:36.295   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_debugService2mnld
05-24 15:39:36.297   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_meta2mnld
05-24 15:39:36.299   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_agpsd2debugService
05-24 15:39:36.301   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_debugService2agpsd
05-24 15:39:36.302   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/AgpsInterface
05-24 15:39:36.303   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/AgpsDebugInterface
05-24 15:39:36.303   825   825 I netd    : Registering NetdNativeService: 178us
05-24 15:39:36.304   825   825 I netd    : Registering MDnsService: 212us
05-24 15:39:36.304   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mnld2mtklogger
05-24 15:39:36.305   579   920 I hwservicemanager: Tried to start vendor.mediatek.hardware.pq@2.14::IPictureQuality/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 15:39:36.307   825   825 I HidlServiceManagement: Registered android.system.net.netd@1.1::INetd/default
05-24 15:39:36.307   825   825 I netd    : Registering NetdHwService: 3251us
05-24 15:39:36.308   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mtklogger2mnld
05-24 15:39:36.309   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lbs_log_v2s
05-24 15:39:36.311   847   847 I HidlServiceManagement: Registered android.hardware.sensors@2.0::ISensors/default
05-24 15:39:36.312   847   847 I HidlServiceManagement: Removing namespace from <NAME_EMAIL>6893 to sensors@2.0-service-multihal.mt6893.
05-24 15:39:36.321   916   916 E ccci_mdinit: (1):main, fail to open ccci_dump, err(Permission denied)
05-24 15:39:36.324   919   919 E ccci_mdinit: (3):main, fail to open ccci_dump, err(Permission denied)
05-24 15:39:36.326   916   916 I ccci_mdinit: (1):[main] drv_ver: 2
05-24 15:39:36.326   916   916 I ccci_mdinit: (1):[main] ccci_create_md_status_listen_thread
05-24 15:39:36.327   916   916 I ccci_mdinit: (1):md_init ver:2.30, sub:0, 1
05-24 15:39:36.327   916   916 I NVRAM   : MD1 set status: vendor.mtk.md1.status=init 
05-24 15:39:36.328   916   916 I ccci_mdinit: (1):MD0 set status: mtk.md0.status=init 
05-24 15:39:36.328   916   916 I NVRAM   : MD0 set status: mtk.md0.status=init 
05-24 15:39:36.328   916   916 E ccci_mdinit: (1):get property fail: ro.vendor.mtk_mipc_support
05-24 15:39:36.328   916   916 I ccci_mdinit: (1):service names: [init.svc.vendor.gsm0710muxd][init.svc.vendor.ril-daemon-mtk][init.svc.emdlogger1][init.svc.vendor.ccci_fsd]
05-24 15:39:36.328   916   916 I ccci_mdinit: (1):md_img_exist 0 0 0 0
05-24 15:39:36.328   916   916 I ccci_mdinit: (1):MD1 set status: mtk.md1.status=reset 
05-24 15:39:36.328   916   916 E ccci_mdinit: (1):[get_mdini_killed_state] error: get mdinit killed: 25(-1)
05-24 15:39:36.329   916   916 I ccci_mdinit: (1):MD_LOG_MEMDUMP_WAIT:ro.vendor.md_log_memdump_wait not exist, using default value
05-24 15:39:36.329   916   916 I ccci_mdinit: (1):MD_LOG_MEMDUMP_WAIT value: 0
05-24 15:39:36.329   916   916 I ccci_mdinit: (1):md0: mdl_mode=0
05-24 15:39:36.329   916   916 I ccci_mdinit: (1):check_nvram_ready(), property_get("vendor.service.nvram_init") = , read_nvram_ready_retry = 1
05-24 15:39:36.338   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: failed to get path of fd 3: No such file or directory
05-24 15:39:36.339   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(15): previous definition here
05-24 15:39:36.339   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(38): previous definition here
05-24 15:39:36.339   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(69): previous definition here
05-24 15:39:36.339   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(70): previous definition here
05-24 15:39:36.339   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(26): previous definition here
05-24 15:39:36.339   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(37): previous definition here
05-24 15:39:36.340   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(45): previous definition here
05-24 15:39:36.340   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(46): previous definition here
05-24 15:39:36.340   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(53): previous definition here
05-24 15:39:36.340   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(56): previous definition here
05-24 15:39:36.340   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(59): previous definition here
05-24 15:39:36.340   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(60): previous definition here
05-24 15:39:36.340   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(61): previous definition here
05-24 15:39:36.340   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(74): previous definition here
05-24 15:39:36.340   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(15): previous definition here
05-24 15:39:36.341   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(88): previous definition here
05-24 15:39:36.341   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(89): previous definition here
05-24 15:39:36.341   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(90): previous definition here
05-24 15:39:36.341   846   846 W android.hardware.media.c2@1.2-mediatek: libminijail[846]: compile_file: <fd>(91): previous definition here
05-24 15:39:36.348   846   846 I HidlServiceManagement: Registered android.hardware.media.c2@1.1::IComponentStore/default
05-24 15:39:36.348   846   846 I HidlServiceManagement: Removing namespace from process name android.hardware.media.c2@1.2-mediatek to c2@1.2-mediatek.
05-24 15:39:36.348   947   947 I TeeMcDaemon: Initialise Secure World [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/MobiCoreDriverDaemon.cpp:260]
05-24 15:39:36.349   947   947 W TeeMcDaemon: Cannot open key SO  (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:445]
05-24 15:39:36.349   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.configstore@1.0::ISurfaceFlingerConfigs/default in either framework or device VINTF manifest.
05-24 15:39:36.349   947   947 I TeeMcDaemon: Start services [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/MobiCoreDriverDaemon.cpp:325]
05-24 15:39:36.353   912   912 I SurfaceFlinger: Using HWComposer service: default
05-24 15:39:36.356   947   969 W TeeMcDaemon: Cannot open trustlet /data/vendor/mcRegistry/07050501000000000000000000000020.tlbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.360   654   657 W powerhal-libperfmgr: Connecting to PPS daemon failed (No such file or directory)
05-24 15:39:36.364   912   912 I SurfaceFlinger: SurfaceFlinger's main thread ready to run. Initializing graphics H/W...
05-24 15:39:36.367   841   841 I HidlServiceManagement: Registered android.hardware.gatekeeper@1.0::IGatekeeper/default
05-24 15:39:36.367   841   841 I HidlServiceManagement: Removing namespace from process name android.hardware.gatekeeper@1.0-service to gatekeeper@1.0-service.
05-24 15:39:36.368   947   969 W TeeMcDaemon: Cannot open trustlet /data/vendor/mcRegistry/08050500000000000000000000000000.tlbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.377   972   972 I gsid    : no DSU: No such file or directory
05-24 15:39:36.389   947   969 W TeeMcDaemon: Cannot open trustlet /data/vendor/mcRegistry/08050500000000000000000000000000.tlbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.390   836   836 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:39:36.415   882   882 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:39:36.416   882   882 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:39:36.417   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:39:36.434   882   882 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:39:36.434   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:39:36.435   882   882 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:39:36.440   882   882 W BatteryNotifier: batterystats service unavailable!
05-24 15:39:36.442   882   882 I ServiceManagerCppClient: Waiting for service 'media.metrics' on '/dev/binder'...
05-24 15:39:36.500   996   996 I bootstat: Service started: /system/bin/bootstat --set_system_boot_reason 
05-24 15:39:36.516   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.516   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.517   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.517   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.517   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.517   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.517   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.517   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.517   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.518   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.518   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.518   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.518   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.518   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.518   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.518   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.518   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.519   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.530   947   947 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/05160000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.530   947   947 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/020b0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.536   947   947 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/030b0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.536   947   947 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/03100000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.539   947   947 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/031c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.540   947   947 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/033c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.540   947   947 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/035c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.540   947   947 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/037c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.540   947   947 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/031c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.540   947   947 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/032c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.540   947   947 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/033c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.540   947   947 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/034c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.540   947   947 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/035c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.540   947   947 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/036c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.540   947   947 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/037c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.544   947   947 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/070c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.544   947   947 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/090b0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.544   947   947 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/0f5eed3c3b5a47afacca69a84bf0efad.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.544   947   947 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/07060000000000000000000000007169.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.544   947   947 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/4be4f7dc1f2c11e5b5f7727283247c7f.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.544   947   947 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/08070000000000000000000000008270.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.544   947   947 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/07070000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.544   947   947 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/07407000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.546   947   947 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/6b3f5fa0f8cf55a7be2582587d62d63a.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-24 15:39:36.559  1002  1002 W perfetto:          service.cc:232 Started traced, listening on /dev/socket/traced_producer /dev/socket/traced_consumer
05-24 15:39:36.562  1001  1001 I perfetto:           probes.cc:104 Starting /system/bin/traced_probes service
05-24 15:39:36.565  1001  1001 I perfetto:  probes_producer.cc:332 Connected to the service
05-24 15:39:36.594   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.atrace@1.0::IAtraceDevice/default in either framework or device VINTF manifest.
05-24 15:39:36.600   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.600   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.603   836   836 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 15:39:36.620   836   836 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x3d0b104f)
05-24 15:39:36.624  1012  1012 W MTK_FG_NVRAM: fd < 0, init first!
05-24 15:39:36.624  1012  1012 W MTK_FG_NVRAM: init failed, return!
05-24 15:39:36.625   836   836 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x9629b471)
05-24 15:39:36.627   836   836 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:39:36.628   836   836 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x65d323c3)
05-24 15:39:36.628  1012  1012 W MTK_FG_NVRAM: fd < 0, init first!
05-24 15:39:36.628  1012  1012 W MTK_FG_NVRAM: init failed, return!
05-24 15:39:36.628  1012  1012 W MTK_FG_NVRAM: fd < 0, init first!
05-24 15:39:36.628  1012  1012 W MTK_FG_NVRAM: init failed, return!
05-24 15:39:36.629   836   836 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x8db68e95)
05-24 15:39:36.630  1012  1012 W MTK_FG_NVRAM: fd < 0, init first!
05-24 15:39:36.630  1012  1012 W MTK_FG_NVRAM: init failed, return!
05-24 15:39:36.630  1012  1012 W MTK_FG_NVRAM: fd < 0, init first!
05-24 15:39:36.630  1012  1012 W MTK_FG_NVRAM: init failed, return!
05-24 15:39:36.630  1012  1012 W MTK_FG_NVRAM: fd < 0, init first!
05-24 15:39:36.630  1012  1012 W MTK_FG_NVRAM: init failed, return!
05-24 15:39:36.630  1012  1012 W MTK_FG_NVRAM: fd < 0, init first!
05-24 15:39:36.630  1012  1012 W MTK_FG_NVRAM: init failed, return!
05-24 15:39:36.630  1012  1012 W MTK_FG_NVRAM: fd < 0, init first!
05-24 15:39:36.630  1012  1012 W MTK_FG_NVRAM: init failed, return!
05-24 15:39:36.630  1012  1012 W MTK_FG_NVRAM: fd < 0, init first!
05-24 15:39:36.630  1012  1012 W MTK_FG_NVRAM: init failed, return!
05-24 15:39:36.630  1012  1012 W MTK_FG_NVRAM: fd < 0, init first!
05-24 15:39:36.630  1012  1012 W MTK_FG_NVRAM: init failed, return!
05-24 15:39:36.637   836   836 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-24 15:39:36.637   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:39:36.638   836   836 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-24 15:39:36.640   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:39:36.641   836   836 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-24 15:39:36.642   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:39:36.643   836   836 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-24 15:39:36.643   836   836 W audiohalservice: Could not register Bluetooth Audio API
05-24 15:39:36.643   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-24 15:39:36.644   836   836 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-24 15:39:36.644   836   836 W audiohalservice: Could not register Bluetooth Audio Offload API
05-24 15:39:36.653   828   828 I zygote  : Initializing ART runtime metrics
05-24 15:39:36.653   827   827 I zygote64: Initializing ART runtime metrics
05-24 15:39:36.679   836   836 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-24 15:39:36.685   836   836 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-24 15:39:36.685   836   836 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-24 15:39:36.692  1017  1017 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 15:39:36.692  1017  1017 E mnld_pwr_interface: mnld_pwr_init: mnld_pwr_open failed, No such file or directory
05-24 15:39:36.692  1017  1017 E MNL2AGPS: bind_udp_socket: bind failed path=[/data/agps_supl/agps_to_mnl] reason=[No such file or directory]
05-24 15:39:36.693  1017  1017 E mtk_lbs_utility: init_timer_id_alarm: timerfd_create  CLOCK_BOOTTIME_ALARM 
05-24 15:39:36.694  1017  1017 E MNLD    : mnld_init: mnl2hal_release_wakelock failed because of safe_sendto fail ,strerror:Connection refused 
05-24 15:39:36.694  1017  1017 E MNLD    : mnld_init: mnl2hal_mnld_reboot failed because of safe_sendto fail ,strerror:Connection refused 
05-24 15:39:36.694  1017  1017 E mnld    : mtk_socket_connect_local: mtk_socket_connect_local() connect() failed reason=[Connection refused]111 for path=[mtk_mnld2debugService]
05-24 15:39:36.697  1017  1056 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 15:39:36.697  1017  1056 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:0
05-24 15:39:36.720  1018  1018 I cameraserver: ServiceManager: 0xb4000079becf5e80
05-24 15:39:36.720  1018  1018 I CameraService: CameraService started (pid=1018)
05-24 15:39:36.720  1018  1018 I CameraService: CameraService process starting
05-24 15:39:36.721  1018  1018 W BatteryNotifier: batterystats service unavailable!
05-24 15:39:36.721  1018  1018 W BatteryNotifier: batterystats service unavailable!
05-24 15:39:36.723   579   579 I hwservicemanager: Since android.hardware.camera.provider@2.4::ICameraProvider/internal/0 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 15:39:36.724  1010  1010 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-neuron
05-24 15:39:36.724  1018  1018 W CameraProviderManager: tryToInitializeHidlProviderLocked: HIDL Camera provider HAL 'internal/0' is not actually available
05-24 15:39:36.724  1010  1010 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-24 15:39:36.724  1010  1010 I ANNService: Registered service for mtk-neuron
05-24 15:39:36.725  1010  1010 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-dsp
05-24 15:39:36.726  1010  1010 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-24 15:39:36.726  1010  1010 I ANNService: Registered service for mtk-dsp
05-24 15:39:36.726  1018  1018 W CameraProviderManager: tryToInitializeAidlProviderLocked: AIDL Camera provider HAL 'android.hardware.camera.provider.ICameraProvider/virtual/0' is not actually available
05-24 15:39:36.727  1010  1010 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-mdla
05-24 15:39:36.728  1010  1010 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-24 15:39:36.729  1010  1010 I ANNService: Registered service for mtk-mdla
05-24 15:39:36.730  1010  1010 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-gpu
05-24 15:39:36.730  1010  1010 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-24 15:39:36.731  1010  1010 I ANNService: Registered service for mtk-gpu
05-24 15:39:36.732  1018  1018 I HidlServiceManagement: Registered android.frameworks.cameraservice.service@2.2::ICameraService/default
05-24 15:39:36.733  1018  1018 I CameraService: CameraService pinged cameraservice proxy
05-24 15:39:36.734  1018  1018 I cameraserver: ServiceManager: 0xb4000079becf5e80 done instantiate
05-24 15:39:36.736  1067  1067 I HidlServiceManagement: Registered android.system.wifi.keystore@1.0::IKeystore/default
05-24 15:39:36.742  1010  1010 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.apusys@2.1::INeuronApusys/default
05-24 15:39:36.744   882   882 I ServiceManagerCppClient: Waiting for service 'media.metrics' on '/dev/binder' successful after waiting 302ms
05-24 15:39:36.745   882   882 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-24 15:39:36.746  1024  1044 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder'...
05-24 15:39:36.750  1023  1023 I main_extractorservice: enable media.extractor memory limits
05-24 15:39:36.754  1010  1010 I apuware_server: Start NeuronXrp 2.0 service 
05-24 15:39:36.759  1023  1023 W mediaextractor: libminijail[1023]: failed to get path of fd 5: No such file or directory
05-24 15:39:36.759  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(38): previous definition here
05-24 15:39:36.759   882   882 E libxml2 : I/O warning : failed to load "/odm/etc/virtual_audio_policy_configuration.xml": No such file or directory
05-24 15:39:36.759   882   882 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-24 15:39:36.759   882   882 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-24 15:39:36.760  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(18): previous definition here
05-24 15:39:36.760  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(6): previous definition here
05-24 15:39:36.760  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(27): previous definition here
05-24 15:39:36.760  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(29): previous definition here
05-24 15:39:36.760  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(28): previous definition here
05-24 15:39:36.760  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(4): previous definition here
05-24 15:39:36.760  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(32): previous definition here
05-24 15:39:36.760  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(12): previous definition here
05-24 15:39:36.760  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(9): previous definition here
05-24 15:39:36.760  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(8): previous definition here
05-24 15:39:36.760  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(23): previous definition here
05-24 15:39:36.760  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(41): previous definition here
05-24 15:39:36.760  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(25): previous definition here
05-24 15:39:36.760  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(56): previous definition here
05-24 15:39:36.760  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(5): previous definition here
05-24 15:39:36.760  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(14): previous definition here
05-24 15:39:36.760  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(13): previous definition here
05-24 15:39:36.761  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(11): previous definition here
05-24 15:39:36.761  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(15): previous definition here
05-24 15:39:36.761  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(16): previous definition here
05-24 15:39:36.761  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(10): previous definition here
05-24 15:39:36.761  1010  1010 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.xrp@2.0::INeuronXrp/default
05-24 15:39:36.762  1023  1023 W mediaextractor: libminijail[1023]: compile_file: /apex/com.android.media/etc/seccomp_policy/crash_dump.arm64.policy(7): previous definition here
05-24 15:39:36.762  1023  1023 W mediaextractor: libminijail[1023]: compile_file: <fd>(56): previous definition here
05-24 15:39:36.762  1023  1023 W mediaextractor: libminijail[1023]: compile_file: /apex/com.android.media/etc/seccomp_policy/crash_dump.arm64.policy(6): previous definition here
05-24 15:39:36.766  1010  1010 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.hmp@1.0::IApuwareHmp/default
05-24 15:39:36.771  1079  1079 I thermal_repeater: RilRPC_init 
05-24 15:39:36.772  1079  1079 I thermal_repeater: RilRPC_init dlopen fail: dlopen failed: library "librpcril.so" not found 
05-24 15:39:36.773  1010  1010 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.utils@2.0::IApuwareUtils/default
05-24 15:39:36.774   579  1078 I hwservicemanager: Tried to start android.hardware.camera.provider@2.4::ICameraProvider/internal/0 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 15:39:36.779  1045  1045 I storaged: Unable to get AIDL health service, trying HIDL...
05-24 15:39:36.785  1045  1045 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder'...
05-24 15:39:36.789  1085  1085 I thermal_src1: ta_daemon_init
05-24 15:39:36.791  1008  1008 I vtservice: [VT][SRV]ServiceManager: 0xb400007981707a50
05-24 15:39:36.791  1008  1008 I vtservice: [VT][SRV]before VTService_instantiate
05-24 15:39:36.802   836   836 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-24 15:39:36.803   836   836 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-24 15:39:36.812  1087  1115 I MtkAgpsNative: Enter mtk_agps_up_init
05-24 15:39:36.815  1087  1115 E agps    : [agps] ERR: [MNL] bind failed path=[/data/agps_supl/mnl_to_agps] reason=[No such file or directory]
05-24 15:39:36.815  1085  1085 I thermal_src: u_CATM_ON == -1, get catm init val again
05-24 15:39:36.815  1085  1085 I thermal_src: ta_catm_init_flow
05-24 15:39:36.815  1085  1085 I thermal_src: u_CATM_ON == -1, get catm init val
05-24 15:39:36.825  1087  1115 E agps    : [agps] ERR: [Default] mtk_expat_xml_load2() failed [fopen() No such file or directory] file [/data/vendor/agps_supl/agps_profiles_conf2.xml]
05-24 15:39:36.826  1087  1115 E agps    : [agps] ERR: [Default] mtk_expat_xml_load2() failed [fopen() No such file or directory] file [/data/agps_supl/agps_profiles_conf2.xml]
05-24 15:39:36.826  1087  1115 E agps    : [agps] ERR: [Default] mtk_expat_xml_load2() failed [fopen() No such file or directory] file [/vendor/etc/agps_profiles_conf2.xml]
05-24 15:39:36.828   836   836 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:39:36.829   916   916 E ccci_mdinit: (1):fail to open /mnt/vendor/nvdata/APCFG/APRDCL/CXP_SBP: 2
05-24 15:39:36.829   916   916 I ccci_mdinit: (1):get_cip_sbp_setting, file /custom/etc/firmware/CIP_MD_SBP NOT exists!
05-24 15:39:36.829   916   916 I ccci_mdinit: (1):PRJ_SBP_ID:ro.vendor.mtk_md_sbp_custom_value not exist, using default value
05-24 15:39:36.830  1068  1068 I android.hardware.media.omx@1.0-service: mediacodecservice starting
05-24 15:39:36.833  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: failed to get path of fd 5: No such file or directory
05-24 15:39:36.833  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: allowing syscall: clock_gettime
05-24 15:39:36.833  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: allowing syscall: connect
05-24 15:39:36.833  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: allowing syscall: fcntl64
05-24 15:39:36.833  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: allowing syscall: socket
05-24 15:39:36.833  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: allowing syscall: writev
05-24 15:39:36.834  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(59): syscall getrandom redefined here
05-24 15:39:36.834  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(15): previous definition here
05-24 15:39:36.834  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(1): syscall read redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(9): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(2): syscall write redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(5): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(3): syscall exit redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(40): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(4): syscall rt_sigreturn redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(45): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(6): syscall exit_group redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(44): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(7): syscall clock_gettime redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(7): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(8): syscall gettimeofday redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(47): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(9): syscall futex redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(3): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(10): syscall getrandom redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(15): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(13): syscall ppoll redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(13): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(14): syscall pipe2 redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(46): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(15): syscall openat redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(30): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(16): syscall dup redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(12): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(17): syscall close redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(10): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(18): syscall lseek redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(50): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(19): syscall getdents64 redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(58): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(20): syscall faccessat redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(38): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(27): syscall rt_sigprocmask redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(41): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(30): syscall prctl redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(6): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(31): syscall madvise redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(29): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(32): syscall mprotect redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(28): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(33): syscall munmap redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(27): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(34): syscall getuid32 redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(34): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(35): syscall fstat64 redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(35): previous definition here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(36): syscall mmap2 redefined here
05-24 15:39:36.835  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(14): previous definition here
05-24 15:39:36.836  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(66): syscall getpid redefined here
05-24 15:39:36.836  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(11): previous definition here
05-24 15:39:36.836   916   916 I ccci_mdinit: (1):SBP_SUB_ID:persist.vendor.operator.subid not exist
05-24 15:39:36.836   916   916 I ccci_mdinit: (1):set md boot data:mdl=0 sbp=0 dbg_dump=-1 sbp_sub=0
05-24 15:39:36.836  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(67): syscall gettid redefined here
05-24 15:39:36.836  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(12): previous definition here
05-24 15:39:36.836  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(74): syscall recvfrom redefined here
05-24 15:39:36.836  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(22): previous definition here
05-24 15:39:36.836  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(77): syscall sched_getaffinity redefined here
05-24 15:39:36.836  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(75): previous definition here
05-24 15:39:36.836  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(82): syscall sysinfo redefined here
05-24 15:39:36.836  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(24): previous definition here
05-24 15:39:36.836  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: <fd>(83): syscall setsockopt redefined here
05-24 15:39:36.836  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(23): previous definition here
05-24 15:39:36.836  1068  1068 W android.hardware.media.omx@1.0-service: libminijail[1068]: logging seccomp filter failures
05-24 15:39:36.836   916   916 E ccci_mdinit: [SYSENV]get_env_info():240 , env_buffer[0] : 0xb400007c28b37040
05-24 15:39:36.837   916   916 I ccci_mdinit: (1):get md_type (null)
05-24 15:39:36.837   916   916 I ccci_mdinit: (1):MD1 set status: mtk.md1.status=bootup 
05-24 15:39:36.837   916   916 I ccci_mdinit: (1):md_id = 0; mdstatusfd = -1
05-24 15:39:36.838  1087  1115 E mtk_socket: ERR: mtk_socket_connect_local() connect() failed reason=[Connection refused]111 for path=[mtk_agpsd2debugService]
05-24 15:39:36.838  1087  1115 E agps    : [agps] ERR: [CP] get_ccci_uart  open failed node=[/dev/ccci2_tty2] reason=[No such file or directory]
05-24 15:39:36.838  1087  1115 E agps    :  ERR: [AGPS2WIFI] bind failed path=[/data/agps_supl/wifi_2_agps] reason=[No such file or directory]
05-24 15:39:36.838  1087  1115 E agps    : [agps] ERR: [WIFI] wifi_mgr_init  create_wifi2agps_fd failed
05-24 15:39:36.855  1146  1146 I KernelSU Next: ksud::cli: command: Services
05-24 15:39:36.855  1146  1146 I KernelSU Next: ksud::init_event: on_services triggered!
05-24 15:39:36.856  1146  1146 I KernelSU Next: ksud::module: /data/adb/service.d not exists, skip
05-24 15:39:36.856  1068  1068 I HidlServiceManagement: Registered android.hardware.media.omx@1.0::IOmx/default
05-24 15:39:36.856  1077  1077 I ULog    : ULog initialized: mode=0x1  filters: req=0x0 func=0x0/0x0 details=0xfffff000 level=3
05-24 15:39:36.857  1068  1068 I HidlServiceManagement: Removing namespace from process name android.hardware.media.omx@1.0-service to omx@1.0-service.
05-24 15:39:36.858  1068  1068 I android.hardware.media.omx@1.0-service: IOmx HAL service created.
05-24 15:39:36.860  1113  1113 E android.hardware.biometrics.fingerprint@2.1-service: fingerprint hwbinder service starting
05-24 15:39:36.861  1113  1113 I android.hardware.biometrics.fingerprint@2.1-service: read_proc_string_data sys_buffer_data=G_OPTICAL_G3S #end
05-24 15:39:36.862  1113  1113 E android.hardware.biometrics.fingerprint@2.1-service: fp read fp_id_string = G_OPTICAL_G3S
05-24 15:39:36.862  1113  1113 E android.hardware.biometrics.fingerprint@2.1-service: optical goodix fingerprint
05-24 15:39:36.863  1068  1068 E MediaCodecsXmlParser: Cannot find the role for a decoder of type audio/x-adpcm-dvi-ima
05-24 15:39:36.863  1068  1068 E MediaCodecsXmlParser: Cannot find the role for a decoder of type audio/x-adpcm-ms
05-24 15:39:36.863  1068  1068 E MediaCodecsXmlParser: Cannot find the role for a decoder of type audio/alac
05-24 15:39:36.863  1068  1068 E MediaCodecsXmlParser: Cannot find the role for a decoder of type audio/ape
05-24 15:39:36.863  1068  1068 I OmxStore: node [OMX.MTK.AUDIO.DECODER.GSM] not found in IOmx
05-24 15:39:36.863  1068  1068 I OmxStore: node [OMX.MTK.AUDIO.DECODER.MP3] not found in IOmx
05-24 15:39:36.863  1113  1113 I android.hardware.biometrics.fingerprint@2.1-service: fpSettingsInit project name:0
05-24 15:39:36.863  1113  1113 I android.hardware.biometrics.fingerprint@2.1-service: read_proc_string_data sys_buffer_data=Device version:		AMS644VA04_MTK04_20615
05-24 15:39:36.863  1113  1113 I android.hardware.biometrics.fingerprint@2.1-service: Device manufacture:		samsung1024
05-24 15:39:36.863  1113  1113 I android.hardware.biometrics.fingerprint@2.1-service:  #end
05-24 15:39:36.863  1113  1113 I android.hardware.biometrics.fingerprint@2.1-service: fpSettingsInit lcd type:1
05-24 15:39:36.864  1113  1113 I android.hardware.biometrics.fingerprint@2.1-service: fpSettingsInit select config index is :0
05-24 15:39:36.864  1089  1089 I VPUD    : vdec_codec_service_init() block mode
05-24 15:39:36.864  1089  1089 I VPUD    : venc_codec_service_init()
05-24 15:39:36.864  1089  1089 I VPUD    : -- send_init_fin
05-24 15:39:36.864  1089  1186 I VPUD    : venc service entry TID = 1186
05-24 15:39:36.864  1089  1185 I VPUD    : vdec_service_entry()
05-24 15:39:36.865  1113  1113 I android.hardware.biometrics.fingerprint@2.1-service: do nothing
05-24 15:39:36.866   579   579 I hwservicemanager: Since vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 15:39:36.872  1113  1113 E [GF_HAL][HalContext]: [init], init with G3 HAL.
05-24 15:39:36.877   579  1188 I hwservicemanager: Tried to start vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 15:39:36.880   853   853 I android.hardware.usb@1.3-service-mediatekv2: setCurrentUsbFunctions: skip first time for usbd
05-24 15:39:36.880   853   853 I android.hardware.usb@1.3-service-mediatekv2: Usb Gadget setcurrent functions failed
05-24 15:39:36.882  1068  1068 I HidlServiceManagement: Registered android.hardware.media.omx@1.0::IOmxStore/default
05-24 15:39:36.883  1068  1068 I HidlServiceManagement: Removing namespace from process name android.hardware.media.omx@1.0-service to omx@1.0-service.
05-24 15:39:36.883   579   579 I hwservicemanager: Since vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 15:39:36.884  1104  1104 I HidlServiceManagement: getService: Trying again for vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default...
05-24 15:39:36.888   579  1191 I hwservicemanager: Tried to start vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 15:39:36.895   912   912 I HidlServiceManagement: Registered android.frameworks.displayservice@1.0::IDisplayService/default
05-24 15:39:36.906   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.906   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.906   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.906   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.906   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.906   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.906   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.906   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.907   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.907   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.907   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.907   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.907   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.907   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.907   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.907   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.907   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.908   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.945  1042  1042 I mediaserver: ServiceManager: 0xf21c2b00
05-24 15:39:36.946  1042  1042 W BatteryNotifier: batterystats service unavailable!
05-24 15:39:36.947  1042  1042 W BatteryNotifier: batterystats service unavailable!
05-24 15:39:36.948  1088  1088 I HidlServiceManagement: Registered vendor.mediatek.hardware.pq@2.15::IPictureQuality/default
05-24 15:39:36.948  1088  1088 I HidlServiceManagement: Removing namespace from process name vendor.mediatek.hardware.pq@2.2-service to pq@2.2-service.
05-24 15:39:36.957   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.957   836   836 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:36.961   836   836 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 15:39:36.986   836   836 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x95b1578d)
05-24 15:39:36.987   836  1231 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-24 15:39:36.991   836  1231 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0x95b1578d)
05-24 15:39:36.992   836  1231 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0x95b1578d)
05-24 15:39:36.994  1088  1226 E PQ      : [PQ_SERVICE] aisdr2hdr_pqindex is not found in cust_color.xml
05-24 15:39:37.005  1088  1226 I vendor.mediatek.hardware.pq@2.2-service: transferAIOutput(), register trs callback
05-24 15:39:37.062  1112  1112 I NetdagentFirewall: setupIptablesHooks done in oem_iptables_init
05-24 15:39:37.062  1112  1112 I NetdagentController: Initializing iptables: 204.4ms
05-24 15:39:37.062  1112  1112 I Netdagent:  Create CommandService  successfully
05-24 15:39:37.065  1112  1241 I HidlServiceManagement: Registered vendor.mediatek.hardware.netdagent@1.0::INetdagent/default
05-24 15:39:37.081   836   836 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-24 15:39:37.089   836   836 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 15:39:37.091   836   836 W AudioSmartPaController: init(), dlopen failed, dlerror = dlopen failed: library "libfile_op.so" not found: needed by /vendor/lib/libnxp_extamp_intf.so in namespace (default)
05-24 15:39:37.108   836   836 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-24 15:39:37.129   836   836 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 15:39:37.129   836   836 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 15:39:37.129   836   836 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 15:39:37.133   836   836 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 15:39:37.133   836   836 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 15:39:37.135   836   836 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-24 15:39:37.143   836   836 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-24 15:39:37.144   836   836 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-24 15:39:37.146   882   882 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 15:39:37.147   882   882 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 15:39:37.149   836   888 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-24 15:39:37.149   836   888 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-24 15:39:37.150   827   827 E ActivityRecognitionHardware: activity_recognition HAL is deprecated. class_init is effectively a no-op
05-24 15:39:37.150   836   888 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 15:39:37.151   882   882 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-24 15:39:37.152   882   882 I AudioFlinger: openOutput() this 0xb40000720f699900, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-24 15:39:37.155   882   882 I AudioFlinger: operator(): Using 3000 ms as standby time
05-24 15:39:37.156   882   882 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 15:39:37.157   882   882 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:39:37.158   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:39:37.159   882   882 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:39:37.177   836   888 E EffectsFactoryConfigLoader: loadLibrary Could not find library in effect directories: libaudiopreprocessing_mtk.so
05-24 15:39:37.178   836   888 E EffectsFactoryConfigLoader: EffectLoadXmlEffectConfig 4 errors during loading of configuration: /vendor/etc/audio_effects.xml
05-24 15:39:37.184  1090  1090 I mediaswcodec: media swcodec service starting
05-24 15:39:37.185   882  1258 I AudioFlinger: AudioFlinger's thread 0xb4000072d2717760 tid=1258 ready to run
05-24 15:39:37.186  1090  1090 W mediaswcodec: libminijail[1090]: failed to get path of fd 5: No such file or directory
05-24 15:39:37.186  1090  1090 W mediaswcodec: libminijail[1090]: compile_file: <fd>(39): previous definition here
05-24 15:39:37.188  1090  1090 I CodecServiceRegistrant: Creating software Codec2 service...
05-24 15:39:37.195  1090  1090 I HidlServiceManagement: Registered android.hardware.media.c2@1.2::IComponentStore/software
05-24 15:39:37.197  1090  1090 I CodecServiceRegistrant: Preferred Codec2 HIDL store is set to "default".
05-24 15:39:37.197  1090  1090 I CodecServiceRegistrant: Software Codec2 service created and registered.
05-24 15:39:37.211   916   959 E ccci_fsd(1): FS_OTP_init:otp_get_size:1048576, status=0, type=0!
05-24 15:39:37.225   836  1244 W audio_messenger_ipi: read_ipi_thread(), read dsp fail!! length_read: 0, errno: 0
05-24 15:39:37.233  1266  1266 E DEBUG   : failed to read process info: failed to open /proc/836: No such file or directory
05-24 15:39:37.252   827   827 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlags
05-24 15:39:37.252   827   827 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlagsImpl
05-24 15:39:37.252   827   827 W Zygote  : Class not found for preloading: android.media.audio.Flags
05-24 15:39:37.293  1008  1008 W AVSync  : initFD done, g_fd_name: /dev/ccci_imsdc
05-24 15:39:37.294   577   577 I auditd  : avc:  denied  { find } for pid=1008 uid=1000 name=vendor.mediatek.hardware.videotelephony.IVideoTelephony/default scontext=u:r:vtservice:s0 tcontext=u:object_r:default_android_service:s0 tclass=service_manager permissive=0
05-24 15:39:37.294  1008  1286 W ServiceManagerCppClient: Failed to get isDeclared for vendor.mediatek.hardware.videotelephony.IVideoTelephony/default: Status(-1, EX_SECURITY): 'SELinux denied for service.'
05-24 15:39:37.337  1266  1266 F DEBUG   : Cmdline: /vendor/bin/hw/android.hardware.audio.service
05-24 15:39:37.337  1266  1266 F DEBUG   : pid: 836, tid: 836, name: binder:836_2  >>> /vendor/bin/hw/android.hardware.audio.service <<<
05-24 15:39:37.337  1266  1266 F DEBUG   :       #01 pc 0002651d  /vendor/lib/hw/<EMAIL> (android::hardware::audio::V7_1::implementation::StreamOut::setLatencyModeCallback(android::sp<android::hardware::audio::V7_1::IStreamOutLatencyModeCallback> const&)+36) (BuildId: 544a581a8a5464266e14c74f5aea9a1c)
05-24 15:39:37.337  1266  1266 F DEBUG   :       #02 pc 00043c0f  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::_hidl_setLatencyModeCallback(android::hidl::base::V1_0::BnHwBase*, android::hardware::Parcel const&, android::hardware::Parcel*, std::__1::function<void (android::hardware::Parcel&)>)+198) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 15:39:37.337  1266  1266 F DEBUG   :       #03 pc 00044703  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::onTransact(unsigned int, android::hardware::Parcel const&, android::hardware::Parcel*, unsigned int, std::__1::function<void (android::hardware::Parcel&)>)+1926) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 15:39:37.337  1266  1266 F DEBUG   :       #06 pc 00002d23  /vendor/bin/hw/android.hardware.audio.service (main+3150) (BuildId: 4f8cce90cd7ff9b304f84e6fd8ec6b0b)
05-24 15:39:37.337  1266  1266 F DEBUG   :       #07 pc 00036457  /apex/com.android.runtime/lib/bionic/libc.so (__libc_init+74) (BuildId: a6009addd32ae1399831564e0073b350)
05-24 15:39:37.359   916   959 W ccci_mdinit: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.system.suspend.ISystemSuspend/default
05-24 15:39:37.378   882   986 E HalDeathHandler: HAL server crashed, audio server is restarting
05-24 15:39:37.387  1077  1077 I mtkcam-devicemgr: [initialize] +
05-24 15:39:37.589  1113  1113 E [GF_HAL][ShenzhenSensor]: [init] gainvalue: 150/100
05-24 15:39:37.589  1113  1113 E [GF_HAL][ShenzhenSensor]: [init] expotime 38
05-24 15:39:37.589  1113  1113 E [GF_HAL][ShenzhenSensor]: [init] @@@@@ mQRCode=Z918095013A0061493,len=18
05-24 15:39:37.590  1113  1113 E [GF_HAL][ShenzhenSensor]: [init] module_type = 0x6
05-24 15:39:37.590  1113  1113 E [GF_HAL][ShenzhenSensor]: [init] lens_type = 0xa
05-24 15:39:37.681  1017  1030 E mnld    : thread_adc_capture_init: set IOCTL_EMI_MEMORY_INIT failed,(Success)
05-24 15:39:37.697  1017  1056 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 15:39:37.697  1017  1056 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:1
05-24 15:39:37.884  1104  1104 W HidlServiceManagement: Waited one second for vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default
05-24 15:39:37.885   579   579 I hwservicemanager: Since vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 15:39:37.885  1104  1104 I HidlServiceManagement: getService: Trying again for vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default...
05-24 15:39:37.886   579  1313 I hwservicemanager: Tried to start vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 15:39:38.003   826   830 I ServiceManagerCppClient: Waiting for service 'statscompanion' on '/dev/binder'...
05-24 15:39:38.026  1113  1113 E [GF_HAL][FingerprintCore]: [init_report_data] algo version is V03.02.02.230.005
05-24 15:39:38.026  1113  1113 E [GF_HAL][FingerprintCore]: [init_report_data] lcdtype_prop = SDC
05-24 15:39:38.026  1113  1113 E [GF_HAL][FingerprintCore]: [init_report_data] type = V03.02.02.230.005_S_SDC
05-24 15:39:38.055  1113  1113 I HidlServiceManagement: Registered vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default
05-24 15:39:38.055  1113  1113 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.biometrics.fingerprint@2.1-service to fingerprint@2.1-service.
05-24 15:39:38.057   579   579 W hwservicemanager: Detected instance of android.hardware.biometrics.fingerprint@2.3::IBiometricsFingerprint (pid: 1104) registering over instance of or with base of android.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint (pid: 1113).
05-24 15:39:38.058  1104  1104 I HidlServiceManagement: Registered android.hardware.biometrics.fingerprint@2.3::IBiometricsFingerprint/default
05-24 15:39:38.058  1104  1104 I HidlServiceManagement: Removing namespace from <NAME_EMAIL>6893 to fingerprint@2.3-service.mt6893.
05-24 15:39:38.140  1307  1307 E system_server: memevent listener failed to initialize, not supported kernel
05-24 15:39:38.147  1307  1307 W Binder  : 	at android.os.IServiceManager$Stub$Proxy.checkService(IServiceManager.java:507)
05-24 15:39:38.147  1307  1307 W Binder  : 	at android.os.ServiceManagerProxy.checkService(ServiceManagerNative.java:73)
05-24 15:39:38.147  1307  1307 W Binder  : 	at android.os.ServiceManagerProxy.getService2(ServiceManagerNative.java:69)
05-24 15:39:38.147  1307  1307 W Binder  : 	at android.os.ServiceManager.rawGetService(ServiceManager.java:430)
05-24 15:39:38.147  1307  1307 W Binder  : 	at android.os.ServiceManager.getService(ServiceManager.java:175)
05-24 15:39:38.147  1307  1307 W Binder  : 	at android.app.ActivityThread.initializeSystemThread(ActivityThread.java:8715)
05-24 15:39:38.147  1307  1307 W Binder  : 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:597)
05-24 15:39:38.147  1307  1307 W Binder  : 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:889)
05-24 15:39:38.153  1307  1307 I SystemServerInitThreadPool: Creating instance with 8 threads
05-24 15:39:38.160  1307  1339 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-com.android.providers.media.module.xml
05-24 15:39:38.161  1307  1339 I SystemConfig: Reading permissions from /system/etc/sysconfig/app.grapheneos.gmscompat.xml
05-24 15:39:38.161  1307  1339 I SystemConfig: Reading permissions from /system/etc/sysconfig/app.grapheneos.networklocation.xml
05-24 15:39:38.161  1307  1339 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-platform-full-base.xml
05-24 15:39:38.162  1307  1339 I SystemConfig: Reading permissions from /system/etc/sysconfig/framework-sysconfig.xml
05-24 15:39:38.162  1307  1339 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-asl-files.xml
05-24 15:39:38.162  1307  1339 I SystemConfig: Reading permissions from /system/etc/sysconfig/hiddenapi-whitelist-co.aospa.sense.xml
05-24 15:39:38.163  1307  1339 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-strict-signature.xml
05-24 15:39:38.163  1307  1339 I SystemConfig: Reading permissions from /system/etc/sysconfig/backup.xml
05-24 15:39:38.163  1307  1339 I SystemConfig: Reading permissions from /system/etc/sysconfig/hiddenapi-package-whitelist.xml
05-24 15:39:38.163  1307  1339 I SystemConfig: Reading permissions from /system/etc/sysconfig/enhanced-confirmation.xml
05-24 15:39:38.164  1307  1339 I SystemConfig: Reading permissions from /system/etc/sysconfig/package-shareduid-allowlist.xml
05-24 15:39:38.164  1307  1339 I SystemConfig: Reading permissions from /system/etc/sysconfig/initial-package-stopped-states.xml
05-24 15:39:38.165  1307  1339 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-platform-handheld-system.xml
05-24 15:39:38.165  1307  1339 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-platform.xml
05-24 15:39:38.167  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/DigitalWellbeing.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.167  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/GoogleContacts.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.167  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/GoogleContactsSyncAdapter.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.167  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/Drive.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.167  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/GoogleMaps.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.168  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.intentresolver.xml
05-24 15:39:38.168  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/GoogleCalendar.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.168  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/CarrierServices.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.168  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/GoogleDialer.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.168  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/GoogleOneTimeInitializer.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.168  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.documentsui.xml
05-24 15:39:38.169  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/app.grapheneos.networklocation.xml
05-24 15:39:38.170  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/DeviceHealthServices.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.170  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/ExtraFiles.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.170  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/org.apache.http.legacy.xml
05-24 15:39:38.170  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.live_wallpaper.xml
05-24 15:39:38.171  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/GooglePlayStore.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.171  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/privapp-permissions-mediatek.xml
05-24 15:39:38.171  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/GBoard.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.171  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/GoogleKeep.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.171  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/GoogleRestore.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.171  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.nfc_extras.xml
05-24 15:39:38.172  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.networkstack.xml
05-24 15:39:38.172  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.credentials.xml
05-24 15:39:38.173  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/privapp-permissions-platform.xml
05-24 15:39:38.176  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/android.test.mock.xml
05-24 15:39:38.177  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/android.test.base.xml
05-24 15:39:38.177  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.webview.xml
05-24 15:39:38.178  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/android.hardware.biometrics.face.xml
05-24 15:39:38.178  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/GoogleMessages.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.178  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/app.grapheneos.logviewer.xml
05-24 15:39:38.179  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/GoogleServicesFramework.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.179  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/GoogleCalendarSyncAdapter.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.179  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/javax.obex.xml
05-24 15:39:38.179  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.location.provider.xml
05-24 15:39:38.180  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/GoogleLocationHistory.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.180  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/android.test.runner.xml
05-24 15:39:38.180  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/GooglePhotos.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.180  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.media.remotedisplay.xml
05-24 15:39:38.181  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.sip.voip.xml
05-24 15:39:38.181  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.window_magnification.xml
05-24 15:39:38.181  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/GoogleClock.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.181  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/GoogleCalculator.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.181  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.future.usb.accessory.xml
05-24 15:39:38.183  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.mediadrm.signer.xml
05-24 15:39:38.184  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/privapp_whitelist_co.aospa.sense.xml
05-24 15:39:38.185  1307  1339 I SystemConfig: Non-xml file /system/etc/permissions/GmsCore.prop in /system/etc/permissions directory, ignoring
05-24 15:39:38.185  1307  1339 I SystemConfig: Reading permissions from /system/etc/permissions/platform.xml
05-24 15:39:38.187  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.ese.xml
05-24 15:39:38.188  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.xml
05-24 15:39:38.188  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.location.gps.xml
05-24 15:39:38.189  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.multitouch.distinct.xml
05-24 15:39:38.189  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.verified_boot.xml
05-24 15:39:38.190  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.mediatek.hardware.vow.xml
05-24 15:39:38.190  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.accelerometer.xml
05-24 15:39:38.190  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.bluetooth_le.xml
05-24 15:39:38.191  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.flash-autofocus.xml
05-24 15:39:38.192  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.stepcounter.xml
05-24 15:39:38.192  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.gyroscope.xml
05-24 15:39:38.193  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.wifi.passpoint.xml
05-24 15:39:38.193  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.telephony.ims.xml
05-24 15:39:38.194  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.bluetooth.xml
05-24 15:39:38.194  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.raw.xml
05-24 15:39:38.194  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.android.nfc_extras.xml
05-24 15:39:38.196  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.vulkan.version.xml
05-24 15:39:38.196  1307  1307 I SystemServiceManager: Starting com.android.server.security.FileIntegrityService
05-24 15:39:38.196  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.front.xml
05-24 15:39:38.197  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.hifi_sensors.xml
05-24 15:39:38.197  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.opengles.deqp.level.xml
05-24 15:39:38.197  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.xml
05-24 15:39:38.198  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/nfc_features.xml
05-24 15:39:38.198  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.usb.accessory.xml
05-24 15:39:38.199  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.telephony.gsm.xml
05-24 15:39:38.199  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.wifi.xml
05-24 15:39:38.200  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.freeform_window_management.xml
05-24 15:39:38.201  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.hcef.xml
05-24 15:39:38.201  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.midi.xml
05-24 15:39:38.201  1307  1307 I SystemServiceManager: Starting com.android.server.pm.Installer
05-24 15:39:38.201  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.usb.host.xml
05-24 15:39:38.202  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.fingerprint.xml
05-24 15:39:38.202  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.multitouch.jazzhand.xml
05-24 15:39:38.203  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.multitouch.xml
05-24 15:39:38.203  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.ipsec_tunnels.xml
05-24 15:39:38.203  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.se.omapi.uicc.xml
05-24 15:39:38.204  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.opengles.aep.xml
05-24 15:39:38.204  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.se.omapi.ese.xml
05-24 15:39:38.205  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/handheld_core_hardware.xml
05-24 15:39:38.205  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.hce.xml
05-24 15:39:38.205  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.faketouch.xml
05-24 15:39:38.206  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.light.xml
05-24 15:39:38.206  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.audio.low_latency.xml
05-24 15:39:38.206  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.vulkan.deqp.level.xml
05-24 15:39:38.207  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.vulkan.level.xml
05-24 15:39:38.207  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.xml
05-24 15:39:38.207  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.vulkan.compute.xml
05-24 15:39:38.208  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.wifi.direct.xml
05-24 15:39:38.208  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.nxp.mifare.xml
05-24 15:39:38.208  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.stepdetector.xml
05-24 15:39:38.209  1307  1307 I SystemServiceManager: Starting com.android.server.os.DeviceIdentifiersPolicyService
05-24 15:39:38.209  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.compass.xml
05-24 15:39:38.209  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.proximity.xml
05-24 15:39:38.209  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.mediatek.hardware.vow_dsp.xml
05-24 15:39:38.210  1307  1307 I SystemServiceManager: Starting com.android.server.flags.FeatureFlagsService
05-24 15:39:38.210  1307  1339 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.full.xml
05-24 15:39:38.211  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-overlays.xml
05-24 15:39:38.212  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/GoogleCamera_6gb_or_more_ram.xml
05-24 15:39:38.213  1307  1307 I SystemServiceManager: Starting com.android.server.uri.UriGrantsManagerService$Lifecycle
05-24 15:39:38.213  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/nga.xml
05-24 15:39:38.213  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-org.lineageos.glimpse.xml
05-24 15:39:38.213  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-google-install-constraints-package-allowlist.xml
05-24 15:39:38.214  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/quick_tap.xml
05-24 15:39:38.215  1307  1307 I SystemServiceManager: Starting com.android.server.powerstats.PowerStatsService
05-24 15:39:38.215  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/initial-package-stopped-states-org.lineageos.etar.xml
05-24 15:39:38.215  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/google.xml
05-24 15:39:38.216  1307  1339 I SystemConfig: Adding association: com.google.android.as <- com.android.bluetooth.services
05-24 15:39:38.216  1307  1339 I SystemConfig: Adding association: com.google.android.as <- com.google.android.bluetooth.services
05-24 15:39:38.217  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/pixel_experience_2017.xml
05-24 15:39:38.217  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-voltage-product.xml
05-24 15:39:38.218  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/pixel_2016_exclusive.xml
05-24 15:39:38.219  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/adaptivecharging.xml
05-24 15:39:38.219  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-handheld-product.xml
05-24 15:39:38.220  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/com.android.messaging.allowlist.xml
05-24 15:39:38.220  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-google-hiddenapi-package-whitelist.xml
05-24 15:39:38.220  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/voltage-component-overrides.xml
05-24 15:39:38.221  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-google-staged-installer-whitelist.xml
05-24 15:39:38.221  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/google_build.xml
05-24 15:39:38.222  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-org.lineageos.etar.xml
05-24 15:39:38.222   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.power.stats@1.0::IPowerStats/default in either framework or device VINTF manifest.
05-24 15:39:38.222  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/com.android.deskclock_allowlist.xml
05-24 15:39:38.222  1307  1307 E PowerStatsService: Unable to get power.stats HAL service.
05-24 15:39:38.222  1307  1307 E PowerStatsService: nativeInit failed to connect to power.stats HAL
05-24 15:39:38.222  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/dreamliner.xml
05-24 15:39:38.223  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/initial-package-stopped-states-org.lineageos.glimpse.xml
05-24 15:39:38.223  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/initial-package-stopped-states-org.lineageos.aperture.xml
05-24 15:39:38.223  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-preinstalled-packages-product-pixel-2017-and-newer.xml
05-24 15:39:38.224  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/d2d_cable_migration_feature.xml
05-24 15:39:38.225  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-telephony-product.xml
05-24 15:39:38.225  1307  1307 I HidlServiceManagement: Registered android.frameworks.stats@1.0::IStats/default
05-24 15:39:38.226  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/org.lineageos.etar.allowlist.xml
05-24 15:39:38.226  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-org.lineageos.aperture.xml
05-24 15:39:38.226  1307  1339 I SystemConfig: Reading permissions from /product/etc/sysconfig/nexus.xml
05-24 15:39:38.227  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.maps.xml
05-24 15:39:38.228  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.widevine.software.drm.xml
05-24 15:39:38.229  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/privapp-permissions-hotword.xml
05-24 15:39:38.229  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/android.software.freeform_window_management.xml
05-24 15:39:38.229  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.gms.xml
05-24 15:39:38.230  1307  1307 I SystemServiceManager: Starting com.android.server.permission.access.AccessCheckingService
05-24 15:39:38.231  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/privapp-permissions-googleapps-turbo.xml
05-24 15:39:38.231  1307  1307 I SystemServiceManager: Starting com.android.server.wm.ActivityTaskManagerService$Lifecycle
05-24 15:39:38.232  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.wellbeing.xml
05-24 15:39:38.232  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/privapp-permissions-xhotword.xml
05-24 15:39:38.233  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.onetimeinitializer.xml
05-24 15:39:38.234  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.dialer.support.xml
05-24 15:39:38.234  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.keep.xml
05-24 15:39:38.235  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-split-permissions-google.xml
05-24 15:39:38.235  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.dialer.xml
05-24 15:39:38.236  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-privapp-permissions-google-p.xml
05-24 15:39:38.238  1307  1307 I SystemServiceManager: Starting com.android.server.am.ActivityManagerService$Lifecycle
05-24 15:39:38.239  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.turbo.xml
05-24 15:39:38.240  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.vending.xml
05-24 15:39:38.241  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/privapp_whitelist_com.android.dialer-ext.xml
05-24 15:39:38.242  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.dialer.xml
05-24 15:39:38.242  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.media.effects.xml
05-24 15:39:38.243  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.ims.xml
05-24 15:39:38.243  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.messaging.xml
05-24 15:39:38.244  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/android.software.angle.xml
05-24 15:39:38.244  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.imsserviceentitlement.xml
05-24 15:39:38.245  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.settings.intelligence.xml
05-24 15:39:38.245  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-privapp-permissions-google-se.xml
05-24 15:39:38.247  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-privapp-permissions-google.xml
05-24 15:39:38.248  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.contacts.xml
05-24 15:39:38.249  1307  1339 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.restore.xml
05-24 15:39:38.250  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/sysconfig/allowlist_com.stevesoltys.seedvault.xml
05-24 15:39:38.250  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/sysconfig/android.telephony.satellite.xml
05-24 15:39:38.251  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.emergency.xml
05-24 15:39:38.252  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_whitelist_com.android.launcher3-ext.xml
05-24 15:39:38.252  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_allowlist_com.libremobileos.freeform.xml
05-24 15:39:38.253  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.hotwordenrollment.common.util.xml
05-24 15:39:38.253  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/permissions_com.stevesoltys.seedvault.xml
05-24 15:39:38.254  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_whitelist_org.lineageos.setupwizard.xml
05-24 15:39:38.254  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/androidx.window.sidecar.xml
05-24 15:39:38.255  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.bluetooth.bthelper.xml
05-24 15:39:38.255  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_whitelist_io.chaldeaprjkt.gamespace.xml
05-24 15:39:38.256  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/android.software.theme_picker.xml
05-24 15:39:38.256  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.systemui.xml
05-24 15:39:38.257  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.settings.xml
05-24 15:39:38.257  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.carrierconfig.xml
05-24 15:39:38.258  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/androidx.window.extensions.xml
05-24 15:39:38.258  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_allowlist_com.libremobileos.sidebar.xml
05-24 15:39:38.259  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp-permissions-custom.xml
05-24 15:39:38.259  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.launcher3.xml
05-24 15:39:38.260  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.google.android.gsf.xml
05-24 15:39:38.261  1307  1339 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.storagemanager.xml
05-24 15:39:38.262  1307  1339 I SystemConfig: Reading permissions from /apex/com.android.cellbroadcast/etc/permissions/com.android.cellbroadcastreceiver.module.xml
05-24 15:39:38.263  1307  1339 I SystemConfig: Reading permissions from /apex/com.android.cellbroadcast/etc/permissions/com.android.cellbroadcastservice.xml
05-24 15:39:38.264  1307  1339 I SystemConfig: Reading permissions from /apex/com.android.nfcservices/etc/permissions/com.android.nfc.xml
05-24 15:39:38.265  1307  1339 I SystemConfig: Reading permissions from /apex/com.android.tethering/etc/permissions/permissions.xml
05-24 15:39:38.267  1307  1339 I SystemConfig: Reading permissions from /apex/com.android.adservices/etc/permissions/com.android.adservices.api.xml
05-24 15:39:38.268  1307  1339 I SystemConfig: Reading permissions from /apex/com.android.permission/etc/permissions/com.android.permissioncontroller.xml
05-24 15:39:38.269  1307  1339 I SystemConfig: Reading permissions from /apex/com.android.mediaprovider/etc/permissions/com.android.photopicker.xml
05-24 15:39:38.270  1307  1339 I SystemConfig: Reading permissions from /apex/com.android.mediaprovider/etc/permissions/com.android.providers.media.module.xml
05-24 15:39:38.270  1307  1339 I SystemConfig: Reading permissions from /apex/com.android.apex.cts.shim/etc/permissions/signature-permission-allowlist.xml
05-24 15:39:38.271  1307  1339 I SystemConfig: Reading permissions from /apex/com.android.extservices/etc/permissions/android.ext_sminus.services.xml
05-24 15:39:38.273  1307  1339 I SystemConfig: Reading permissions from /apex/com.android.ipsec/etc/permissions/android.net.ipsec.ike.xml
05-24 15:39:38.274  1307  1339 I SystemConfig: Reading permissions from /apex/com.android.healthfitness/etc/permissions/com.android.healthconnect.controller.xml
05-24 15:39:38.275  1307  1339 I SystemConfig: Reading permissions from /apex/com.android.btservices/etc/permissions/com.android.bluetooth.xml
05-24 15:39:38.276  1307  1339 I SystemConfig: Reading permissions from /apex/com.android.devicelock/etc/permissions/com.android.devicelockcontroller.xml
05-24 15:39:38.277  1307  1339 I incfs   : Initial API level of the device: 30
05-24 15:39:38.280  1307  1346 E system_server: memevent deregister all events failed, failure to initialize
05-24 15:39:38.280  1307  1346 E OomConnection: failed waiting for OOM events: java.lang.RuntimeException: Failed to initialize memevents listener
05-24 15:39:38.378  1307  1326 W android.permission.PermissionManager: Missing ActivityManager; assuming 1047 does not hold android.permission.MANAGE_APP_OPS_MODES
05-24 15:39:38.379  1307  1307 I SystemServiceManager: Starting com.android.server.pm.DataLoaderManagerService
05-24 15:39:38.383  1307  1307 I SystemServiceManager: Starting com.android.server.power.PowerManagerService
05-24 15:39:38.397  1307  1307 I PowerHalLoader: Successfully connected to Power HAL AIDL service.
05-24 15:39:38.401  1307  1307 I SystemServiceManager: Starting com.android.server.power.ThermalManagerService
05-24 15:39:38.407  1307  1307 I SystemServiceManager: Starting com.android.server.recoverysystem.RecoverySystemService$Lifecycle
05-24 15:39:38.410  1307  1307 I SystemServiceManager: Starting com.android.server.lights.LightsService
05-24 15:39:38.412  1307  1307 I SystemServiceManager: Starting com.android.server.display.DisplayManagerService
05-24 15:39:38.420  1307  1307 I SystemServiceManager: Starting phase 100
05-24 15:39:38.423  1307  1335 E DisplayManagerService: Default display is null for info request from uid 1000
05-24 15:39:38.430  1307  1356 W ProcessCpuTracker: Failed to stat(/proc/394): android.system.ErrnoException: stat failed: ENOENT (No such file or directory)
05-24 15:39:38.478  1307  1307 I UserManagerService: Upgrading users from userVersion 11 to 11
05-24 15:39:38.573   916   916 I ccci_mdinit: (1):MD1 set status: mtk.md1.status=ready 
05-24 15:39:38.573   916   916 I ccci_mdinit: (1):start_service init.svc.emdlogger1, but returned 0, maybe has no this property
05-24 15:39:38.574   916   916 I ccci_mdinit: (1):wait_for_property: is_stop_wait: 0
05-24 15:39:38.584   916   916 I ccci_mdinit: (1):start_service init.svc.vendor.gsm0710muxd, but returned 0, maybe has no this property
05-24 15:39:38.588   916   916 I ccci_mdinit: (1):wait_for_property: is_stop_wait: 0
05-24 15:39:38.588   916   916 I ccci_mdinit: (1):wait_for_property:success(init.svc.vendor.gsm0710muxd=running), loop:600
05-24 15:39:38.613  1307  1307 W android.permission.PermissionManager: Missing ActivityManager; assuming 1000 holds android.permission.SET_PREFERRED_APPLICATIONS
05-24 15:39:38.669  1307  1307 W PackageManager: No package known for package restrictions com.android.adservices
05-24 15:39:38.679  1307  1307 W PackageManager: No package known for package restrictions com.android.permission
05-24 15:39:38.698  1017  1056 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 15:39:38.698  1017  1056 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:2
05-24 15:39:38.703  1307  1307 W PackageManager: No package known for package restrictions com.android.btservices
05-24 15:39:38.790  1307  1307 W PackageManager: No package known for package restrictions com.android.extservices
05-24 15:39:38.818  1307  1307 W PackageManager: No package known for package restrictions com.android.nfcservices
05-24 15:39:38.826  1307  1307 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.noCutout on user 0
05-24 15:39:38.826  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.font.sanfrancisco on user 0
05-24 15:39:38.826  1307  1307 W PackageSettings: Missing permission state for package org.omnirom.omnijaws on user 0
05-24 15:39:38.826  1307  1307 W PackageSettings: Missing permission state for package com.android.systemui.clocks.metro on user 0
05-24 15:39:38.826  1307  1307 W PackageSettings: Missing permission state for package in.zeta.android on user 0
05-24 15:39:38.826  1307  1307 W PackageSettings: Missing permission state for package com.android.cts.priv.ctsshim on user 0
05-24 15:39:38.826  1307  1307 W PackageSettings: Missing permission state for package org.voltage.theme.font.dosis on user 0
05-24 15:39:38.826  1307  1307 W PackageSettings: Missing permission state for package com.google.android.youtube on user 0
05-24 15:39:38.826  1307  1307 W PackageSettings: Missing permission state for package com.android.uwb.resources on user 0
05-24 15:39:38.826  1307  1307 W PackageSettings: Missing permission state for package com.nikgapps.overlay.messages on user 0
05-24 15:39:38.826  1307  1307 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.corner on user 0
05-24 15:39:38.826  1307  1307 W PackageSettings: Missing permission state for package com.android.adservices.api on user 0
05-24 15:39:38.826  1307  1307 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.double on user 0
05-24 15:39:38.826  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.themepicker on user 0
05-24 15:39:38.826  1307  1307 W PackageSettings: Missing permission state for package com.rifsxd.ksunext on user 0
05-24 15:39:38.826  1307  1307 W PackageSettings: Missing permission state for package app.grapheneos.gmscompat.config on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.settings on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.truecaller on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.healthconnect.controller on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.manhwabuddy on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.luascans on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.xperia.settings on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.gradicon.android on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.google.android.onetimeinitializer on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.health.connect.backuprestore on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.virtualmachine.res on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.mxtech.videoplayer.pro on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.systemui on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.managedprovisioning.auto_generated_rro_product__ on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package org.omnirom.omnijaws.auto_generated_rro_product__ on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.apkupdater on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.settings on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.narrow on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.systemui on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.systemui on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.settings on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.android on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.documentsui on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.externalstorage on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.server.deviceconfig.resources on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.settings on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.nikgapps.overlay.googlelocationhistory on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.whatsapp on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.android.companiondevicemanager on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package com.coderstory.toolkit on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package io.github.jica98 on user 0
05-24 15:39:38.827  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.gourmetscans on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package app.grapheneos.logviewer on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.systemui on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.android.systemui.auto_generated_rro_product__ on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.google.android.apps.messaging on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.mediatek.engineermode on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.android.federatedcompute.services on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.android on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.android.carrierconfig.mt6893 on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package io.chaldeaprjkt.gamespace.auto_generated_rro_product__ on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.lonelycatgames.Xplore on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.android.system.monet.snowpaintdrop on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.scyllascans on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.nikgapps.overlay.googlephotos on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.xperia.systemui on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package app.komikku on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package jp.pxv.android on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.systemui on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.themepicker on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package net.thunderbird.android on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.android.systemui.clocks.bignum on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.android.avatarpicker on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.xayah.databackup.foss on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.systemui on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.font.rookery on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.snowmtl on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.android.systemui.plugin.globalactions.wallet on user 0
05-24 15:39:38.828  1307  1307 W PackageSettings: Missing permission state for package com.android.safetycenter.resources on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package org.zwanoo.android.speedtest on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.system.monet.vivid on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.vending on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.pacprocessor on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.simappdialog on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package org.protonaosp.deviceconfig.auto_generated_rro_product__ on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package eu.darken.sdmse on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.systemui on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.systemui.clocks.growth on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.connectivity.resources on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.hole on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.tall on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.wide on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.ancient.telephonyoverlay on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.networkstack.overlay on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package org.lineageos.glimpse.frameworksbaseoverlay on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.modulemetadata on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.certinstaller on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.carrierconfig on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.launcher on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.lorn.android on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.internal.systemui.navbar.threebutton on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.brave.browser on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.aurorascans on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.talkback on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.wifi.dialog on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.nikgapps.overlay.gmscore on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.hotwordenrollment.xgoogle on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.oneplusparts.overlay.rm on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package ru.mike.updatelocker on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.launcher on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.launcher on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.philiascans on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package co.aospa.sense.auto_generated_rro_product__ on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.kewnscans on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.shojoscans on user 0
05-24 15:39:38.829  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.settings on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package org.lineageos.cupida.frameworkresoverlay on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.android on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package ru.andr7e.deviceinfohw on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.android.egg on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.android.launcher3 on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package livio.pack.lang.en_US on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.google.android.trichromelibrary_710306033 on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package org.lineageos.settings.doze.overlay on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.android.backupconfirm on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.font.fluidsans on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.axiel7.anihyou on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.android.settings.auto_generated_rro_vendor__ on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package org.voltage.theme.font.opposans on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.android.systemui.auto_generated_rro_vendor__ on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.google.android.deskclock on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package org.protonaosp.deviceconfig on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.wmods.wppenhacer on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.android.systemui.clocks.numoverlap on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.android.statementservice on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.xperia.android on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.google.android.gm on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package org.calyxos.backup.contacts on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.launcher on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.voltageos.colorstub on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.webtoons on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.oplus.doze.overlay_system on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.settings on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.android.settings.intelligence on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.android.systemui.clocks.calligraphy on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.android.systemui.accessibility.accessibilitymenu on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package org.voltage.theme.font.linotte on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.oplus.doze.overlay_systemui on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.themepicker on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package org.adaway on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.zeptoconsumerapp on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.f0x1d.logfox on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.ancient.frameworkresoverlay.mt6893 on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package ru.tech.imageresizershrinker on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.mangademon on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.ancient.systemuioverlay.mt6893 on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package org.lineageos.setupwizard.auto_generated_rro_product__ on user 0
05-24 15:39:38.830  1307  1307 W PackageSettings: Missing permission state for package com.android.sharedstoragebackup on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.launcher on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.printspooler on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.hotwordenrollment.okgoogle on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.emergency.auto_generated_rro_product__ on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.settings on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.dreams.basic on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.settings.overlay.oplus.target on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.launcher on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.providers.settings.auto_generated_rro_product__ on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package org.mozilla.focus on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.photopicker on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.systemui on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.webview on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.permissioncontroller.overlay on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package app.grapheneos.networklocation on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.google.android.apps.wellbeing on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.coffeemanga on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.rkpdapp on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.google.android.dialer on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.launcher on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.bips on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.themepicker on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.settings on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.intentresolver.auto_generated_rro_product__ on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.android on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package org.eu.droid_ng.jellyfish on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.musicfx on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package app.vitune.android on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.google.android.apps.docs on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package ellipi.messenger on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.systemui on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.asurascans on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package app.grapheneos.gmscompat.lib on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package duy.com.text_converter on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.customization.themes on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.font.googlesans on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package net.one97.paytm on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.cellbroadcastreceiver on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.google.android.webview on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package android.ext.shared on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.android.bluetooth.bthelper.auto_generated_rro_product__ on user 0
05-24 15:39:38.831  1307  1307 W PackageSettings: Missing permission state for package com.google.android.contactkeys on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.google.android.contacts on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.google.android.syncadapters.contacts on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.android.system.monet.expresso on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.nikgapps.overlay.googleclock on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package org.calyxos.datura on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.themepicker on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.android.systemui.clocks.inflate on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.google.android.calculator on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.adultwebtoon on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package org.voltage.theme.font.manrope on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.android.printservice.recommendation on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package app.grapheneos.AppCompatConfig on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package me.jmh.authenticatorpro on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.systemui on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.mangadex on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.kaiscans on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.google.android.gms on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.google.android.ims on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.android.system.theme.black on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package android.ext.services on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.android.wifi.resources on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.plumpy.systemui on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.android.cameraextensions on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.android.packageinstaller on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.android.carrierdefaultapp on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.magusmanga on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.gradicon.systemui on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.necroscans on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.font.opsans on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.batoto on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.android.credentialmanager on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.android on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.font.notoserifsource on user 0
05-24 15:39:38.832  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.android on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.proxyhandler on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.launcher3.auto_generated_rro_product__ on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.waterfall on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.intentresolver on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.systemui on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package io.github.muntashirakon.AppManager on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.internal.systemui.navbar.transparent on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.providers.settings.overlay on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.android on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.google.android.apps.photos on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.android on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.managedprovisioning on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.aeinscans on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package io.github.dovecoteescapee.byedpi on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.lorn.systemui on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.dreams.phototable on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package org.lineageos.settings.doze.auto_generated_rro_product__ on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.networkstack.tethering.mt6893 on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.launcher on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package org.voltage.theme.font.recursive_casual on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package air.kukulive.mailnow on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.looker.droidify on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.android on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.smspush on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.role.notes.enabled on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.berdik.letmedowngrade on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.wallpaper.livepicker on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package org.lineageos.aperture on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.cellbroadcastreceiver.module on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.systemui.clocks.flex on user 0
05-24 15:39:38.833  1307  1307 W PackageSettings: Missing permission state for package com.android.apps.tag on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.android.inputmethod.latin.auto_generated_rro_product__ on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.power.hub.udfps.icons on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.android.imsserviceentitlement on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.android.appsearch.apk on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.launcher on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.valirscans on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package org.voltage.theme.font.recursive_linear on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.avoidAppsInCutout on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package org.lineageos.cupida.wifioverlay on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package idm.internet.download.manager.plus on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.plumpy.android on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.arvenscans on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.melody on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.android on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.android.storagemanager on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.zerodha.kite3 on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.android.bookmarkprovider on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.fitbit.FitbitMobile on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.aurora.systemui on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.launcher on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package uk.akane.omni on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package org.protonaosp.theme.font.linotte on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.google.android.apps.googlecamera.fishfood on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.voltage.overlay.customization.keyboard.nonavbar on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.google.android.apps.turbo on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.enryumanga on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.google.android.safetycore on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.whalemanga on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package app.grapheneos.gmscompat on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.themepicker on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.android.wifi.resources.mt6893 on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package proton.android.pass on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.launcher on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.android.wallpaper on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.nikgapps.overlay.turbo on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.android.vpndialogs on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package com.goping.user on user 0
05-24 15:39:38.834  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.nyxscans on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.google.android.keep on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.android.angle on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.mangareadorg on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.linkbox.plus.android on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.themepicker on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.android.sdksandbox on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.android.wallpaperbackup on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.stevesoltys.seedvault.auto_generated_rro_product__ on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.android.settings.auto_generated_rro_product__ on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.voltageos.Covers on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.android.providers.media.module on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.power.hub.udfps.animations on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package in.swiggy.android on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.android.hotspot2.osulogin on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.solarmtl on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.google.android.gms.location.history on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.intsig.camscanner on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.android.internal.systemui.navbar.gestural on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package co.aospa.sense.settings.overlay on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.themepicker on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.wstxda.viper4android on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.harimanga on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.mangadistrict on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.android.bluetoothmidiservice on user 0
05-24 15:39:38.835  1307  1307 W PackageSettings: Missing permission state for package com.ancient.settingsoverlay.mt6893 on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package org.akanework.gramophone on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.android.permissioncontroller on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.android.storagemanager.auto_generated_rro_product__ on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.zerodha.coin on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.android.dialer.auto_generated_rro_vendor__ on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package app.customerportal.tachyon1 on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.android.imsserviceentitlement.auto_generated_rro_product__ on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.android.phone.auto_generated_rro_product__ on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package android.auto_generated_rro_product__ on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.ezmanga on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.templescan on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.android.dialer.auto_generated_rro_product__ on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.settings on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.themepicker on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.android.ondevicepersonalization.services on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.android.documentsui.overlay on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.anisascans on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.android.captiveportallogin on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.aurora.android on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.android.devicelockcontroller on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.settings on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.tukann.confinedandhorny on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.settings on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.likemanga on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.nikgapps.overlay.wellbeing on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.nikgapps.overlay.dialer on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.google.android.inputmethod.latin on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package org.lineageos.aperture.frameworksbaseoverlay on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.nikgapps.overlay.contacts on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package android.auto_generated_rro_vendor__ on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.android on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for package com.google.android.apps.restore on user 0
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for shared user: android.uid.log
05-24 15:39:38.836  1307  1307 W PackageSettings: Missing permission state for shared user: android.uid.uwb
05-24 15:39:38.967  1307  1307 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-24 15:39:38.972  1307  1307 I PackageManager: /system/apex/com.android.btservices.apex changed; collecting certs
05-24 15:39:38.986  1307  1307 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-24 15:39:39.005  1307  1307 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-24 15:39:39.013  1307  1307 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-24 15:39:39.106  1307  1320 I system_server: Compiler allocated 4688KB to compile com.android.server.pm.ScanResult com.android.server.pm.ScanPackageUtils.scanPackageOnly(com.android.server.pm.ScanRequest, com.android.server.pm.PackageManagerServiceInjector, boolean, long)
05-24 15:39:39.178  1400  1400 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/slot1
05-24 15:39:39.180  1400  1400 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSlot1
05-24 15:39:39.183  1400  1400 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/imsAospSlot1
05-24 15:39:39.185  1400  1400 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/imsSlot1
05-24 15:39:39.187  1400  1400 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/se1
05-24 15:39:39.189  1400  1400 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSe1
05-24 15:39:39.190  1400  1405 E SchedPolicy: open of /dev/cpuctl/bg_non_interactive/tasks failed: No such file or directory
05-24 15:39:39.190  1400  1400 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/em1
05-24 15:39:39.192  1400  1400 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkEm1
05-24 15:39:39.193  1400  1400 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkAssist1
05-24 15:39:39.194  1400  1400 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRcs1
05-24 15:39:39.195  1400  1400 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkCap1
05-24 15:39:39.196  1400  1412 I RmcVsim : [0] RmcVsimUrcHandler init slot: 0, ch id 0
05-24 15:39:39.196  1400  1400 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSmartRatSwitch1
05-24 15:39:39.197  1400  1414 I RmcVsim : [1] RmcVsimUrcHandler init slot: 1, ch id 0
05-24 15:39:39.198  1400  1400 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRsu1
05-24 15:39:39.199  1400  1400 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/slot2
05-24 15:39:39.200  1400  1400 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSlot2
05-24 15:39:39.202  1400  1400 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/imsAospSlot2
05-24 15:39:39.203  1400  1400 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/imsSlot2
05-24 15:39:39.205  1400  1400 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/se2
05-24 15:39:39.206  1400  1400 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSe2
05-24 15:39:39.208  1400  1400 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/em2
05-24 15:39:39.211  1400  1400 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkEm2
05-24 15:39:39.212  1400  1400 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkAssist2
05-24 15:39:39.213  1400  1400 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRcs2
05-24 15:39:39.214  1400  1400 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkCap2
05-24 15:39:39.219  1400  1400 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSmartRatSwitch2
05-24 15:39:39.221  1400  1400 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRsu2
05-24 15:39:39.222  1400  1400 I RILC-OplusRadioAIDL: radio::registerService starting IOplusRadio vendor.oplus.hardware.radio.IRadioStable/OplusRadio0
05-24 15:39:39.225  1400  1405 I WpfaCppUtils: initialRuleContainer!
05-24 15:39:39.225  1400  1400 I RILC-OplusRadioAIDL: radio::registerService IOplusRadio status: 0
05-24 15:39:39.225  1400  1400 I RILC-OplusRadioAIDL: radio::registerService starting IOplusRadio vendor.oplus.hardware.radio.IRadioStable/OplusRadio1
05-24 15:39:39.227  1400  1400 I RILC-OplusRadioAIDL: radio::registerService IOplusRadio status: 0
05-24 15:39:39.227  1400  1400 I RILC-OplusAppRadio: radio::registerService starting IOplusAppRadio oplus_app_slot1
05-24 15:39:39.227  1400  1405 I WpfaCppUtils: initialA2MRingBuffer!
05-24 15:39:39.228  1400  1400 I HidlServiceManagement: Registered vendor.oplus.hardware.appradio@1.0::IOplusAppRadio/oplus_app_slot1
05-24 15:39:39.228  1400  1400 I RILC-OplusAppRadio: radio::registerService IOplusAppRadio status: 0
05-24 15:39:39.228  1400  1400 I RILC-OplusAppRadio: radio::registerService starting IOplusAppRadio oplus_app_slot2
05-24 15:39:39.229  1400  1400 I HidlServiceManagement: Registered vendor.oplus.hardware.appradio@1.0::IOplusAppRadio/oplus_app_slot2
05-24 15:39:39.229  1400  1400 I RILC-OplusAppRadio: radio::registerService IOplusAppRadio status: 0
05-24 15:39:39.229  1400  1433 I RmcDcImsDc2ReqHandler: [0][handleImsBearerNotify] urc=+EIMSPDN: "init"
05-24 15:39:39.231  1400  1400 I HidlServiceManagement: Registered android.hardware.radio.config@1.3::IRadioConfig/default
05-24 15:39:39.234  1400  1400 I HidlServiceManagement: Registered android.hardware.radio@1.2::ISap/slot1
05-24 15:39:39.235  1400  1400 I HidlServiceManagement: Registered android.hardware.radio@1.2::ISap/slot2
05-24 15:39:39.239  1400  1455 I RmcDcImsDc2ReqHandler: [1][handleImsBearerNotify] urc=+EIMSPDN: "init"
05-24 15:39:39.294  1008  1008 I AVSync  : avInit, st 24650139f, int=9, frac=c53ecaf8
05-24 15:39:39.294  1008  1008 I vtservice: [VT][SRV]after VTService_instantiate
05-24 15:39:39.295  1008  1484 I AVSync  : avInit, st 2465f3b6d, int=9, frac=c57ff621
05-24 15:39:39.301   856  1289 I VT HIDL : [IVT] [VT THREAD] [VT_Bind] des = volte_imsvt1 initialize communication
05-24 15:39:39.338  1400  1470 E libmnlUtils: No action: deInitReaderLoop can't get mMnlsocket
05-24 15:39:39.338  1400  1470 I wpfa    : initReaderLoop() done, buf_size=67583
05-24 15:39:39.338  1400  1470 I wpfa    : WPFA_DL initialized
05-24 15:39:39.343  1307  1372 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 15:39:39.343  1307  1372 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 15:39:39.344  1307  1372 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 15:39:39.359  1307  1372 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 15:39:39.366  1307  1372 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 15:39:39.366  1307  1372 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-24 15:39:39.407  1307  1371 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.CAMERA in package: com.mediatek.engineermode at: Binary XML file line #30
05-24 15:39:39.474  1307  1369 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.SATELLITE_COMMUNICATION in package: com.android.shell at: Binary XML file line #775
05-24 15:39:39.475  1307  1369 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE in package: com.android.shell at: Binary XML file line #874
05-24 15:39:39.475  1307  1369 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE_SYSTEM_EXEMPTED in package: com.android.shell at: Binary XML file line #875
05-24 15:39:39.512  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.POST_NOTIFICATIONS in package: com.android.phone at: Binary XML file line #171
05-24 15:39:39.664  1307  1307 W PackageManager: Failed to scan /product/priv-app/CarrierServices: Package com.google.android.ims at /product/priv-app/CarrierServices ignored: updated version 31144015 better than this 30939330
05-24 15:39:39.698  1017  1056 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 15:39:39.699  1017  1056 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:3
05-24 15:39:39.813  1400  1405 E RILC-OplusRadioAIDL: oplusRadioServiceAIDL: oplusRadioServiceAIDL[0]->mRadioIndicationOplus == NULL
05-24 15:39:39.937  1077  1077 I KEYFILE : [INFO   ] CustomCommon.cpp:161 init() ro.vendor.config.oplus.low_ram = 0
05-24 15:39:39.937  1077  1077 I KEYFILE : [INFO   ] CustomCommon.cpp:162 init() vendor.debug.camera.bss.aishutter.weighting = 100,98,96,94,92,90,90,90
05-24 15:39:39.938  1077  1077 I KEYFILE : [INFO   ] CustomCommon.cpp:163 init() vendor.debug.tpi.s.semi.run = 0
05-24 15:39:39.938  1077  1077 I KEYFILE : [INFO   ] CustomCommon.cpp:164 init() vendor.debug.camera.FDAsync = true
05-24 15:39:39.938  1077  1077 E KEYFILE : [ERROR   ] CustomMetadata.cpp:375 init() PROP_SYS_CAM_PACKNAME err 0!
05-24 15:39:39.938  1077  1077 I KEYFILE : [INFO   ] CustomerData.cpp:93 init() 0xb4000077fc85fb30, size: 288 byte
05-24 15:39:40.011  1400  1405 E RILC-OplusRadioAIDL: oplusRadioServiceAIDL: oplusRadioServiceAIDL[1]->mRadioIndicationOplus == NULL
05-24 15:39:40.337  1307  1371 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.CONFIGURE_WIFI_DISPLAY in package: com.android.systemui at: Binary XML file line #174
05-24 15:39:40.337  1307  1371 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.GET_RUNTIME_PERMISSIONS in package: com.android.systemui at: Binary XML file line #252
05-24 15:39:40.337  1307  1371 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.READ_DEVICE_CONFIG in package: com.android.systemui at: Binary XML file line #372
05-24 15:39:40.338  1307  1371 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.MODIFY_AUDIO_SETTINGS in package: com.android.systemui at: Binary XML file line #405
05-24 15:39:40.338  1307  1371 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FORCE_STOP_PACKAGES in package: com.android.systemui at: Binary XML file line #426
05-24 15:39:40.474  1307  1307 I ApexManager: Registering com.android.cellbroadcastservice as apk-in-apex of com.android.cellbroadcast
05-24 15:39:40.548  1307  1307 I ApexManager: Registering com.android.bluetooth as apk-in-apex of com.android.btservices
05-24 15:39:40.569  1307  1370 W PackageParsing: com.android.healthconnect.controller defines a background permission. Only the 'android' packages can do that.
05-24 15:39:40.569  1307  1370 W PackageParsing: com.android.healthconnect.controller defines a background permission. Only the 'android' packages can do that.
05-24 15:39:40.569  1307  1370 W PackageParsing: com.android.healthconnect.controller defines a background permission. Only the 'android' packages can do that.
05-24 15:39:40.638  1307  1307 I ApexManager: Registering com.android.safetycenter.resources as apk-in-apex of com.android.permission
05-24 15:39:40.685  1307  1307 I ApexManager: Registering com.android.permissioncontroller as apk-in-apex of com.android.permission
05-24 15:39:40.699  1017  1056 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 15:39:40.699  1017  1056 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:4
05-24 15:39:41.015  1307  1371 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.RECEIVE_BOOT_COMPLETED in package: com.android.adservices.api at: Binary XML file line #159
05-24 15:39:41.020  1307  1307 I ApexManager: Registering com.android.adservices.api as apk-in-apex of com.android.adservices
05-24 15:39:41.029  1307  1307 I ApexManager: Registering com.android.sdksandbox as apk-in-apex of com.android.adservices
05-24 15:39:41.035  1400  1405 E RadioConfig_service: radioConfigService[0] or mRadioConfigIndication is NULL
05-24 15:39:41.039  1307  1307 I ApexManager: Registering com.android.ondevicepersonalization.services as apk-in-apex of com.android.ondevicepersonalization
05-24 15:39:41.048  1307  1307 I ApexManager: Registering com.android.federatedcompute.services as apk-in-apex of com.android.ondevicepersonalization
05-24 15:39:41.061  1307  1371 W PackageParsing: Unknown element under <manifest>: meta-data at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #102
05-24 15:39:41.061  1307  1371 W PackageParsing: Unknown element under <manifest>: provider at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #106
05-24 15:39:41.061  1307  1371 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #116
05-24 15:39:41.061  1307  1371 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #122
05-24 15:39:41.061  1307  1371 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #129
05-24 15:39:41.061  1307  1371 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #135
05-24 15:39:41.061  1307  1371 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #140
05-24 15:39:41.061  1307  1371 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #147
05-24 15:39:41.061  1307  1371 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #153
05-24 15:39:41.061  1307  1371 W PackageParsing: Unknown element under <manifest>: receiver at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #158
05-24 15:39:41.061  1307  1371 W PackageParsing: Unknown element under <manifest>: service at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #166
05-24 15:39:41.061  1307  1371 W PackageParsing: Unknown element under <manifest>: provider at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #168
05-24 15:39:41.061  1307  1371 W PackageParsing: Unknown element under <manifest>: receiver at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #177
05-24 15:39:41.067  1307  1307 I ApexManager: Registering com.android.nfc as apk-in-apex of com.android.nfcservices
05-24 15:39:41.143  1307  1307 I ApexManager: Registering android.ext.services as apk-in-apex of com.android.extservices
05-24 15:39:41.159  1492  1492 I HidlServiceManagement: Registered android.hardware.audio@7.1::IDevicesFactory/default
05-24 15:39:41.159  1492  1492 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-24 15:39:41.160  1492  1492 I LegacySupport: Registration complete for android.hardware.audio@7.1::IDevicesFactory/default.
05-24 15:39:41.166  1492  1492 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-24 15:39:41.167  1492  1492 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-24 15:39:41.175  1492  1492 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:39:41.246  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.246  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.246  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.246  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.246  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.246  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.246  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.246  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.246  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.247  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.247  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.247  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.247  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.247  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.247  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.247  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.247  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.247  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.287  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.287  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.290  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 15:39:41.307  1492  1492 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x3b53f8c9)
05-24 15:39:41.312  1492  1492 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x6b68b3a9)
05-24 15:39:41.314  1492  1492 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:39:41.315  1492  1492 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x3d322bfb)
05-24 15:39:41.316  1492  1492 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x8e422ac5)
05-24 15:39:41.318  1492  1492 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-24 15:39:41.318   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:39:41.319  1492  1492 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-24 15:39:41.319   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:39:41.319  1492  1492 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-24 15:39:41.320   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:39:41.320  1492  1492 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-24 15:39:41.320  1492  1492 W audiohalservice: Could not register Bluetooth Audio API
05-24 15:39:41.320   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-24 15:39:41.321  1492  1492 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-24 15:39:41.321  1492  1492 W audiohalservice: Could not register Bluetooth Audio Offload API
05-24 15:39:41.337  1307  1371 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.MANAGE_OWN_CALLS in package: com.truecaller at: Binary XML file line #141
05-24 15:39:41.340  1492  1492 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-24 15:39:41.340  1307  1369 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.READ_EXTERNAL_STORAGE in package: ellipi.messenger at: Binary XML file line #111
05-24 15:39:41.340  1307  1369 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WRITE_EXTERNAL_STORAGE in package: ellipi.messenger at: Binary XML file line #179
05-24 15:39:41.340  1492  1492 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-24 15:39:41.340  1492  1492 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-24 15:39:41.361  1499  1499 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:39:41.363  1499  1499 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:39:41.363   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:39:41.378  1499  1499 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:39:41.378   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:39:41.380  1499  1499 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:39:41.388  1307  1326 W android.permission.PermissionManager: Missing ActivityManager; assuming 1041 does not hold android.permission.UPDATE_DEVICE_STATS
05-24 15:39:41.389  1499  1499 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-24 15:39:41.389  1307  1326 W Binder  : java.lang.SecurityException: Access denied, requires: android.permission.UPDATE_DEVICE_STATS
05-24 15:39:41.389  1307  1326 W Binder  : 	at android.os.PermissionEnforcer.enforcePermission(PermissionEnforcer.java:146)
05-24 15:39:41.389  1307  1326 W Binder  : 	at com.android.internal.app.IBatteryStats$Stub.noteResetAudio_enforcePermission(IBatteryStats.java:3472)
05-24 15:39:41.389  1307  1326 W Binder  : 	at com.android.server.am.BatteryStatsService.noteResetAudio(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:1)
05-24 15:39:41.393  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.INTERNET in package: com.intsig.camscanner at: Binary XML file line #34
05-24 15:39:41.401  1499  1499 E libxml2 : I/O warning : failed to load "/odm/etc/virtual_audio_policy_configuration.xml": No such file or directory
05-24 15:39:41.401  1499  1499 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-24 15:39:41.401  1499  1499 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-24 15:39:41.410  1492  1492 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-24 15:39:41.410  1492  1492 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-24 15:39:41.415  1492  1492 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:39:41.468  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.468  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.468  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.468  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.468  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.468  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.469  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.469  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.469  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.469  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.469  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.469  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.469  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.469  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.469  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.469  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.469  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.469  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.508  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.508  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:41.510  1492  1492 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 15:39:41.528  1492  1492 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xd3025a03)
05-24 15:39:41.528  1492  1510 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-24 15:39:41.530  1492  1510 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0xd3025a03)
05-24 15:39:41.532  1492  1510 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0xd3025a03)
05-24 15:39:41.573  1492  1492 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-24 15:39:41.577  1492  1492 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 15:39:41.578  1492  1492 W AudioSmartPaController: init(), dlopen failed, dlerror = dlopen failed: library "libfile_op.so" not found: needed by /vendor/lib/libnxp_extamp_intf.so in namespace (default)
05-24 15:39:41.596  1492  1492 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-24 15:39:41.597  1077  1077 I mtkcam-devicemgr: [initialize] -
05-24 15:39:41.597  1077  1077 I mtkcam-camprovider: [initialize] +
05-24 15:39:41.598  1077  1077 I mtkcam-camprovider: [initialize] -
05-24 15:39:41.602  1077  1077 I HidlServiceManagement: Registered android.hardware.camera.provider@2.6::ICameraProvider/internal/0
05-24 15:39:41.610  1492  1492 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 15:39:41.610  1492  1492 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 15:39:41.610  1492  1492 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 15:39:41.616  1492  1492 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 15:39:41.617  1492  1492 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 15:39:41.617  1492  1492 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-24 15:39:41.617  1077  1077 I HidlServiceManagement: Registered vendor.mediatek.hardware.camera.isphal@1.0::IISPModule/internal/0
05-24 15:39:41.619  1018  1074 I CameraService: onDeviceStatusChanged: Status changed for cameraId=4, newStatus=1
05-24 15:39:41.619  1018  1074 I CameraService: onDeviceStatusChanged: Unknown camera ID 4, a new camera is added
05-24 15:39:41.620  1018  1074 I CameraService: onDeviceStatusChanged: Status changed for cameraId=3, newStatus=1
05-24 15:39:41.620  1018  1074 I CameraService: onDeviceStatusChanged: Unknown camera ID 3, a new camera is added
05-24 15:39:41.620  1018  1074 I CameraService: onDeviceStatusChanged: Status changed for cameraId=2, newStatus=1
05-24 15:39:41.620  1018  1074 I CameraService: onDeviceStatusChanged: Unknown camera ID 2, a new camera is added
05-24 15:39:41.620  1018  1074 I CameraService: onDeviceStatusChanged: Status changed for cameraId=1, newStatus=1
05-24 15:39:41.620  1018  1074 I CameraService: onDeviceStatusChanged: Unknown camera ID 1, a new camera is added
05-24 15:39:41.620  1018  1074 I CameraService: onDeviceStatusChanged: Status changed for cameraId=0, newStatus=1
05-24 15:39:41.620  1018  1074 I CameraService: onDeviceStatusChanged: Unknown camera ID 0, a new camera is added
05-24 15:39:41.620  1492  1492 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-24 15:39:41.621  1492  1492 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-24 15:39:41.621  1077  1077 I MtkCam/BGService: IBGService  into HIDL_FETCH_IBGService
05-24 15:39:41.621  1499  1499 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 15:39:41.621  1499  1499 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 15:39:41.622  1492  1498 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-24 15:39:41.622  1492  1498 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-24 15:39:41.622  1077  1077 I HidlServiceManagement: Registered vendor.mediatek.hardware.camera.bgservice@1.1::IBGService/internal/0
05-24 15:39:41.622  1492  1498 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 15:39:41.622  1499  1499 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-24 15:39:41.622  1077  1077 I LegacySupport: Registration complete for vendor.mediatek.hardware.camera.bgservice@1.1::IBGService/internal/0.
05-24 15:39:41.623  1499  1499 I AudioFlinger: openOutput() this 0xb400007a6154c960, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-24 15:39:41.624  1499  1499 I AudioFlinger: operator(): Using 3000 ms as standby time
05-24 15:39:41.624  1499  1499 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 15:39:41.624  1499  1499 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:39:41.625   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:39:41.627  1499  1499 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:39:41.635  1077  1077 I HidlServiceManagement: Registered vendor.mediatek.hardware.camera.atms@1.0::IATMs/default
05-24 15:39:41.638  1492  1492 E EffectsFactoryConfigLoader: loadLibrary Could not find library in effect directories: libaudiopreprocessing_mtk.so
05-24 15:39:41.638  1492  1492 E EffectsFactoryConfigLoader: EffectLoadXmlEffectConfig 4 errors during loading of configuration: /vendor/etc/audio_effects.xml
05-24 15:39:41.638  1499  1530 I AudioFlinger: AudioFlinger's thread 0xb400007cbf0c8760 tid=1530 ready to run
05-24 15:39:41.667  1307  1371 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #9
05-24 15:39:41.667  1307  1371 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #10
05-24 15:39:41.667  1307  1371 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #11
05-24 15:39:41.667  1492  1512 W audio_messenger_ipi: read_ipi_thread(), read dsp fail!! length_read: 0, errno: 0
05-24 15:39:41.667  1307  1371 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #12
05-24 15:39:41.677  1533  1533 E DEBUG   : failed to read process info: failed to open /proc/1492: No such file or directory
05-24 15:39:41.699  1017  1056 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 15:39:41.699  1017  1056 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:5
05-24 15:39:41.746  1533  1533 F DEBUG   : Cmdline: /vendor/bin/hw/android.hardware.audio.service
05-24 15:39:41.746  1533  1533 F DEBUG   : pid: 1492, tid: 1498, name: HwBinder:1492_2  >>> /vendor/bin/hw/android.hardware.audio.service <<<
05-24 15:39:41.747  1533  1533 F DEBUG   :       #01 pc 0002651d  /vendor/lib/hw/<EMAIL> (android::hardware::audio::V7_1::implementation::StreamOut::setLatencyModeCallback(android::sp<android::hardware::audio::V7_1::IStreamOutLatencyModeCallback> const&)+36) (BuildId: 544a581a8a5464266e14c74f5aea9a1c)
05-24 15:39:41.747  1533  1533 F DEBUG   :       #02 pc 00043c0f  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::_hidl_setLatencyModeCallback(android::hidl::base::V1_0::BnHwBase*, android::hardware::Parcel const&, android::hardware::Parcel*, std::__1::function<void (android::hardware::Parcel&)>)+198) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 15:39:41.747  1533  1533 F DEBUG   :       #03 pc 00044703  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::onTransact(unsigned int, android::hardware::Parcel const&, android::hardware::Parcel*, unsigned int, std::__1::function<void (android::hardware::Parcel&)>)+1926) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 15:39:41.783  1024  1044 W ServiceManagerCppClient: Service package_native didn't start. Returning NULL
05-24 15:39:41.783  1024  1044 E ServiceUtilities: getCachedInfo: Cannot find package_native
05-24 15:39:41.839  1045  1045 W ServiceManagerCppClient: Service package_native didn't start. Returning NULL
05-24 15:39:41.839  1045  1045 E storaged: getService package_native failed
05-24 15:39:41.847  1045  1538 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder'...
05-24 15:39:42.699  1017  1056 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 15:39:42.700  1017  1056 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:6
05-24 15:39:42.929  1307  1372 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 15:39:42.930  1307  1372 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 15:39:42.931  1307  1372 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 15:39:42.932  1307  1372 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #34
05-24 15:39:42.933  1307  1372 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 15:39:42.934  1307  1372 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 15:39:42.935  1307  1372 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-24 15:39:43.035   826   830 W ServiceManagerCppClient: Service statscompanion didn't start. Returning NULL
05-24 15:39:43.035   826   830 E statsd  : Uid 1000 does not have the android.permission.REGISTER_STATS_PULL_ATOM permission when registering atom 10205 (-1)
05-24 15:39:43.049  1307  1371 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.USE_FINGERPRINT in package: org.mozilla.focus at: Binary XML file line #73
05-24 15:39:43.081  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.ACCESS_NETWORK_STATE in package: com.google.android.gm at: Binary XML file line #311
05-24 15:39:43.081  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE in package: com.google.android.gm at: Binary XML file line #314
05-24 15:39:43.081  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE_DATA_SYNC in package: com.google.android.gm at: Binary XML file line #315
05-24 15:39:43.081  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.INTERNET in package: com.google.android.gm at: Binary XML file line #316
05-24 15:39:43.081  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.RECEIVE_BOOT_COMPLETED in package: com.google.android.gm at: Binary XML file line #317
05-24 15:39:43.081  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.VIBRATE in package: com.google.android.gm at: Binary XML file line #318
05-24 15:39:43.081  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WAKE_LOCK in package: com.google.android.gm at: Binary XML file line #319
05-24 15:39:43.081  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WRITE_EXTERNAL_STORAGE in package: com.google.android.gm at: Binary XML file line #321
05-24 15:39:43.082  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.POST_NOTIFICATIONS in package: com.google.android.gm at: Binary XML file line #322
05-24 15:39:43.082  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.c2dm.permission.RECEIVE in package: com.google.android.gm at: Binary XML file line #324
05-24 15:39:43.082  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.GET_PACKAGE_SIZE in package: com.google.android.gm at: Binary XML file line #325
05-24 15:39:43.082  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.providers.gsf.permission.READ_GSERVICES in package: com.google.android.gm at: Binary XML file line #326
05-24 15:39:43.082  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.providers.gsf.permission.READ_GSERVICES in package: com.google.android.gm at: Binary XML file line #331
05-24 15:39:43.082  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.GET_PACKAGE_SIZE in package: com.google.android.gm at: Binary XML file line #332
05-24 15:39:43.082  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.ACCESS_NETWORK_STATE in package: com.google.android.gm at: Binary XML file line #334
05-24 15:39:43.082  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.CAMERA in package: com.google.android.gm at: Binary XML file line #345
05-24 15:39:43.082  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE in package: com.google.android.gm at: Binary XML file line #360
05-24 15:39:43.082  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.INTERNET in package: com.google.android.gm at: Binary XML file line #361
05-24 15:39:43.082  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.RECORD_AUDIO in package: com.google.android.gm at: Binary XML file line #363
05-24 15:39:43.082  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WAKE_LOCK in package: com.google.android.gm at: Binary XML file line #364
05-24 15:39:43.082  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.POST_NOTIFICATIONS in package: com.google.android.gm at: Binary XML file line #373
05-24 15:39:43.083  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE_DATA_SYNC in package: com.google.android.gm at: Binary XML file line #379
05-24 15:39:43.083  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.gm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION in package: com.google.android.gm at: Binary XML file line #385
05-24 15:39:43.083  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.VIBRATE in package: com.google.android.gm at: Binary XML file line #386
05-24 15:39:43.083  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.READ_CONTACTS in package: com.google.android.gm at: Binary XML file line #387
05-24 15:39:43.083  1307  1370 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.hangouts.START_HANGOUT in package: com.google.android.gm at: Binary XML file line #388
05-24 15:39:43.700  1017  1056 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 15:39:43.700  1017  1056 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:7
05-24 15:39:43.818  1087  1367 W mtk_agpsd: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.system.suspend.ISystemSuspend/default
05-24 15:39:44.597  1307  1307 W AppIdPermissionPolicy: Ignoring permission com.google.android.gtalkservice.permission.GTALK_SERVICE declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-24 15:39:44.597  1307  1307 W AppIdPermissionPolicy: Ignoring permission com.android.vending.INTENT_VENDING_ONLY declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-24 15:39:44.597  1307  1307 W AppIdPermissionPolicy: Ignoring permission com.google.android.providers.settings.permission.WRITE_GSETTINGS declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-24 15:39:44.597  1307  1307 W AppIdPermissionPolicy: Ignoring permission com.google.android.providers.gsf.permission.WRITE_GSERVICES declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-24 15:39:44.598  1307  1307 W AppIdPermissionPolicy: Ignoring permission lineageos.permission.MANAGE_REMOTE_PREFERENCES declared in system package com.android.settings: already declared in another system package io.chaldeaprjkt.gamespace
05-24 15:39:44.602  1307  1307 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_TOPICS declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 15:39:44.602  1307  1307 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_ATTRIBUTION declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 15:39:44.602  1307  1307 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 15:39:44.602  1307  1307 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_AD_SELECTION declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 15:39:44.602  1307  1307 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_PROTECTED_SIGNALS declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 15:39:44.602  1307  1307 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_AD_ID declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-24 15:39:44.700  1017  1056 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 15:39:44.700  1017  1056 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:8
05-24 15:39:44.707  1307  1307 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.adservices/appid
05-24 15:39:44.708  1307  1307 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.permission/appid
05-24 15:39:44.712  1307  1307 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.btservices/appid
05-24 15:39:44.722  1307  1307 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.extservices/appid
05-24 15:39:44.726  1307  1307 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.nfcservices/appid
05-24 15:39:44.816  1307  1307 I SystemServiceManager: Starting com.android.server.pm.UserManagerService$LifeCycle
05-24 15:39:44.861  1045  1538 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder' successful after waiting 3014ms
05-24 15:39:44.931  1307  1568 W PackageManager: Skipping preparing app data for com.android.adservices
05-24 15:39:44.931  1307  1568 W PackageManager: Skipping preparing app data for com.android.permission
05-24 15:39:44.932  1307  1568 W PackageManager: Skipping preparing app data for com.android.btservices
05-24 15:39:44.933  1307  1568 W PackageManager: Skipping preparing app data for com.android.extservices
05-24 15:39:44.934  1307  1568 W PackageManager: Skipping preparing app data for com.android.nfcservices
05-24 15:39:45.054  1307  1307 I SystemServiceManager: Starting com.android.server.sensors.SensorService
05-24 15:39:45.055  1307  1307 I SystemServiceManager: Starting com.android.server.SystemConfigService
05-24 15:39:45.059  1307  1307 I SystemServiceManager: Starting com.android.server.BatteryService
05-24 15:39:45.059   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.sensors@2.1::ISensors/default in either framework or device VINTF manifest.
05-24 15:39:45.071  1307  1579 W SensorService: lsm6dso ACCELEROMETER's max range 78.453201293945 is not a multiple of the resolution 0.001200000057 - updated to 78.453605651855
05-24 15:39:45.071  1307  1579 I SensorService: lsm6dso ACCELEROMETER's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.071  1307  1579 W SensorService: mmc5603 MAGNETOMETER's max range 4912.000000000000 is not a multiple of the resolution 0.150000005960 - updated to 4912.050292968750
05-24 15:39:45.071  1307  1579 I SensorService: mmc5603 MAGNETOMETER's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.071  1307  1579 W SensorService: lsm6dso GYROSCOPE's max range 34.906600952148 is not a multiple of the resolution 0.001099999994 - updated to 34.906299591064
05-24 15:39:45.071  1307  1579 I SensorService: lsm6dso GYROSCOPE's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.071  1307  1579 I SensorService: tcs3701 PROXIMITY's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.071  1307  1579 W SensorService: UNCALI_MAG's max range 4912.000000000000 is not a multiple of the resolution 0.150000005960 - updated to 4912.050292968750
05-24 15:39:45.071  1307  1579 I SensorService: UNCALI_MAG's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.071  1307  1579 W SensorService: UNCALI_GYRO's max range 34.906600952148 is not a multiple of the resolution 0.001099999994 - updated to 34.906299591064
05-24 15:39:45.071  1307  1579 I SensorService: UNCALI_GYRO's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.071  1307  1579 I SensorService: SIGNIFICANT_MOTION's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.071  1307  1579 I SensorService: STEP_DETECTOR's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.071  1307  1579 I SensorService: STEP_COUNTER's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.071  1307  1579 I SensorService: DEVICE_ORIENTATION's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.071  1307  1579 I SensorService: STATIONARY_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.071  1307  1579 I SensorService: MOTION_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.071  1307  1579 W SensorService: UNCALI_ACC's max range 39.226600646973 is not a multiple of the resolution 0.001200000057 - updated to 39.226802825928
05-24 15:39:45.071  1307  1579 I SensorService: UNCALI_ACC's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.071  1307  1579 I SensorService: tcs3701 LIGHT's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.071  1307  1579 I SensorService: RAW_MAG's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.071  1307  1579 I SensorService: mn29005 rear_als's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.072  1307  1579 I SensorService: ai_shutter's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.072  1307  1579 I SensorService: STEP_DETECTOR_WAKEUP's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.072  1307  1579 I SensorService: PICKUP_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.072  1307  1579 I SensorService: FP_DISPLAY's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.072  1307  1579 I SensorService: LUX_AOD's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.072  1307  1579 I SensorService: PEDO_MINUTE's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.072  1307  1579 I SensorService: OPLUS_ACTIVITY_RECOGNITION's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.072  1307  1579 I SensorService: ELEVATOR_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-24 15:39:45.082   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.light@2.0::ILight/default in either framework or device VINTF manifest.
05-24 15:39:45.084  1307  1307 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 15:39:45.086  1307  1307 I HealthServiceWrapperHidl: health: HealthServiceWrapper listening to instance default
05-24 15:39:45.087  1307  1307 I BatteryService: health: Waited 0ms and received the update.
05-24 15:39:45.089  1307  1307 I SystemServiceManager: Starting com.android.server.usage.UsageStatsService
05-24 15:39:45.094  1307  1307 I SystemServiceManager: Starting com.android.server.webkit.WebViewUpdateService
05-24 15:39:45.096  1307  1307 I SystemServiceManager: Starting com.android.server.CachedDeviceStateService
05-24 15:39:45.097  1307  1307 I SystemServiceManager: Starting com.android.server.BinderCallsStatsService$LifeCycle
05-24 15:39:45.098  1307  1307 I SystemServiceManager: Starting com.android.server.LooperStatsService$Lifecycle
05-24 15:39:45.100  1307  1307 I SystemServiceManager: Starting com.android.server.rollback.RollbackManagerService
05-24 15:39:45.115  1307  1307 I SystemServiceManager: Starting com.android.server.os.NativeTombstoneManagerService
05-24 15:39:45.117  1307  1307 I SystemServiceManager: Starting com.android.server.os.BugreportManagerService
05-24 15:39:45.119  1307  1307 I SystemServiceManager: Starting com.android.server.gpu.GpuService
05-24 15:39:45.120  1307  1307 I SystemServiceManager: Starting com.android.server.security.rkp.RemoteProvisioningService
05-24 15:39:45.124  1307  1307 I SystemServiceManager: Starting com.android.server.security.KeyChainSystemService
05-24 15:39:45.126  1307  1307 I SystemServiceManager: Starting com.android.server.BinaryTransparencyService
05-24 15:39:45.129  1307  1307 I TransparencyService: Started BinaryTransparencyService
05-24 15:39:45.130  1307  1307 I SystemServiceManager: Starting com.android.server.telecom.TelecomLoaderService
05-24 15:39:45.137  1307  1307 I SystemServiceManager: Starting com.android.server.accounts.AccountManagerService$Lifecycle
05-24 15:39:45.140  1307  1307 I SystemServiceManager: Starting com.android.server.content.ContentService$Lifecycle
05-24 15:39:45.144  1307  1591 I SchedulingPolicyService: Moving 1090 back to group default
05-24 15:39:45.287  1307  1307 I Freezer : Cannot open freezer path "/sys/fs/cgroup/uid_1000/pid_1307/frozen/freezer.state": No such file or directory
05-24 15:39:45.288  1307  1307 I SystemServiceManager: Starting com.android.server.deviceconfig.DeviceConfigInit$Lifecycle
05-24 15:39:45.290  1307  1307 I SystemServiceManager: Starting com.android.server.DropBoxManagerService
05-24 15:39:45.292  1307  1307 I SystemServiceManager: Starting com.android.ecm.EnhancedConfirmationService
05-24 15:39:45.302  1307  1307 I SystemServiceManager: Starting com.android.server.power.hint.HintManagerService
05-24 15:39:45.307   828   828 E ActivityRecognitionHardware: activity_recognition HAL is deprecated. class_init is effectively a no-op
05-24 15:39:45.312  1307  1307 I SystemServiceManager: Starting com.android.role.RoleService
05-24 15:39:45.319  1307  1307 I SystemServiceManager: Starting com.android.server.vibrator.VibratorManagerService$Lifecycle
05-24 15:39:45.343  1307  1307 I SystemServiceManager: Starting com.android.server.alarm.AlarmManagerService
05-24 15:39:45.372  1307  1307 I InputManager: Initializing input manager, mUseDevInputEventForAudioJack=true
05-24 15:39:45.373  1307  1307 I SystemServiceManager: Starting com.android.server.devicestate.DeviceStateManagerService
05-24 15:39:45.378  1307  1307 E DeviceStateManagerService: Cannot notify device state info change before the initial state has been committed.
05-24 15:39:45.378  1307  1307 I DeviceStateManagerService: Cannot notify device state info change when pending state is present.
05-24 15:39:45.381  1307  1307 I SystemServiceManager: Starting com.android.server.camera.CameraServiceProxy
05-24 15:39:45.385  1307  1307 I SystemServiceManager: Starting phase 200
05-24 15:39:45.413   828   828 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlags
05-24 15:39:45.413   828   828 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlagsImpl
05-24 15:39:45.413   828   828 W Zygote  : Class not found for preloading: android.media.audio.Flags
05-24 15:39:45.477  1307  1307 I SystemServiceManager: Starting com.android.server.bluetooth.BluetoothService
05-24 15:39:45.478  1307  1605 I HidlServiceManagement: Registered android.frameworks.schedulerservice@1.0::ISchedulingPolicyService/default
05-24 15:39:45.479  1307  1604 I HidlServiceManagement: Registered android.frameworks.sensorservice@1.0::ISensorManager/default
05-24 15:39:45.488  1307  1307 I SystemServiceManager: Starting com.android.server.connectivity.IpConnectivityMetrics
05-24 15:39:45.488  1307  1307 I SystemServiceManager: Starting com.android.server.net.watchlist.NetworkWatchlistService$Lifecycle
05-24 15:39:45.492  1307  1307 I SystemServiceManager: Starting com.android.server.pinner.PinnerService
05-24 15:39:45.495  1307  1307 I SystemServiceManager: Starting com.android.server.integrity.AppIntegrityManagerService
05-24 15:39:45.502  1307  1307 I SystemServiceManager: Starting com.android.server.logcat.LogcatManagerService
05-24 15:39:45.521  1307  1307 I SystemServiceManager: Starting com.android.server.inputmethod.InputMethodManagerService$Lifecycle
05-24 15:39:45.533  1307  1307 I SystemServiceManager: Starting com.android.server.accessibility.AccessibilityManagerService$Lifecycle
05-24 15:39:45.546  1307  1307 I SystemServiceManager: Starting com.android.server.StorageManagerService$Lifecycle
05-24 15:39:45.549  1307  1342 W PinnerService: Could not find pinlist.meta for "/system_ext/priv-app/SystemUI/SystemUI.apk": pinning as blob
05-24 15:39:45.556  1307  1307 I SystemServiceManager: Starting com.android.server.usage.StorageStatsService$Lifecycle
05-24 15:39:45.561  1307  1307 I SystemServiceManager: Starting com.android.server.UiModeManagerService
05-24 15:39:45.563  1307  1307 I SystemServiceManager: Starting com.android.server.locales.LocaleManagerService
05-24 15:39:45.565  1307  1307 I SystemServiceManager: Starting com.android.server.grammaticalinflection.GrammaticalInflectionService
05-24 15:39:45.566  1307  1307 I SystemServiceManager: Starting com.android.server.apphibernation.AppHibernationService
05-24 15:39:45.569  1307  1307 I SystemServiceManager: Starting com.android.server.locksettings.LockSettingsService$Lifecycle
05-24 15:39:45.581  1307  1307 I SystemServiceManager: Starting com.android.server.pdb.PersistentDataBlockService
05-24 15:39:45.581  1307  1307 I SystemServiceManager: Starting com.android.server.testharness.TestHarnessModeService
05-24 15:39:45.582  1307  1307 I SystemServiceManager: Starting com.android.server.oemlock.OemLockService
05-24 15:39:45.584   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.oemlock@1.0::IOemLock/default in either framework or device VINTF manifest.
05-24 15:39:45.586  1307  1307 I SystemServiceManager: Starting com.android.server.DeviceIdleController
05-24 15:39:45.593  1307  1339 E PersistentDataBlockService: FRP deactivation failed with secret 83a6804305e08a3ba2abf5a4b17e6c7460ff0d85c20f0b4d50be1603da087545
05-24 15:39:45.593  1307  1339 W PersistentDataBlockService: Failed to deactivate with primary secret file, trying backup.
05-24 15:39:45.593  1307  1339 I PersistentDataBlockService: Failed to read FRP secret file: /data/system/frp_secret_tmp NoSuchFileException
05-24 15:39:45.593  1307  1339 W PersistentDataBlockService: Failed to deactivate with backup secret file, trying default secret.
05-24 15:39:45.593  1307  1339 I PersistentDataBlockService: FRP secret matched, FRP deactivated.
05-24 15:39:45.594  1307  1307 I SystemServiceManager: Starting com.android.server.devicepolicy.DevicePolicyManagerService$Lifecycle
05-24 15:39:45.610  1307  1307 I SystemServiceManager: Starting com.android.server.systemcaptions.SystemCaptionsManagerService
05-24 15:39:45.610  1307  1307 I SystemServiceManager: Starting com.android.server.texttospeech.TextToSpeechManagerService
05-24 15:39:45.611  1307  1307 I SystemServiceManager: Starting com.android.server.wearable.WearableSensingManagerService
05-24 15:39:45.613  1307  1307 I SystemServiceManager: Starting com.android.server.ondeviceintelligence.OnDeviceIntelligenceManagerService
05-24 15:39:45.614  1307  1307 I SystemServiceManager: Starting com.android.server.speech.SpeechRecognitionManagerService
05-24 15:39:45.615  1307  1307 I SystemServiceManager: Starting com.android.server.appprediction.AppPredictionManagerService
05-24 15:39:45.616  1307  1307 I SystemServiceManager: Starting com.android.server.contentsuggestions.ContentSuggestionsManagerService
05-24 15:39:45.617  1307  1307 I SystemServiceManager: Starting com.android.server.contextualsearch.ContextualSearchManagerService
05-24 15:39:45.621  1307  1307 I FontManagerService: Using optimized boot-time font loading.
05-24 15:39:45.622  1307  1307 I SystemServiceManager: Starting com.android.server.textservices.TextServicesManagerService$Lifecycle
05-24 15:39:45.623  1307  1307 I SystemServiceManager: Starting com.android.server.textclassifier.TextClassificationManagerService$Lifecycle
05-24 15:39:45.625  1307  1307 I SystemServiceManager: Starting com.android.server.NetworkScoreService$Lifecycle
05-24 15:39:45.626  1307  1307 I NetworkScoreService: Registering network_score
05-24 15:39:45.627  1307  1307 I SystemServiceManager: Starting com.android.server.NetworkStatsServiceInitializer
05-24 15:39:45.655  1307  1307 I NetworkStatsServiceInitializer: Registering netstats
05-24 15:39:45.663  1307  1307 I SystemServiceManager: Starting com.android.server.wifi.WifiService
05-24 15:39:45.666  1307  1307 I WifiContext: Got resolveInfos for com.android.server.wifi.intent.action.SERVICE_WIFI_RESOURCES_APK: [ResolveInfo{a716e78 com.android.wifi.resources/android.app.Activity m=0x108000 userHandle=UserHandle{0}}]
05-24 15:39:45.672  1307  1307 E WifiScoringParams: 	at com.android.server.wifi.ScoringParams.<init>(ScoringParams.java:262)
05-24 15:39:45.672  1307  1307 E WifiScoringParams: 	at com.android.server.wifi.WifiInjector.<init>(WifiInjector.java:319)
05-24 15:39:45.672  1307  1307 E WifiScoringParams: 	at com.android.server.wifi.WifiService.<init>(WifiService.java:44)
05-24 15:39:45.672  1307  1307 E WifiScoringParams: 	at com.android.server.SystemServiceManager.startService(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:9)
05-24 15:39:45.672  1307  1307 E WifiScoringParams: 	at com.android.server.SystemServiceManager.startServiceFromJar(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:88)
05-24 15:39:45.672  1307  1307 E WifiScoringParams: 	at com.android.server.SystemServer.startOtherServices(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:322)
05-24 15:39:45.672  1307  1307 E WifiScoringParams: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:597)
05-24 15:39:45.672  1307  1307 E WifiScoringParams: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:889)
05-24 15:39:45.678  1307  1307 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.2::IServiceManager/default
05-24 15:39:45.684  1307  1307 I SupplicantStaIfaceHal: Initializing SupplicantStaIfaceHal using AIDL implementation.
05-24 15:39:45.685  1307  1307 I SupplicantP2pIfaceHal: Initializing SupplicantP2pIfaceHal using AIDL implementation.
05-24 15:39:45.700  1017  1056 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-24 15:39:45.701  1017  1056 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:9
05-24 15:39:45.704  1307  1342 W PinnerService: Could not find pinlist.meta for "/system_ext/priv-app/Launcher3QuickStep/Launcher3QuickStep.apk": pinning as blob
05-24 15:39:45.712  1307  1307 I WifiService: Registering wifi
05-24 15:39:45.713  1307  1307 I SystemServiceManager: Starting com.android.server.wifi.scanner.WifiScanningService
05-24 15:39:45.713  1307  1307 I WifiScanningService: Creating wifiscanner
05-24 15:39:45.715  1307  1307 I WifiScanningService: Publishing wifiscanner
05-24 15:39:45.716  1307  1307 I SystemServiceManager: Starting com.android.server.wifi.p2p.WifiP2pService
05-24 15:39:45.717  1307  1307 I WifiContext: Got resolveInfos for com.android.server.wifi.intent.action.SERVICE_WIFI_RESOURCES_APK: [ResolveInfo{54a7e5e com.android.wifi.resources/android.app.Activity m=0x108000 userHandle=UserHandle{0}}]
05-24 15:39:45.720  1307  1307 I WifiP2pService: Registering wifip2p
05-24 15:39:45.722  1307  1307 I SystemServiceManager: Starting com.android.server.ConnectivityServiceInitializer
05-24 15:39:45.750  1307  1307 I MulticastRoutingCoordinatorService: socket created for multicast routing: java.io.FileDescriptor@aa4688c
05-24 15:39:45.753  1307  1638 W BroadcastLoopers: Found previously unknown looper Thread[NsdService,5,main]
05-24 15:39:45.759  1307  1307 I ConnectivityServiceInitializer: Registering ethernet
05-24 15:39:45.759  1307  1307 I ConnectivityServiceInitializer: Registering connectivity
05-24 15:39:45.760  1307  1307 I ConnectivityServiceInitializer: Registering ipsec
05-24 15:39:45.760  1307  1307 I ConnectivityServiceInitializer: Registering connectivity_native
05-24 15:39:45.761  1307  1307 I ConnectivityServiceInitializer: Registering servicediscovery
05-24 15:39:45.761  1307  1307 I ConnectivityServiceInitializer: Registering nearby
05-24 15:39:45.764  1307  1307 I SystemServiceManager: Starting com.android.server.ConnectivityServiceInitializerB
05-24 15:39:45.766  1307  1307 I ConnectivityServiceInitializerB: Registering vcn_management
05-24 15:39:45.766  1307  1307 I SystemUpdateManagerService: No existing info file /data/system/system-update-info.xml
05-24 15:39:45.768  1307  1307 I SystemServiceManager: Starting com.android.server.notification.NotificationManagerService
05-24 15:39:45.815  1307  1307 I NotificationManagerService.NotificationListeners: Read notification listener permissions from xml
05-24 15:39:45.816  1307  1307 W ActivityManager: Unable to start service Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener } U=0: not found
05-24 15:39:45.816  1307  1307 W NotificationManagerService.NotificationListeners: Unable to bind notification listener service: Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener (has extras) } in user 0
05-24 15:39:45.816  1307  1307 I NotificationManagerService.NotificationAssistants: Read notification assistant permissions from xml
05-24 15:39:45.819  1307  1307 I ConditionProviders: Read condition provider permissions from xml
05-24 15:39:45.819  1307  1307 I ConditionProviders: Read condition provider permissions from xml
05-24 15:39:45.820  1307  1307 I ConditionProviders:  Allowing condition provider android.ext.services/android.ext.services.notification.Assistant (userSet: true)
05-24 15:39:45.827  1307  1307 W SystemServiceManager: Service com.android.server.notification.NotificationManagerService took 58 ms in onStart
05-24 15:39:45.829  1307  1307 I SystemServiceManager: Starting com.android.server.storage.DeviceStorageMonitorService
05-24 15:39:45.830  1307  1307 I SystemServiceManager: Starting com.android.server.timedetector.TimeDetectorService$Lifecycle
05-24 15:39:45.833  1307  1307 I SystemServiceManager: Starting com.android.server.location.LocationManagerService$Lifecycle
05-24 15:39:45.839  1307  1307 I SystemServiceManager: Starting com.android.server.timezonedetector.TimeZoneDetectorService$Lifecycle
05-24 15:39:45.843  1307  1307 I SystemServiceManager: Starting com.android.server.location.altitude.AltitudeService$Lifecycle
05-24 15:39:45.845  1307  1307 I SystemServiceManager: Starting com.android.server.timezonedetector.location.LocationTimeZoneManagerService$Lifecycle
05-24 15:39:45.846  1307  1307 I SystemServiceManager: Starting com.android.server.search.SearchManagerService$Lifecycle
05-24 15:39:45.847  1307  1307 I SystemServiceManager: Starting com.android.server.wallpaper.WallpaperManagerService$Lifecycle
05-24 15:39:45.850  1307  1307 I SystemServiceManager: Starting com.android.server.audio.AudioService$Lifecycle
05-24 15:39:46.184  1646  1646 I HidlServiceManagement: Registered android.hardware.audio@7.1::IDevicesFactory/default
05-24 15:39:46.185  1646  1646 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-24 15:39:46.185  1646  1646 I LegacySupport: Registration complete for android.hardware.audio@7.1::IDevicesFactory/default.
05-24 15:39:46.197  1646  1646 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-24 15:39:46.197  1646  1646 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-24 15:39:46.205  1646  1646 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:39:46.289  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.289  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.289  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.290  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.290  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.290  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.290  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.290  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.290  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.290  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.290  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.290  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.291  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.291  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.291  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.291  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.291  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.291  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.341  1653  1653 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:39:46.342  1653  1653 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:39:46.342   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:39:46.344  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.344  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.347  1646  1646 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 15:39:46.351  1653  1653 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:39:46.351   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:39:46.352  1653  1653 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:39:46.358  1653  1653 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-24 15:39:46.366  1646  1646 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xb04efba3)
05-24 15:39:46.369  1653  1653 E libxml2 : I/O warning : failed to load "/odm/etc/virtual_audio_policy_configuration.xml": No such file or directory
05-24 15:39:46.369  1653  1653 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-24 15:39:46.369  1653  1653 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-24 15:39:46.371  1646  1646 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x9acf2ea3)
05-24 15:39:46.381  1646  1646 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:39:46.381  1646  1652 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-24 15:39:46.382  1646  1646 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xd1114615)
05-24 15:39:46.383  1646  1652 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-24 15:39:46.384  1646  1646 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x85e1dcfb)
05-24 15:39:46.386  1646  1646 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-24 15:39:46.387   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:39:46.387  1646  1646 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-24 15:39:46.388   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:39:46.389  1646  1652 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:39:46.390  1646  1646 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-24 15:39:46.390   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:39:46.390  1646  1646 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-24 15:39:46.390  1646  1646 W audiohalservice: Could not register Bluetooth Audio API
05-24 15:39:46.391   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-24 15:39:46.391  1646  1646 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-24 15:39:46.391  1646  1646 W audiohalservice: Could not register Bluetooth Audio Offload API
05-24 15:39:46.400  1646  1646 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-24 15:39:46.400  1646  1646 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-24 15:39:46.400  1646  1646 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-24 15:39:46.487  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.487  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.487  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.488  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.488  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.488  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.488  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.488  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.488  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.488  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.488  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.489  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.489  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.489  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.489  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.489  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.489  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.489  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.535  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.535  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:46.538  1646  1652 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 15:39:46.557  1646  1652 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xa96ba6e3)
05-24 15:39:46.557  1646  1663 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-24 15:39:46.560  1646  1663 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0xa96ba6e3)
05-24 15:39:46.562  1646  1663 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0xa96ba6e3)
05-24 15:39:46.611  1646  1652 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-24 15:39:46.618  1646  1652 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 15:39:46.619  1646  1652 W AudioSmartPaController: init(), dlopen failed, dlerror = dlopen failed: library "libfile_op.so" not found: needed by /vendor/lib/libnxp_extamp_intf.so in namespace (default)
05-24 15:39:46.643  1646  1652 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-24 15:39:46.657  1646  1652 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 15:39:46.657  1646  1652 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 15:39:46.657  1646  1652 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 15:39:46.663  1646  1652 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 15:39:46.663  1646  1652 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 15:39:46.664  1646  1652 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-24 15:39:46.668  1646  1652 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-24 15:39:46.668  1646  1652 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-24 15:39:46.669  1653  1653 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 15:39:46.669  1653  1653 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 15:39:46.669  1646  1646 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-24 15:39:46.669  1646  1646 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-24 15:39:46.670  1646  1646 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 15:39:46.670  1653  1653 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-24 15:39:46.670  1653  1653 I AudioFlinger: openOutput() this 0xb40000768f8551f0, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-24 15:39:46.671  1653  1653 I AudioFlinger: operator(): Using 3000 ms as standby time
05-24 15:39:46.672  1653  1653 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 15:39:46.672  1653  1653 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:39:46.672   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:39:46.673  1653  1653 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:39:46.680  1646  1678 E EffectsFactoryConfigLoader: loadLibrary Could not find library in effect directories: libaudiopreprocessing_mtk.so
05-24 15:39:46.680  1646  1678 E EffectsFactoryConfigLoader: EffectLoadXmlEffectConfig 4 errors during loading of configuration: /vendor/etc/audio_effects.xml
05-24 15:39:46.681  1653  1679 I AudioFlinger: AudioFlinger's thread 0xb4000078c833c760 tid=1679 ready to run
05-24 15:39:46.709  1646  1665 W audio_messenger_ipi: read_ipi_thread(), read dsp fail!! length_read: 0, errno: 0
05-24 15:39:46.714  1683  1683 E DEBUG   : failed to read process info: failed to open /proc/1646: No such file or directory
05-24 15:39:46.773  1683  1683 F DEBUG   : Cmdline: /vendor/bin/hw/android.hardware.audio.service
05-24 15:39:46.773  1683  1683 F DEBUG   : pid: 1646, tid: 1678, name: HwBinder:1646_5  >>> /vendor/bin/hw/android.hardware.audio.service <<<
05-24 15:39:46.773  1683  1683 F DEBUG   :       #01 pc 0002651d  /vendor/lib/hw/<EMAIL> (android::hardware::audio::V7_1::implementation::StreamOut::setLatencyModeCallback(android::sp<android::hardware::audio::V7_1::IStreamOutLatencyModeCallback> const&)+36) (BuildId: 544a581a8a5464266e14c74f5aea9a1c)
05-24 15:39:46.773  1683  1683 F DEBUG   :       #02 pc 00043c0f  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::_hidl_setLatencyModeCallback(android::hidl::base::V1_0::BnHwBase*, android::hardware::Parcel const&, android::hardware::Parcel*, std::__1::function<void (android::hardware::Parcel&)>)+198) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 15:39:46.773  1683  1683 F DEBUG   :       #03 pc 00044703  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::onTransact(unsigned int, android::hardware::Parcel const&, android::hardware::Parcel*, unsigned int, std::__1::function<void (android::hardware::Parcel&)>)+1926) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 15:39:46.810  1653  1657 E HalDeathHandler: HAL server crashed, audio server is restarting
05-24 15:39:48.768  1307  1365 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.adservices/appid
05-24 15:39:48.774  1307  1365 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.permission/appid
05-24 15:39:48.790  1307  1365 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.btservices/appid
05-24 15:39:48.814  1307  1365 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.extservices/appid
05-24 15:39:48.820  1307  1365 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.nfcservices/appid
05-24 15:39:50.856  1307  1307 E AudioProductStrategies-JNI: AudioSystem::listAudioProductStrategies error -32
05-24 15:39:50.856  1307  1307 W AudioProductStrategy: : initializeAudioProductStrategies failed
05-24 15:39:51.206  1691  1691 I HidlServiceManagement: Registered android.hardware.audio@7.1::IDevicesFactory/default
05-24 15:39:51.207  1691  1691 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-24 15:39:51.208  1691  1691 I LegacySupport: Registration complete for android.hardware.audio@7.1::IDevicesFactory/default.
05-24 15:39:51.214  1691  1691 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-24 15:39:51.214  1691  1691 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-24 15:39:51.224  1691  1691 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:39:51.314  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.314  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.314  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.314  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.314  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.314  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.314  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.314  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.314  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.315  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.315  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.315  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.315  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.315  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.315  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.315  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.315  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.315  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.354  1698  1698 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:39:51.355  1698  1698 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:39:51.356   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:39:51.361  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.361  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.363  1698  1698 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:39:51.363   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:39:51.363  1691  1691 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 15:39:51.364  1698  1698 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:39:51.369  1698  1698 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-24 15:39:51.379  1698  1698 E libxml2 : I/O warning : failed to load "/odm/etc/virtual_audio_policy_configuration.xml": No such file or directory
05-24 15:39:51.379  1698  1698 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-24 15:39:51.379  1698  1698 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-24 15:39:51.381  1691  1691 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xf2c9de19)
05-24 15:39:51.386  1691  1691 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x54d89d55)
05-24 15:39:51.397  1691  1691 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:39:51.398  1691  1697 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-24 15:39:51.399  1691  1691 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x2fe9b809)
05-24 15:39:51.399  1691  1697 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-24 15:39:51.400  1691  1691 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x7bdbba4d)
05-24 15:39:51.403  1691  1691 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-24 15:39:51.403   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:39:51.404  1691  1691 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-24 15:39:51.405  1691  1697 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:39:51.405   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:39:51.405  1691  1691 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-24 15:39:51.405   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:39:51.406  1691  1691 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-24 15:39:51.406  1691  1691 W audiohalservice: Could not register Bluetooth Audio API
05-24 15:39:51.406   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-24 15:39:51.407  1691  1691 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-24 15:39:51.407  1691  1691 W audiohalservice: Could not register Bluetooth Audio Offload API
05-24 15:39:51.414  1691  1691 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-24 15:39:51.414  1691  1691 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-24 15:39:51.414  1691  1691 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-24 15:39:51.470  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.470  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.470  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.470  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.470  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.470  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.471  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.471  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.471  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.471  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.471  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.471  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.471  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.471  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.472  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.472  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.472  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.472  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.525  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.525  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:51.528  1691  1697 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 15:39:51.549  1691  1697 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xba0398dd)
05-24 15:39:51.550  1691  1708 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-24 15:39:51.552  1691  1708 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0xba0398dd)
05-24 15:39:51.554  1691  1708 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0xba0398dd)
05-24 15:39:51.619  1691  1697 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-24 15:39:51.626  1691  1697 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 15:39:51.627  1691  1697 W AudioSmartPaController: init(), dlopen failed, dlerror = dlopen failed: library "libfile_op.so" not found: needed by /vendor/lib/libnxp_extamp_intf.so in namespace (default)
05-24 15:39:51.650  1691  1697 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-24 15:39:51.662  1691  1697 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 15:39:51.662  1691  1697 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 15:39:51.662  1691  1697 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 15:39:51.668  1691  1697 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 15:39:51.668  1691  1697 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 15:39:51.668  1691  1697 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-24 15:39:51.672  1691  1697 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-24 15:39:51.672  1691  1697 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-24 15:39:51.673  1698  1698 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 15:39:51.673  1698  1698 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 15:39:51.674  1691  1691 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-24 15:39:51.674  1691  1691 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-24 15:39:51.674  1691  1691 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 15:39:51.674  1698  1698 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-24 15:39:51.675  1698  1698 I AudioFlinger: openOutput() this 0xb4000076019a9840, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-24 15:39:51.676  1698  1698 I AudioFlinger: operator(): Using 3000 ms as standby time
05-24 15:39:51.676  1698  1698 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 15:39:51.676  1698  1698 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:39:51.677   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:39:51.677  1698  1698 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:39:51.685  1691  1691 E EffectsFactoryConfigLoader: loadLibrary Could not find library in effect directories: libaudiopreprocessing_mtk.so
05-24 15:39:51.685  1691  1691 E EffectsFactoryConfigLoader: EffectLoadXmlEffectConfig 4 errors during loading of configuration: /vendor/etc/audio_effects.xml
05-24 15:39:51.686  1698  1722 I AudioFlinger: AudioFlinger's thread 0xb400007890758760 tid=1722 ready to run
05-24 15:39:51.716  1691  1710 W audio_messenger_ipi: read_ipi_thread(), read dsp fail!! length_read: 0, errno: 0
05-24 15:39:51.720  1726  1726 E DEBUG   : failed to read process info: failed to open /proc/1691: No such file or directory
05-24 15:39:51.780  1726  1726 F DEBUG   : Cmdline: /vendor/bin/hw/android.hardware.audio.service
05-24 15:39:51.780  1726  1726 F DEBUG   : pid: 1691, tid: 1721, name: HwBinder:1691_3  >>> /vendor/bin/hw/android.hardware.audio.service <<<
05-24 15:39:51.780  1726  1726 F DEBUG   :       #01 pc 0002651d  /vendor/lib/hw/<EMAIL> (android::hardware::audio::V7_1::implementation::StreamOut::setLatencyModeCallback(android::sp<android::hardware::audio::V7_1::IStreamOutLatencyModeCallback> const&)+36) (BuildId: 544a581a8a5464266e14c74f5aea9a1c)
05-24 15:39:51.780  1726  1726 F DEBUG   :       #02 pc 00043c0f  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::_hidl_setLatencyModeCallback(android::hidl::base::V1_0::BnHwBase*, android::hardware::Parcel const&, android::hardware::Parcel*, std::__1::function<void (android::hardware::Parcel&)>)+198) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 15:39:51.780  1726  1726 F DEBUG   :       #03 pc 00044703  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::onTransact(unsigned int, android::hardware::Parcel const&, android::hardware::Parcel*, unsigned int, std::__1::function<void (android::hardware::Parcel&)>)+1926) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 15:39:51.806  1698  1702 E HalDeathHandler: HAL server crashed, audio server is restarting
05-24 15:39:55.049  1307  1572 W OverlayManager: service 'idmap' died
05-24 15:39:55.857  1307  1307 E AudioSystem-JNI: Command failed for android_media_AudioSystem_setParameters: -32
05-24 15:39:56.230  1741  1741 I HidlServiceManagement: Registered android.hardware.audio@7.1::IDevicesFactory/default
05-24 15:39:56.230  1741  1741 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-24 15:39:56.231  1741  1741 I LegacySupport: Registration complete for android.hardware.audio@7.1::IDevicesFactory/default.
05-24 15:39:56.239  1741  1741 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-24 15:39:56.240  1741  1741 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-24 15:39:56.248  1741  1741 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:39:56.340  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.340  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.340  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.340  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.340  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.340  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.340  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.340  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.341  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.341  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.341  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.341  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.341  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.341  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.341  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.341  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.341  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.342  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.354  1748  1748 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:39:56.356  1748  1748 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:39:56.357   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:39:56.368  1748  1748 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:39:56.369   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:39:56.371  1748  1748 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:39:56.377  1748  1748 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-24 15:39:56.387  1748  1748 E libxml2 : I/O warning : failed to load "/odm/etc/virtual_audio_policy_configuration.xml": No such file or directory
05-24 15:39:56.387  1748  1748 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-24 15:39:56.387  1748  1748 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-24 15:39:56.387  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.387  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.390  1741  1741 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 15:39:56.409  1741  1741 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xe8994cf3)
05-24 15:39:56.413  1741  1741 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0xbc6fc89)
05-24 15:39:56.426  1741  1741 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:39:56.427  1741  1741 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x7e0652e5)
05-24 15:39:56.428  1741  1741 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0xbda13cd)
05-24 15:39:56.428  1741  1747 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-24 15:39:56.430  1741  1747 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-24 15:39:56.431  1741  1741 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-24 15:39:56.432   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:39:56.432  1741  1741 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-24 15:39:56.432   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:39:56.433  1741  1741 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-24 15:39:56.433   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:39:56.433  1741  1741 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-24 15:39:56.433  1741  1741 W audiohalservice: Could not register Bluetooth Audio API
05-24 15:39:56.433   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-24 15:39:56.434  1741  1747 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:39:56.434  1741  1741 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-24 15:39:56.434  1741  1741 W audiohalservice: Could not register Bluetooth Audio Offload API
05-24 15:39:56.439  1741  1741 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-24 15:39:56.440  1741  1741 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-24 15:39:56.440  1741  1741 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-24 15:39:56.528  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.528  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.528  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.529  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.529  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.529  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.529  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.529  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.529  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.529  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.529  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.530  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.530  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.530  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.530  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.530  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.530  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.530  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.577  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.577  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:39:56.581  1741  1747 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 15:39:56.599  1741  1747 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x729857c3)
05-24 15:39:56.600  1741  1758 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-24 15:39:56.603  1741  1758 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0x729857c3)
05-24 15:39:56.606  1741  1758 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0x729857c3)
05-24 15:39:56.684  1741  1747 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-24 15:39:56.691  1741  1747 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 15:39:56.692  1741  1747 W AudioSmartPaController: init(), dlopen failed, dlerror = dlopen failed: library "libfile_op.so" not found: needed by /vendor/lib/libnxp_extamp_intf.so in namespace (default)
05-24 15:39:56.714  1741  1747 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-24 15:39:56.727  1741  1747 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 15:39:56.728  1741  1747 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 15:39:56.728  1741  1747 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 15:39:56.733  1741  1747 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 15:39:56.733  1741  1747 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 15:39:56.733  1741  1747 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-24 15:39:56.736  1741  1747 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-24 15:39:56.736  1741  1747 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-24 15:39:56.737  1748  1748 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 15:39:56.738  1748  1748 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 15:39:56.739  1741  1746 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-24 15:39:56.739  1741  1746 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-24 15:39:56.740  1741  1772 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 15:39:56.740  1748  1748 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-24 15:39:56.741  1748  1748 I AudioFlinger: openOutput() this 0xb40000759020a8a0, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-24 15:39:56.742  1748  1748 I AudioFlinger: operator(): Using 3000 ms as standby time
05-24 15:39:56.743  1748  1748 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 15:39:56.743  1748  1748 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:39:56.744   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:39:56.744  1748  1748 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:39:56.750  1741  1772 E EffectsFactoryConfigLoader: loadLibrary Could not find library in effect directories: libaudiopreprocessing_mtk.so
05-24 15:39:56.750  1741  1772 E EffectsFactoryConfigLoader: EffectLoadXmlEffectConfig 4 errors during loading of configuration: /vendor/etc/audio_effects.xml
05-24 15:39:56.751  1748  1773 I AudioFlinger: AudioFlinger's thread 0xb40000777c1c4760 tid=1773 ready to run
05-24 15:39:56.780  1741  1760 W audio_messenger_ipi: read_ipi_thread(), read dsp fail!! length_read: 0, errno: 0
05-24 15:39:56.787  1777  1777 E DEBUG   : failed to read process info: failed to open /proc/1741: No such file or directory
05-24 15:39:56.855  1777  1777 F DEBUG   : Cmdline: /vendor/bin/hw/android.hardware.audio.service
05-24 15:39:56.855  1777  1777 F DEBUG   : pid: 1741, tid: 1772, name: HwBinder:1741_4  >>> /vendor/bin/hw/android.hardware.audio.service <<<
05-24 15:39:56.855  1777  1777 F DEBUG   :       #01 pc 0002651d  /vendor/lib/hw/<EMAIL> (android::hardware::audio::V7_1::implementation::StreamOut::setLatencyModeCallback(android::sp<android::hardware::audio::V7_1::IStreamOutLatencyModeCallback> const&)+36) (BuildId: 544a581a8a5464266e14c74f5aea9a1c)
05-24 15:39:56.855  1777  1777 F DEBUG   :       #02 pc 00043c0f  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::_hidl_setLatencyModeCallback(android::hidl::base::V1_0::BnHwBase*, android::hardware::Parcel const&, android::hardware::Parcel*, std::__1::function<void (android::hardware::Parcel&)>)+198) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 15:39:56.855  1777  1777 F DEBUG   :       #03 pc 00044703  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::onTransact(unsigned int, android::hardware::Parcel const&, android::hardware::Parcel*, unsigned int, std::__1::function<void (android::hardware::Parcel&)>)+1926) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 15:39:56.890  1748  1752 E HalDeathHandler: HAL server crashed, audio server is restarting
05-24 15:40:00.858  1307  1307 E AudioSystem-JNI: Command failed for android_media_AudioSystem_setParameters: -32
05-24 15:40:01.239  1782  1782 I HidlServiceManagement: Registered android.hardware.audio@7.1::IDevicesFactory/default
05-24 15:40:01.239  1782  1782 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-24 15:40:01.240  1782  1782 I LegacySupport: Registration complete for android.hardware.audio@7.1::IDevicesFactory/default.
05-24 15:40:01.248  1782  1782 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-24 15:40:01.248  1782  1782 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-24 15:40:01.261  1782  1782 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:40:01.315  1789  1789 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:40:01.317  1789  1789 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:40:01.318   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:40:01.327  1789  1789 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:40:01.327   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:40:01.328  1789  1789 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:40:01.334  1789  1789 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-24 15:40:01.345  1789  1789 E libxml2 : I/O warning : failed to load "/odm/etc/virtual_audio_policy_configuration.xml": No such file or directory
05-24 15:40:01.345  1789  1789 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-24 15:40:01.345  1789  1789 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-24 15:40:01.346  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.346  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.346  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.346  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.346  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.346  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.346  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.346  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.346  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.347  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.347  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.347  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.347  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.347  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.347  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.347  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.347  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.347  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.392  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.392  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.395  1782  1782 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 15:40:01.413  1782  1782 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xc24be5bf)
05-24 15:40:01.418  1782  1782 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x6b40f39f)
05-24 15:40:01.433  1782  1782 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:40:01.434  1782  1782 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xbc7283d7)
05-24 15:40:01.435  1782  1782 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0xcb6236b5)
05-24 15:40:01.435  1782  1788 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-24 15:40:01.436  1782  1788 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-24 15:40:01.439  1782  1788 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:40:01.440  1782  1782 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-24 15:40:01.441   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:40:01.441  1782  1782 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-24 15:40:01.441   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:40:01.441  1782  1782 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-24 15:40:01.442   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:40:01.442  1782  1782 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-24 15:40:01.442  1782  1782 W audiohalservice: Could not register Bluetooth Audio API
05-24 15:40:01.442   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-24 15:40:01.442  1782  1782 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-24 15:40:01.443  1782  1782 W audiohalservice: Could not register Bluetooth Audio Offload API
05-24 15:40:01.450  1782  1782 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-24 15:40:01.450  1782  1782 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-24 15:40:01.450  1782  1782 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-24 15:40:01.528  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.528  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.529  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.529  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.529  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.530  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.530  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.530  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.530  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.530  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.530  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.530  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.531  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.531  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.531  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.531  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.531  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.531  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.582  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.582  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:01.585  1782  1788 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 15:40:01.606  1782  1788 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xe2a24e23)
05-24 15:40:01.607  1782  1799 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-24 15:40:01.608  1782  1799 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0xe2a24e23)
05-24 15:40:01.611  1782  1799 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0xe2a24e23)
05-24 15:40:01.669  1782  1788 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-24 15:40:01.675  1782  1788 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 15:40:01.675  1782  1788 W AudioSmartPaController: init(), dlopen failed, dlerror = dlopen failed: library "libfile_op.so" not found: needed by /vendor/lib/libnxp_extamp_intf.so in namespace (default)
05-24 15:40:01.697  1782  1788 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-24 15:40:01.709  1782  1788 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 15:40:01.709  1782  1788 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 15:40:01.709  1782  1788 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 15:40:01.714  1782  1788 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 15:40:01.714  1782  1788 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 15:40:01.714  1782  1788 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-24 15:40:01.719  1782  1788 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-24 15:40:01.719  1782  1788 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-24 15:40:01.721  1789  1789 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 15:40:01.721  1789  1789 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 15:40:01.722  1782  1782 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-24 15:40:01.722  1782  1782 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-24 15:40:01.723  1782  1782 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 15:40:01.723  1789  1789 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-24 15:40:01.724  1789  1789 I AudioFlinger: openOutput() this 0xb4000077e570c010, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-24 15:40:01.725  1789  1789 I AudioFlinger: operator(): Using 3000 ms as standby time
05-24 15:40:01.725  1789  1789 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 15:40:01.726  1789  1789 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:40:01.727   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:40:01.729  1789  1789 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:40:01.734  1782  1782 E EffectsFactoryConfigLoader: loadLibrary Could not find library in effect directories: libaudiopreprocessing_mtk.so
05-24 15:40:01.734  1782  1782 E EffectsFactoryConfigLoader: EffectLoadXmlEffectConfig 4 errors during loading of configuration: /vendor/etc/audio_effects.xml
05-24 15:40:01.735  1789  1812 I AudioFlinger: AudioFlinger's thread 0xb400007a0ab96760 tid=1812 ready to run
05-24 15:40:01.765  1782  1801 W audio_messenger_ipi: read_ipi_thread(), read dsp fail!! length_read: 0, errno: 0
05-24 15:40:01.769  1819  1819 E DEBUG   : failed to read process info: failed to open /proc/1782: No such file or directory
05-24 15:40:01.817  1819  1819 F DEBUG   : Cmdline: /vendor/bin/hw/android.hardware.audio.service
05-24 15:40:01.817  1819  1819 F DEBUG   : pid: 1782, tid: 1782, name: binder:1782_2  >>> /vendor/bin/hw/android.hardware.audio.service <<<
05-24 15:40:01.817  1819  1819 F DEBUG   :       #01 pc 0002651d  /vendor/lib/hw/<EMAIL> (android::hardware::audio::V7_1::implementation::StreamOut::setLatencyModeCallback(android::sp<android::hardware::audio::V7_1::IStreamOutLatencyModeCallback> const&)+36) (BuildId: 544a581a8a5464266e14c74f5aea9a1c)
05-24 15:40:01.817  1819  1819 F DEBUG   :       #02 pc 00043c0f  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::_hidl_setLatencyModeCallback(android::hidl::base::V1_0::BnHwBase*, android::hardware::Parcel const&, android::hardware::Parcel*, std::__1::function<void (android::hardware::Parcel&)>)+198) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 15:40:01.817  1819  1819 F DEBUG   :       #03 pc 00044703  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::onTransact(unsigned int, android::hardware::Parcel const&, android::hardware::Parcel*, unsigned int, std::__1::function<void (android::hardware::Parcel&)>)+1926) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 15:40:01.817  1819  1819 F DEBUG   :       #06 pc 00002d23  /vendor/bin/hw/android.hardware.audio.service (main+3150) (BuildId: 4f8cce90cd7ff9b304f84e6fd8ec6b0b)
05-24 15:40:01.817  1819  1819 F DEBUG   :       #07 pc 00036457  /apex/com.android.runtime/lib/bionic/libc.so (__libc_init+74) (BuildId: a6009addd32ae1399831564e0073b350)
05-24 15:40:01.850  1789  1812 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:01.850  1789  1792 E HalDeathHandler: HAL server crashed, audio server is restarting
05-24 15:40:05.859  1307  1307 E AudioSystem-JNI: Command failed for android_media_AudioSystem_setParameters: -32
05-24 15:40:06.252  1824  1824 I HidlServiceManagement: Registered android.hardware.audio@7.1::IDevicesFactory/default
05-24 15:40:06.252  1824  1824 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-24 15:40:06.253  1824  1824 I LegacySupport: Registration complete for android.hardware.audio@7.1::IDevicesFactory/default.
05-24 15:40:06.262  1824  1824 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-24 15:40:06.262  1824  1824 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-24 15:40:06.270  1824  1824 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:40:06.289   886   917 E GpuMem  : Failed to attach bpf program to gpu_mem/gpu_mem_total tracepoint [2(No such file or directory)]
05-24 15:40:06.289   886   917 E GpuMemTracer: Cannot initialize GpuMemTracer before GpuMem
05-24 15:40:06.312  1831  1831 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:40:06.313  1831  1831 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:40:06.314   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:40:06.322  1831  1831 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:40:06.322   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:40:06.323  1831  1831 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:40:06.329  1831  1831 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-24 15:40:06.338  1831  1831 E libxml2 : I/O warning : failed to load "/odm/etc/virtual_audio_policy_configuration.xml": No such file or directory
05-24 15:40:06.339  1831  1831 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-24 15:40:06.339  1831  1831 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-24 15:40:06.349  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.349  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.349  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.349  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.349  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.349  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.350  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.350  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.350  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.350  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.350  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.350  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.350  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.350  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.350  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.351  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.351  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.351  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.397  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.398  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.401  1824  1824 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 15:40:06.419  1824  1824 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xbd1aa3a7)
05-24 15:40:06.424  1824  1824 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0xc18a96ed)
05-24 15:40:06.438  1824  1824 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:40:06.439  1824  1824 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x91778e49)
05-24 15:40:06.440  1824  1824 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x6b40b69b)
05-24 15:40:06.440  1824  1830 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-24 15:40:06.441  1824  1830 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-24 15:40:06.442  1824  1824 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-24 15:40:06.442   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:40:06.443  1824  1824 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-24 15:40:06.443   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:40:06.444  1824  1830 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:40:06.444  1824  1824 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-24 15:40:06.444   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:40:06.444  1824  1824 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-24 15:40:06.444  1824  1824 W audiohalservice: Could not register Bluetooth Audio API
05-24 15:40:06.445   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-24 15:40:06.445  1824  1824 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-24 15:40:06.445  1824  1824 W audiohalservice: Could not register Bluetooth Audio Offload API
05-24 15:40:06.451  1824  1824 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-24 15:40:06.452  1824  1824 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-24 15:40:06.452  1824  1824 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-24 15:40:06.525  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.525  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.525  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.526  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.526  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.526  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.526  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.526  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.526  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.526  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.526  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.527  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.527  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.527  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.527  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.527  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.527  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.527  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.578  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.578  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:06.581  1824  1830 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 15:40:06.601  1824  1830 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xa2fa9925)
05-24 15:40:06.602  1824  1841 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-24 15:40:06.604  1824  1841 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0xa2fa9925)
05-24 15:40:06.606  1824  1841 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0xa2fa9925)
05-24 15:40:06.667  1824  1830 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-24 15:40:06.673  1824  1830 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 15:40:06.674  1824  1830 W AudioSmartPaController: init(), dlopen failed, dlerror = dlopen failed: library "libfile_op.so" not found: needed by /vendor/lib/libnxp_extamp_intf.so in namespace (default)
05-24 15:40:06.694  1824  1830 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-24 15:40:06.707  1824  1830 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 15:40:06.708  1824  1830 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 15:40:06.708  1824  1830 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 15:40:06.712  1824  1830 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 15:40:06.712  1824  1830 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 15:40:06.713  1824  1830 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-24 15:40:06.717  1824  1830 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-24 15:40:06.717  1824  1830 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-24 15:40:06.718  1831  1831 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 15:40:06.718  1831  1831 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 15:40:06.718  1824  1824 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-24 15:40:06.718  1824  1824 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-24 15:40:06.719  1824  1824 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 15:40:06.719  1831  1831 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-24 15:40:06.719  1831  1831 I AudioFlinger: openOutput() this 0xb4000077673711f0, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-24 15:40:06.720  1831  1831 I AudioFlinger: operator(): Using 3000 ms as standby time
05-24 15:40:06.721  1831  1831 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 15:40:06.721  1831  1831 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:40:06.721   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:40:06.722  1831  1831 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:40:06.727  1824  1824 E EffectsFactoryConfigLoader: loadLibrary Could not find library in effect directories: libaudiopreprocessing_mtk.so
05-24 15:40:06.728  1824  1824 E EffectsFactoryConfigLoader: EffectLoadXmlEffectConfig 4 errors during loading of configuration: /vendor/etc/audio_effects.xml
05-24 15:40:06.728  1831  1854 I AudioFlinger: AudioFlinger's thread 0xb4000079ac18e760 tid=1854 ready to run
05-24 15:40:06.762  1824  1843 W audio_messenger_ipi: read_ipi_thread(), read dsp fail!! length_read: 0, errno: 0
05-24 15:40:06.767  1857  1857 E DEBUG   : failed to read process info: failed to open /proc/1824: No such file or directory
05-24 15:40:06.824  1857  1857 F DEBUG   : Cmdline: /vendor/bin/hw/android.hardware.audio.service
05-24 15:40:06.825  1857  1857 F DEBUG   : pid: 1824, tid: 1824, name: binder:1824_2  >>> /vendor/bin/hw/android.hardware.audio.service <<<
05-24 15:40:06.825  1857  1857 F DEBUG   :       #01 pc 0002651d  /vendor/lib/hw/<EMAIL> (android::hardware::audio::V7_1::implementation::StreamOut::setLatencyModeCallback(android::sp<android::hardware::audio::V7_1::IStreamOutLatencyModeCallback> const&)+36) (BuildId: 544a581a8a5464266e14c74f5aea9a1c)
05-24 15:40:06.825  1857  1857 F DEBUG   :       #02 pc 00043c0f  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::_hidl_setLatencyModeCallback(android::hidl::base::V1_0::BnHwBase*, android::hardware::Parcel const&, android::hardware::Parcel*, std::__1::function<void (android::hardware::Parcel&)>)+198) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 15:40:06.825  1857  1857 F DEBUG   :       #03 pc 00044703  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::onTransact(unsigned int, android::hardware::Parcel const&, android::hardware::Parcel*, unsigned int, std::__1::function<void (android::hardware::Parcel&)>)+1926) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 15:40:06.825  1857  1857 F DEBUG   :       #06 pc 00002d23  /vendor/bin/hw/android.hardware.audio.service (main+3150) (BuildId: 4f8cce90cd7ff9b304f84e6fd8ec6b0b)
05-24 15:40:06.825  1857  1857 F DEBUG   :       #07 pc 00036457  /apex/com.android.runtime/lib/bionic/libc.so (__libc_init+74) (BuildId: a6009addd32ae1399831564e0073b350)
05-24 15:40:06.850  1831  1835 E HalDeathHandler: HAL server crashed, audio server is restarting
05-24 15:40:08.173  1307  1338 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 15:40:08.798   577   577 I libc    : Requested dump for pid 577 (servicemanager)
05-24 15:40:10.140   635   635 I libc    : Requested dump for pid 635 (suspend-service)
05-24 15:40:10.193  1989  1989 F crash_dump64: crash_dump.cpp:625] failed to attach to thread 636: Permission denied
05-24 15:40:10.860  1307  1307 E AudioSystem-JNI: Command failed for android_media_AudioSystem_getDevicesForAttributes: -32
05-24 15:40:10.861  1307  1307 E AudioSystem: error 100 in getDevicesForAttributes attributes: AudioAttributes: usage=USAGE_UNKNOWN content=CONTENT_TYPE_UNKNOWN flags=0x800 tags= bundle=null forVolume: false
05-24 15:40:10.861  1307  1307 W AS.AudioDeviceBroker: updateActiveCommunicationDevice(): no device for phone strategy
05-24 15:40:10.866  1307  1307 I AS.AudioService: Stream 5: using max vol of 7
05-24 15:40:10.866  1307  1307 I AS.AudioService: Stream 5: using default vol of 5
05-24 15:40:10.866  1307  1307 I AS.AudioService: Stream 2: using max vol of 7
05-24 15:40:10.866  1307  1307 I AS.AudioService: Stream 2: using default vol of 5
05-24 15:40:11.211  1992  1992 I HidlServiceManagement: Registered android.hardware.audio@7.1::IDevicesFactory/default
05-24 15:40:11.211  1992  1992 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-24 15:40:11.212  1992  1992 I LegacySupport: Registration complete for android.hardware.audio@7.1::IDevicesFactory/default.
05-24 15:40:11.217  1992  1992 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-24 15:40:11.217  1992  1992 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-24 15:40:11.227  1992  1992 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:40:11.297  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.297  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.297  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.297  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.297  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.297  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.297  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.297  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.297  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.297  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.297  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.297  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.298  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.298  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.298  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.298  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.298  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.298  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.322  1999  1999 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:40:11.324  1999  1999 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:40:11.324   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:40:11.334  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.334  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.337  1999  1999 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:40:11.337   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:40:11.337  1992  1992 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 15:40:11.338  1999  1999 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:40:11.344  1999  1999 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-24 15:40:11.353  1999  1999 E libxml2 : I/O warning : failed to load "/odm/etc/virtual_audio_policy_configuration.xml": No such file or directory
05-24 15:40:11.354  1999  1999 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-24 15:40:11.354  1999  1999 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-24 15:40:11.354  1992  1992 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xf2de94ef)
05-24 15:40:11.358  1992  1992 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x50b1b187)
05-24 15:40:11.365  1992  1992 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:40:11.366  1992  1998 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-24 15:40:11.367  1992  1998 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-24 15:40:11.367  1992  1992 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xb69fd53b)
05-24 15:40:11.368  1992  1992 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x2a594481)
05-24 15:40:11.369  1992  1998 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:40:11.371  1992  1992 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-24 15:40:11.371   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:40:11.372  1992  1992 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-24 15:40:11.372   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:40:11.372  1992  1992 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-24 15:40:11.372   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:40:11.373  1992  1992 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-24 15:40:11.373  1992  1992 W audiohalservice: Could not register Bluetooth Audio API
05-24 15:40:11.373   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-24 15:40:11.373  1992  1992 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-24 15:40:11.373  1992  1992 W audiohalservice: Could not register Bluetooth Audio Offload API
05-24 15:40:11.379  1992  1992 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-24 15:40:11.379  1992  1992 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-24 15:40:11.379  1992  1992 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-24 15:40:11.430  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.430  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.430  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.430  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.430  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.430  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.431  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.431  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.431  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.431  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.431  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.431  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.431  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.431  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.431  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.432  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.432  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.432  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.484  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.484  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:11.488  1992  1998 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 15:40:11.509  1992  1998 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x5c57f041)
05-24 15:40:11.510  1992  2009 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-24 15:40:11.511  1992  2009 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0x5c57f041)
05-24 15:40:11.513  1992  2009 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0x5c57f041)
05-24 15:40:11.578  1992  1998 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-24 15:40:11.583  1992  1998 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 15:40:11.584  1992  1998 W AudioSmartPaController: init(), dlopen failed, dlerror = dlopen failed: library "libfile_op.so" not found: needed by /vendor/lib/libnxp_extamp_intf.so in namespace (default)
05-24 15:40:11.609  1992  1998 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-24 15:40:11.621  1992  1998 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 15:40:11.621  1992  1998 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 15:40:11.621  1992  1998 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 15:40:11.626  1992  1998 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 15:40:11.626  1992  1998 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 15:40:11.627  1992  1998 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-24 15:40:11.630  1992  1998 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-24 15:40:11.630  1992  1998 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-24 15:40:11.631  1999  1999 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 15:40:11.631  1999  1999 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 15:40:11.632  1992  1992 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-24 15:40:11.632  1992  1992 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-24 15:40:11.633  1992  1992 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 15:40:11.633  1999  1999 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-24 15:40:11.634  1999  1999 I AudioFlinger: openOutput() this 0xb4000073e76db070, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-24 15:40:11.635  1999  1999 I AudioFlinger: operator(): Using 3000 ms as standby time
05-24 15:40:11.635  1999  1999 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 15:40:11.636  1999  1999 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:40:11.637   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:40:11.638  1999  1999 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:40:11.642  1992  1992 E EffectsFactoryConfigLoader: loadLibrary Could not find library in effect directories: libaudiopreprocessing_mtk.so
05-24 15:40:11.642  1992  1992 E EffectsFactoryConfigLoader: EffectLoadXmlEffectConfig 4 errors during loading of configuration: /vendor/etc/audio_effects.xml
05-24 15:40:11.643  1999  2022 I AudioFlinger: AudioFlinger's thread 0xb40000752f9b0760 tid=2022 ready to run
05-24 15:40:11.669  1992  2011 W audio_messenger_ipi: read_ipi_thread(), read dsp fail!! length_read: 0, errno: 0
05-24 15:40:11.673  2028  2028 E DEBUG   : failed to read process info: failed to open /proc/1992: No such file or directory
05-24 15:40:11.724  2028  2028 F DEBUG   : Cmdline: /vendor/bin/hw/android.hardware.audio.service
05-24 15:40:11.724  2028  2028 F DEBUG   : pid: 1992, tid: 1997, name: HwBinder:1992_1  >>> /vendor/bin/hw/android.hardware.audio.service <<<
05-24 15:40:11.724  2028  2028 F DEBUG   :       #01 pc 0002651d  /vendor/lib/hw/<EMAIL> (android::hardware::audio::V7_1::implementation::StreamOut::setLatencyModeCallback(android::sp<android::hardware::audio::V7_1::IStreamOutLatencyModeCallback> const&)+36) (BuildId: 544a581a8a5464266e14c74f5aea9a1c)
05-24 15:40:11.724  2028  2028 F DEBUG   :       #02 pc 00043c0f  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::_hidl_setLatencyModeCallback(android::hidl::base::V1_0::BnHwBase*, android::hardware::Parcel const&, android::hardware::Parcel*, std::__1::function<void (android::hardware::Parcel&)>)+198) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 15:40:11.724  2028  2028 F DEBUG   :       #03 pc 00044703  /vendor/lib/<EMAIL> (android::hardware::audio::V7_1::BnHwStreamOut::onTransact(unsigned int, android::hardware::Parcel const&, android::hardware::Parcel*, unsigned int, std::__1::function<void (android::hardware::Parcel&)>)+1926) (BuildId: 79b6a1b60ff2714c68b122c672363f3b)
05-24 15:40:15.869  1307  1307 E AudioSystem-JNI: Command failed for android_media_AudioSystem_checkAudioFlinger: -32
05-24 15:40:15.869  1307  1991 E AS.AudioService: Audioserver died.
05-24 15:40:15.874  1307  1307 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 15:40:15.901  1307  1307 W AS.AudioService: Could not retrieve audio HAL service pids
05-24 15:40:16.216  2033  2033 I HidlServiceManagement: Registered android.hardware.audio@7.1::IDevicesFactory/default
05-24 15:40:16.217  2033  2033 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-24 15:40:16.217  2033  2033 I LegacySupport: Registration complete for android.hardware.audio@7.1::IDevicesFactory/default.
05-24 15:40:16.223  2033  2033 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-24 15:40:16.223  2033  2033 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-24 15:40:16.230  2033  2033 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:40:16.280  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.280  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.280  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.280  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.280  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.280  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.280  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.280  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.280  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.280  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.281  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.281  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.281  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.281  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.281  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.281  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.281  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.281  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.317  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.317  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.320  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 15:40:16.337  2033  2033 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xdf4a949b)
05-24 15:40:16.341  2033  2033 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x35aab0a7)
05-24 15:40:16.343  2033  2033 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:40:16.343  2033  2033 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x883ed283)
05-24 15:40:16.345  2033  2033 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x59423079)
05-24 15:40:16.347  2033  2033 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-24 15:40:16.348   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:40:16.349  2033  2033 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-24 15:40:16.349   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:40:16.350  2033  2033 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-24 15:40:16.350   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-24 15:40:16.350  2033  2033 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-24 15:40:16.350  2033  2033 W audiohalservice: Could not register Bluetooth Audio API
05-24 15:40:16.351   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-24 15:40:16.351  2033  2033 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-24 15:40:16.351  2033  2033 W audiohalservice: Could not register Bluetooth Audio Offload API
05-24 15:40:16.357  2033  2033 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-24 15:40:16.358  2033  2033 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-24 15:40:16.358  2033  2033 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-24 15:40:16.371  2040  2040 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:40:16.372  2040  2040 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:40:16.373   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:40:16.385  2040  2040 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:40:16.385   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:40:16.386  2040  2040 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:40:16.391  2040  2040 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-24 15:40:16.401  2040  2040 E libxml2 : I/O warning : failed to load "/odm/etc/virtual_audio_policy_configuration.xml": No such file or directory
05-24 15:40:16.401  2040  2040 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-24 15:40:16.401  2040  2040 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-24 15:40:16.409  2033  2033 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-24 15:40:16.410  2033  2033 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-24 15:40:16.411  2033  2033 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-24 15:40:16.468  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.468  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.469  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.469  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.469  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.469  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.469  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.469  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.469  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.469  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.469  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.470  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.470  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.470  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.470  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.470  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.470  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.470  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.515  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.515  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-24 15:40:16.518  2033  2033 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-24 15:40:16.536  2033  2033 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x1fb64717)
05-24 15:40:16.536  2033  2050 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-24 15:40:16.537  2033  2050 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0x1fb64717)
05-24 15:40:16.538  2033  2050 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0x1fb64717)
05-24 15:40:16.602  2033  2033 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-24 15:40:16.607  2033  2033 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 15:40:16.608  2033  2033 W AudioSmartPaController: init(), dlopen failed, dlerror = dlopen failed: library "libfile_op.so" not found: needed by /vendor/lib/libnxp_extamp_intf.so in namespace (default)
05-24 15:40:16.632  2033  2033 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-24 15:40:16.646  2033  2033 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-24 15:40:16.646  2033  2033 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 15:40:16.646  2033  2033 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 15:40:16.651  2033  2033 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-24 15:40:16.651  2033  2033 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-24 15:40:16.652  2033  2033 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-24 15:40:16.656  2033  2033 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-24 15:40:16.657  2033  2033 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-24 15:40:16.658  2040  2040 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 15:40:16.658  2040  2040 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 15:40:16.658  2033  2033 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-24 15:40:16.658  2033  2033 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-24 15:40:16.659  2033  2033 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 15:40:16.659  2040  2040 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-24 15:40:16.659  2040  2040 I AudioFlinger: openOutput() this 0xb40000789425a900, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-24 15:40:16.660  2040  2040 I AudioFlinger: operator(): Using 3000 ms as standby time
05-24 15:40:16.660  2040  2040 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 15:40:16.661  2040  2040 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:40:16.661   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:40:16.662  2040  2040 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:40:16.666  2033  2033 E EffectsFactoryConfigLoader: loadLibrary Could not find library in effect directories: libaudiopreprocessing_mtk.so
05-24 15:40:16.666  2033  2033 E EffectsFactoryConfigLoader: EffectLoadXmlEffectConfig 4 errors during loading of configuration: /vendor/etc/audio_effects.xml
05-24 15:40:16.666  2040  2063 I AudioFlinger: AudioFlinger's thread 0xb400007ab45e4760 tid=2063 ready to run
05-24 15:40:16.667  2040  2063 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 15:40:16.667  2040  2040 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 15:40:16.669  2040  2063 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.669  2040  2040 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 15:40:16.669  2040  2040 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 15:40:16.669  2040  2040 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 15:40:16.669  2040  2040 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 15:40:16.669  2040  2040 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 15:40:16.669  2040  2040 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 15:40:16.669  2040  2040 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 15:40:16.669  2040  2040 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 15:40:16.669  2040  2040 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 15:40:16.669  2040  2040 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 15:40:16.669  2040  2040 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 15:40:16.669  2040  2040 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 15:40:16.669  2040  2040 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-24 15:40:16.669  2040  2040 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-24 15:40:16.669  2040  2040 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-24 15:40:16.669  2040  2040 W AudioFlinger: moveEffects() AUDIO_SESSION_OUTPUT_MIX not found in orphans, checking other mix
05-24 15:40:16.669  2040  2040 W AudioFlinger: moveEffects() bad srcIo 0
05-24 15:40:16.670  2040  2040 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 15:40:16.691  2033  2039 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x2, output_devices == current_output_devices(0x00000002), return
05-24 15:40:16.693  2040  2040 I AudioFlinger: openOutput() this 0xb40000789425a900, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x8
05-24 15:40:16.693  2033  2039 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 15:40:16.694  2040  2040 I AudioFlinger: HAL output buffer size 2048 frames, normal sink buffer size 2048 frames
05-24 15:40:16.695  2040  2064 I AudioFlinger: AudioFlinger's thread 0xb400007ab4576760 tid=2064 ready to run
05-24 15:40:16.695  2040  2064 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.696  2040  2064 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.698  2040  2040 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 15:40:16.763  2033  2039 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x8, output_devices == current_output_devices(0x00000002), return
05-24 15:40:16.766  2040  2040 I AudioFlinger: openOutput() this 0xb40000789425a900, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x4
05-24 15:40:16.766  2033  2039 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 15:40:16.767  2040  2040 I AudioFlinger: HAL output buffer size 256 frames, normal sink buffer size 768 frames
05-24 15:40:16.769  2040  2066 I AudioFlinger: AudioFlinger's thread 0xb400007ab450f760 tid=2066 ready to run
05-24 15:40:16.769  2040  2066 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.770  2040  2066 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.771  2040  2040 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 15:40:16.793  2033  2039 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x4, output_devices == current_output_devices(0x00000002), return
05-24 15:40:16.795  2040  2040 I AudioFlinger: openOutput() this 0xb40000789425a900, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x8000
05-24 15:40:16.795  2033  2039 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 15:40:16.797  2040  2040 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 15:40:16.798  2040  2067 I AudioFlinger: AudioFlinger's thread 0xb400007ab33d4760 tid=2067 ready to run
05-24 15:40:16.799  2040  2067 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.799  2040  2067 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.803  2040  2040 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-24 15:40:16.835  2033  2039 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x8000, output_devices == current_output_devices(0x00000002), return
05-24 15:40:16.839  2040  2040 I AudioFlinger: openOutput() this 0xb40000789425a900, module 10 Device AUDIO_DEVICE_OUT_TELEPHONY_TX, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x10000
05-24 15:40:16.840  2033  2039 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-24 15:40:16.842  2040  2040 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-24 15:40:16.843  2040  2068 I AudioFlinger: AudioFlinger's thread 0xb400007ab30ed760 tid=2068 ready to run
05-24 15:40:16.843  2040  2068 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.849  2040  2068 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.890  2040  2069 I AudioFlinger: AudioFlinger's thread 0xb400007ab30a4a78 tid=2069 ready to run
05-24 15:40:16.890  2040  2069 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.893  2040  2069 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.898  2040  2071 I AudioFlinger: AudioFlinger's thread 0xb400007ab30a4a78 tid=2071 ready to run
05-24 15:40:16.899  2040  2071 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.900  2040  2071 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.902  2040  2073 I AudioFlinger: AudioFlinger's thread 0xb400007ab30a4a78 tid=2073 ready to run
05-24 15:40:16.903  2040  2073 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.903  2040  2073 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.906  2040  2075 I AudioFlinger: AudioFlinger's thread 0xb400007ab30a4a78 tid=2075 ready to run
05-24 15:40:16.907  2040  2075 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.907  2040  2075 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.910  2040  2078 I AudioFlinger: AudioFlinger's thread 0xb400007ab30a4a78 tid=2078 ready to run
05-24 15:40:16.911  2040  2078 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.911  2040  2078 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.912  2033  2033 E DevicesFactoryHAL: loadAudioInterface couldn't load audio hw module audio.binaural_record (No such file or directory)
05-24 15:40:16.912  2040  2040 E AudioFlinger: loadHwModule() error -22 loading module binaural_record
05-24 15:40:16.912  2040  2040 W APM_AudioPolicyManager: could not load HW module binaural_record
05-24 15:40:16.918  2040  2040 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 15:40:16.918  2040  2040 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 15:40:16.918  2040  2040 I AudioFlinger: loadHwModule() Loaded bluetooth audio interface, handle 18
05-24 15:40:16.919  2033  2033 E DevicesFactoryHAL: loadAudioInterface couldn't load audio hw module audio.a2dp (No such file or directory)
05-24 15:40:16.919  2040  2040 E AudioFlinger: loadHwModule() error -22 loading module a2dp
05-24 15:40:16.919  2040  2040 W APM_AudioPolicyManager: could not load HW module a2dp
05-24 15:40:16.919  2033  2033 E DevicesFactoryHAL: loadAudioInterface couldn't load audio hw module audio.usb (No such file or directory)
05-24 15:40:16.919  2040  2040 E AudioFlinger: loadHwModule() error -22 loading module usb
05-24 15:40:16.919  2040  2040 W APM_AudioPolicyManager: could not load HW module usb
05-24 15:40:16.921  2033  2033 I r_submix: adev_open(name=audio_hw_if)
05-24 15:40:16.922  2040  2040 W DeviceHalHidl: getSoundDoseInterface service android.hardware.audio.sounddose.ISoundDoseFactory/default doesn't exist
05-24 15:40:16.922  2040  2040 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-24 15:40:16.922  2033  2033 I r_submix: adev_init_check()
05-24 15:40:16.922  2040  2040 I AudioFlinger: loadHwModule() Loaded r_submix audio interface, handle 26
05-24 15:40:16.925  2040  2080 I AudioFlinger: AudioFlinger's thread 0xb400007ab30a4a78 tid=2080 ready to run
05-24 15:40:16.925  2040  2080 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.926  2040  2080 W AudioFlinger: no wake lock to update, system not ready yet
05-24 15:40:16.928  2040  2040 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-24 15:40:16.929   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-24 15:40:16.931  2040  2040 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-24 15:40:16.944  2040  2040 I audioserver: main: initialization done in 575.490 ms, joining thread pool
05-24 15:40:16.945  1024  1868 W AudioAnalytics: onAudioServerStart: (key=audio.policy) AudioPolicy ctor, loadTimeMs:551.473206
05-24 15:40:16.950  1307  1991 E AS.AudioService: Audioserver died.
05-24 15:40:16.958  1307  1307 I SystemServiceManager: Starting com.android.server.soundtrigger_middleware.SoundTriggerMiddlewareService$Lifecycle
05-24 15:40:16.959  1307  1991 I AS.AudioService: updateIndexFactors() stream:0 index min/max:1/15 indexStepFactor:2.3333333
05-24 15:40:16.960  1307  1991 I AS.AudioService: updateIndexFactors() stream:1 index min/max:0/7 indexStepFactor:1.0
05-24 15:40:16.960  1307  1991 I AS.AudioService: updateIndexFactors() stream:2 index min/max:0/7 indexStepFactor:1.0
05-24 15:40:16.961  1307  1991 I AS.AudioService: updateIndexFactors() stream:3 index min/max:0/15 indexStepFactor:1.0
05-24 15:40:16.961  1307  1991 I AS.AudioService: updateIndexFactors() stream:4 index min/max:1/7 indexStepFactor:1.0
05-24 15:40:16.962  1307  1991 I AS.AudioService: updateIndexFactors() stream:5 index min/max:0/7 indexStepFactor:1.0
05-24 15:40:16.962  1307  1991 I AS.AudioService: updateIndexFactors() stream:7 index min/max:0/7 indexStepFactor:1.0
05-24 15:40:16.962  1307  1991 I AS.AudioService: updateIndexFactors() stream:8 index min/max:0/15 indexStepFactor:1.0
05-24 15:40:16.963  1307  1991 I AS.AudioService: updateIndexFactors() stream:9 index min/max:0/15 indexStepFactor:1.0
05-24 15:40:16.963  1307  1991 I AS.AudioService: updateIndexFactors() stream:10 index min/max:1/15 indexStepFactor:1.0
05-24 15:40:16.964  1307  1991 I AS.AudioService: updateIndexFactors() stream:11 index min/max:0/15 indexStepFactor:1.0
05-24 15:40:16.973  1307  1307 I SystemServiceManager: Starting com.android.server.DockObserver
05-24 15:40:16.975  1307  1307 W WiredAccessoryManager: This kernel does not have usb audio support
05-24 15:40:16.975  1307  1307 W WiredAccessoryManager: This kernel does not have HDMI audio support
05-24 15:40:16.975  1307  1307 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/3/0 does not have DP audio support
05-24 15:40:16.975  1307  1307 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/2/0 does not have DP audio support
05-24 15:40:16.975  1307  1307 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/1/0 does not have DP audio support
05-24 15:40:16.975  1307  1307 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/0/0 does not have DP audio support
05-24 15:40:16.976  1307  1307 I SystemServiceManager: Starting com.android.server.midi.MidiService$Lifecycle
05-24 15:40:16.977  1307  1307 I SystemServiceManager: Starting com.android.server.adb.AdbService$Lifecycle
05-24 15:40:16.978  1307  1991 W BroadcastLoopers: Found previously unknown looper Thread[AudioService,5,main]
05-24 15:40:16.978  1307  1307 I SystemServiceManager: Starting com.android.server.usb.UsbService$Lifecycle
05-24 15:40:16.978  1307  1307 I SystemServiceManager: Starting com.android.server.SerialService$Lifecycle
05-24 15:40:16.979  1307  1307 I HardwarePropertiesManagerService-JNI: Thermal AIDL service is not declared, trying HIDL
05-24 15:40:16.982  1307  1307 I SystemServiceManager: Starting com.android.server.twilight.TwilightService
05-24 15:40:16.982  1307  1307 I SystemServiceManager: Starting com.android.server.display.color.ColorDisplayService
05-24 15:40:16.984  1307  1307 I SystemServiceManager: Starting com.android.server.job.JobSchedulerService
05-24 15:40:16.984  1307  1591 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 15:40:16.985  1307  1689 I UsbDeviceManager: Usb gadget hal service started android.hardware.usb.gadget@1.0::IUsbGadget default
05-24 15:40:16.991  1307  1591 W StorageManagerService: No primary storage defined yet; hacking together a stub
05-24 15:40:16.992  1307  1334 W JobInfo : Job 'com.google.android.setupwizard/.deviceorigin.provider.DeviceOriginWipeOutJobService#8580' has a deadline with no functional constraints. The deadline won't improve job execution latency. Consider removing the deadline.
05-24 15:40:16.996  1307  1307 I SystemServiceManager: Starting com.android.server.soundtrigger.SoundTriggerService
05-24 15:40:16.997  1307  1307 I SystemServiceManager: Starting com.android.server.trust.TrustManagerService
05-24 15:40:16.998  1307  1307 I SystemServiceManager: Starting com.android.server.backup.BackupManagerService$Lifecycle
05-24 15:40:16.999  1307  1307 I SystemServiceManager: Starting com.android.server.appwidget.AppWidgetService
05-24 15:40:17.000  1307  1591 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 15:40:17.000  2040  2042 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_HEARING_AID, connection: wireless}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 15:40:17.000  2040  2042 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x8000000, enabled 1, streamToDriveAbs 3
05-24 15:40:17.000  2040  2042 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_HEADSET, connection: bt-le}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 15:40:17.000  2040  2042 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x20000000, enabled 1, streamToDriveAbs 3
05-24 15:40:17.001  2040  2042 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_SPEAKER, connection: bt-le}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 15:40:17.001  2040  2042 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x20000001, enabled 1, streamToDriveAbs 3
05-24 15:40:17.001  2040  2042 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_BROADCAST, connection: bt-le}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 15:40:17.001  2040  2042 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x20000002, enabled 1, streamToDriveAbs 3
05-24 15:40:17.001  1307  1689 I UsbPortManager: Usb hal service started android.hardware.usb@1.0::IUsb default
05-24 15:40:17.001  1307  1991 E BluetoothAdapter: Bluetooth service is null
05-24 15:40:17.002  1307  1307 I SystemServiceManager: Starting com.android.server.voiceinteraction.VoiceInteractionManagerService
05-24 15:40:17.002  1307  1991 E BluetoothAdapter: Bluetooth service is null
05-24 15:40:17.003  1307  1991 E BluetoothAdapter: Bluetooth service is null
05-24 15:40:17.003  1307  1991 E BluetoothAdapter: Bluetooth service is null
05-24 15:40:17.003  1307  1991 I AS.SpatializerHelper: init effectExpected=false
05-24 15:40:17.003  1307  1991 I AS.SpatializerHelper: init(): setting state to STATE_NOT_SUPPORTED due to effect not expected
05-24 15:40:17.003   853   853 I android.hardware.usb@1.3-service-mediatekv2: Registering 1.2 callback
05-24 15:40:17.003   853   853 I android.hardware.usb@1.3-service-mediatekv2: registering callback
05-24 15:40:17.005   853   853 I android.hardware.usb@1.3-service-mediatekv2: port0
05-24 15:40:17.005   853   853 I android.hardware.usb@1.3-service-mediatekv2: HAL version V1_2
05-24 15:40:17.005   853   853 I android.hardware.usb@1.3-service-mediatekv2: 0:port0 connected:1 canChangeMode:1 canChagedata:0 canChangePower:0 supportedModes:3
05-24 15:40:17.005   853   853 I android.hardware.usb@1.3-service-mediatekv2: ContaminantDetectionStatus:2 ContaminantProtectionStatus:0
05-24 15:40:17.006   853  2101 E android.hardware.usb@1.3-service-mediatekv2: creating thread
05-24 15:40:17.007  1307  1307 I SystemServiceManager: Starting com.android.server.GestureLauncherService
05-24 15:40:17.007  1307  1307 I SystemServiceManager: Starting com.android.server.SensorNotificationService
05-24 15:40:17.017  1307  1334 W JobInfo : Job 'android/com.android.server.usage.UsageStatsIdleService#0' has a deadline with no functional constraints. The deadline won't improve job execution latency. Consider removing the deadline.
05-24 15:40:17.074  1307  1307 I SystemServiceManager: Starting com.android.server.emergency.EmergencyAffordanceService
05-24 15:40:17.075  1307  1307 I SystemServiceManager: Starting com.android.server.blob.BlobStoreManagerService
05-24 15:40:17.077  1307  1307 I SystemServiceManager: Starting com.android.server.dreams.DreamManagerService
05-24 15:40:17.080  1307  1307 I SystemServiceManager: Starting com.android.server.print.PrintManagerService
05-24 15:40:17.081   826  1969 I ServiceManagerCppClient: Waiting for service 'statscompanion' on '/dev/binder'...
05-24 15:40:17.082  1307  1307 I SystemServiceManager: Starting com.android.server.security.AttestationVerificationManagerService
05-24 15:40:17.084  1307  1307 I SystemServiceManager: Starting com.android.server.companion.CompanionDeviceManagerService
05-24 15:40:17.092  1307  1307 I SystemServiceManager: Starting com.android.server.companion.virtual.VirtualDeviceManagerService
05-24 15:40:17.097  1307  1307 I SystemServiceManager: Starting com.android.server.restrictions.RestrictionsManagerService
05-24 15:40:17.099  1307  1307 I SystemServiceManager: Starting com.android.server.media.MediaSessionService
05-24 15:40:17.104  1307  1307 I SystemServiceManager: Starting com.android.server.media.MediaResourceMonitorService
05-24 15:40:17.110  1307  1307 I SystemServiceManager: Starting com.android.server.biometrics.sensors.face.FaceService
05-24 15:40:17.112  1307  1307 I SystemServiceManager: Starting com.android.server.biometrics.sensors.fingerprint.FingerprintService
05-24 15:40:17.117  1307  1307 I SystemServiceManager: Starting com.android.server.biometrics.BiometricService
05-24 15:40:17.123  1307  1307 I CameraManagerGlobal: Connecting to camera service
05-24 15:40:17.134  1307  1307 I SystemServiceManager: Starting com.android.server.biometrics.AuthService
05-24 15:40:17.136  1307  1307 I FingerprintService: Before:getDeclaredInstances: IFingerprint instance found, a.length=0
05-24 15:40:17.136  1307  1307 I FingerprintService: After:getDeclaredInstances: a.length=1
05-24 15:40:17.138  1307  1307 I FaceService: Before:getDeclaredInstances: IFace instance found, a.length=0
05-24 15:40:17.138  1307  1307 I FaceService: After:getDeclaredInstances: a.length=1
05-24 15:40:17.139  1307  1307 E AuthService: Unknown modality: 2
05-24 15:40:17.142  1307  1307 I SystemServiceManager: Starting com.android.server.security.authenticationpolicy.AuthenticationPolicyService
05-24 15:40:17.144  1307  1307 I SystemServiceManager: Starting com.android.server.app.AppLockManagerService$Lifecycle
05-24 15:40:17.147  1307  1307 I SystemServiceManager: Starting com.android.server.display.FreeformService
05-24 15:40:17.170  1307  1307 I SystemServiceManager: Starting com.android.server.pm.ShortcutService$Lifecycle
05-24 15:40:17.174  1307  1307 I SystemServiceManager: Starting com.android.server.pm.LauncherAppsService
05-24 15:40:17.179  1307  1307 I SystemServiceManager: Starting com.android.server.pm.CrossProfileAppsService
05-24 15:40:17.180  1307  1307 I SystemServiceManager: Starting com.android.server.pocket.PocketService
05-24 15:40:17.189  1307  1307 I SystemServiceManager: Starting com.android.server.people.PeopleService
05-24 15:40:17.190  1307  1307 I SystemServiceManager: Starting com.android.server.media.metrics.MediaMetricsManagerService
05-24 15:40:17.192  1307  1307 I SystemServiceManager: Starting com.android.server.pm.BackgroundInstallControlService
05-24 15:40:17.195  1307  1307 I SystemServiceManager: Starting com.android.server.voltage.CustomDeviceConfigService
05-24 15:40:17.195  1307  1307 I SystemServiceManager: Starting com.android.server.custom.LineageHardwareService
05-24 15:40:17.197  1307  1307 I SystemServiceManager: Starting com.android.server.custom.display.LiveDisplayService
05-24 15:40:17.198  1307  1307 I SystemServiceManager: Starting com.android.server.custom.health.HealthInterfaceService
05-24 15:40:17.200  1307  1307 I SystemServiceManager: Starting com.android.server.HideAppListService
05-24 15:40:17.200  1307  1307 I HideAppListService: Starting HideAppListService
05-24 15:40:17.200  1307  1307 I SystemServiceManager: Starting com.android.server.GameSpaceManagerService
05-24 15:40:17.200  1307  1307 I SystemServiceManager: Starting com.android.server.media.projection.MediaProjectionManagerService
05-24 15:40:17.205  1307  1307 I SystemServiceManager: Starting com.android.server.slice.SliceManagerService$Lifecycle
05-24 15:40:17.207  1307  1307 I SystemServiceManager: Starting com.android.server.stats.StatsCompanion$Lifecycle
05-24 15:40:17.209  1307  1307 I SystemServiceManager: Starting com.android.server.scheduling.RebootReadinessManagerService$Lifecycle
05-24 15:40:17.212  1307  1307 I SystemServiceManager: Starting com.android.server.stats.pull.StatsPullAtomService
05-24 15:40:17.212  1307  1307 I SystemServiceManager: Starting com.android.server.stats.bootstrap.StatsBootstrapAtomService$Lifecycle
05-24 15:40:17.213  1307  1307 I SystemServiceManager: Starting com.android.server.incident.IncidentCompanionService
05-24 15:40:17.213  1307  1307 I SystemServiceManager: Starting com.android.server.sdksandbox.SdkSandboxManagerService$Lifecycle
05-24 15:40:17.219  1307  1307 I SystemServiceManager: Starting com.android.server.adservices.AdServicesManagerService$Lifecycle
05-24 15:40:17.221  1307  1307 I SystemServiceManager: Starting com.android.server.ondevicepersonalization.OnDevicePersonalizationSystemService$Lifecycle
05-24 15:40:17.222  1307  1307 I ondevicepersonalization: OnDevicePersonalizationSystemService started!
05-24 15:40:17.222  1307  1307 I SystemServiceManager: Starting android.os.profiling.ProfilingService$Lifecycle
05-24 15:40:17.226  1307  1307 I SystemServiceManager: Starting com.android.server.MmsServiceBroker
05-24 15:40:17.227  1307  1307 I SystemServiceManager: Starting com.android.server.autofill.AutofillManagerService
05-24 15:40:17.230  1307  1307 E AutofillManagerServiceImpl: Bad service name: proton.android.pass/proton.android.pass.autofill.ProtonPassAutofillService
05-24 15:40:17.232  1307  1307 I SystemServiceManager: Starting com.android.server.credentials.CredentialManagerService
05-24 15:40:17.233  1307  1307 I SystemServiceManager: Starting com.android.server.clipboard.ClipboardService
05-24 15:40:17.236  1307  1307 I SystemServiceManager: Starting com.android.server.appbinding.AppBindingService$Lifecycle
05-24 15:40:17.236  1307  1307 I SystemServiceManager: Starting com.android.server.tracing.TracingServiceProxy
05-24 15:40:17.241   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.authsecret@1.0::IAuthSecret/default in either framework or device VINTF manifest.
05-24 15:40:17.241  1307  1307 I LockSettingsService: Device doesn't implement AuthSecret HAL
05-24 15:40:17.243  1307  1307 I SystemServiceManager: Starting phase 480
05-24 15:40:17.256  1307  1307 W PocketService: Un-handled boot phase:480
05-24 15:40:17.256  1307  1307 I SystemServiceManager: Starting phase 500
05-24 15:40:17.257  1307  1307 E StatsPullAtomCallbackImpl: Failed to start PowerStatsService statsd pullers
05-24 15:40:17.259  1307  1307 E BatteryStatsService: Could not register PowerStatsInternal
05-24 15:40:17.262   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.power.stats@1.0::IPowerStats/default in either framework or device VINTF manifest.
05-24 15:40:17.262  1307  1352 E BatteryStatsService: Unable to load Power.Stats.HAL. Setting rail availability to false
05-24 15:40:17.262  1307  1352 E BluetoothAdapter: Bluetooth service is null
05-24 15:40:17.281  1307  1359 W KeyguardServiceDelegate: onScreenTurningOn(): no keyguard service!
05-24 15:40:17.282   826  1969 I ServiceManagerCppClient: Waiting for service 'statscompanion' on '/dev/binder' successful after waiting 200ms
05-24 15:40:17.299  1307  1342 E AppWidgetManager: Notify service of inheritance info
05-24 15:40:17.299  1307  1342 E AppWidgetManager: 	at com.android.server.appwidget.AppWidgetServiceImpl.ensureGroupStateLoadedLocked(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:22)
05-24 15:40:17.299  1307  1342 E AppWidgetManager: 	at com.android.server.appwidget.AppWidgetServiceImpl.getInstalledProvidersForProfile(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:76)
05-24 15:40:17.302  2033  2039 E audio_engineer_test: Control 'TFA98XX Profile' doesn't exist - skipping
05-24 15:40:17.303  2033  2039 E audio_engineer_test: Control 'TFA98XX Profile' doesn't exist - skipping
05-24 15:40:17.303  2033  2039 E audio_engineer_test: Control 'TFA98XX Profile' doesn't exist - skipping
05-24 15:40:17.303  2033  2039 E audio_engineer_test: Control 'TFA98XX Profile' doesn't exist - skipping
05-24 15:40:17.304  2033  2039 E audio_engineer_test: Control 'TFA98XX Profile' doesn't exist - skipping
05-24 15:40:17.304  2033  2039 E audio_engineer_test: Control 'TFA98XX Profile' doesn't exist - skipping
05-24 15:40:17.304  2033  2039 E audio_engineer_test: Control 'TFA98XX Profile' doesn't exist - skipping
05-24 15:40:17.304  2033  2039 E audio_engineer_test: Control 'TFA98XX Profile' doesn't exist - skipping
05-24 15:40:17.304  2033  2039 E audio_engineer_test: Control 'TFA98XX Profile' doesn't exist - skipping
05-24 15:40:17.304  2033  2039 E audio_engineer_test: Control 'TFA98XX Profile' doesn't exist - skipping
05-24 15:40:17.304  2033  2039 E audio_engineer_test: Control 'TFA98XX Profile' doesn't exist - skipping
05-24 15:40:17.304  2033  2039 E audio_engineer_test: Control 'TFA98XX Profile' doesn't exist - skipping
05-24 15:40:17.304  2033  2039 E audio_engineer_test: Control 'TFA98XX Profile' doesn't exist - skipping
05-24 15:40:17.304  2033  2039 E audio_engineer_test: Control 'TFA98XX Profile' doesn't exist - skipping
05-24 15:40:17.304  2033  2039 E audio_engineer_test: Control 'TFA98XX Profile' doesn't exist - skipping
05-24 15:40:17.363  1307  1307 I WifiScanningService: Starting wifiscanner
05-24 15:40:17.365  1307  1307 I EthernetServiceImpl: Starting Ethernet service
05-24 15:40:17.368  1307  1631 I WifiContext: Got resolveInfos for com.android.server.wifi.intent.action.SERVICE_WIFI_RESOURCES_APK: [ResolveInfo{adf3995 com.android.wifi.resources/android.app.Activity m=0x108000 userHandle=UserHandle{0}}]
05-24 15:40:17.378  1307  1627 I WifiService: WifiService starting up with Wi-Fi disabled
05-24 15:40:17.385  1307  1627 I WifiHalHidlImpl: Initializing the WiFi HAL
05-24 15:40:17.385  1307  1627 I WifiHalHidlImpl: initServiceManagerIfNecessaryLocked
05-24 15:40:17.387  1307  1627 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.2::IServiceManager/default
05-24 15:40:17.389  1307  1627 I WifiHalHidlImpl: initWifiIfNecessaryLocked
05-24 15:40:17.390   579   579 I hwservicemanager: Since android.hardware.wifi@1.0::IWifi/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 15:40:17.391   579   579 I hwservicemanager: Since android.hardware.wifi@1.0::IWifi/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 15:40:17.392  1307  1627 I HidlServiceManagement: getService: Trying again for android.hardware.wifi@1.0::IWifi/default...
05-24 15:40:17.421  2133  2133 I android.hardware.wifi@1.0-service-lazy: Wifi Hal is booting up...
05-24 15:40:17.424  2133  2133 I HidlServiceManagement: Registered android.hardware.wifi@1.5::IWifi/default
05-24 15:40:17.424  2133  2133 I HidlServiceManagement: Removing namespace from process name android.hardware.wifi@1.0-service-lazy to wifi@1.0-service-lazy.
05-24 15:40:17.428   579   579 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IKeySwapper/default in either framework or device VINTF manifest.
05-24 15:40:17.430  1307  1342 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 15:40:17.431   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.wifi@1.6::IWifi/default in either framework or device VINTF manifest.
05-24 15:40:17.431  1307  1342 I HealthServiceWrapperHidl: health: HealthServiceWrapper listening to instance default
05-24 15:40:17.438  1307  1342 I StatsPullAtomService: register thermal listener successfully
05-24 15:40:17.438  1307  1307 I SystemServiceManager: Starting com.android.server.policy.PermissionPolicyService
05-24 15:40:17.445  1307  1307 E UserManagerService: Auto-lock preference updated but private space user not found
05-24 15:40:17.446  1307  1307 I AS.AudioService: registerAudioPolicy for android.media.audiopolicy.AudioPolicy$1@b0f9a7 u/pid:1000/1307 with config:reg:32:ap:0
05-24 15:40:17.451  1307  1991 E AS.AudioService: Audioserver died.
05-24 15:40:17.460  1307  1307 I SystemServiceManager: Starting com.android.server.crashrecovery.CrashRecoveryModule$Lifecycle
05-24 15:40:17.466  1307  1364 W DefaultPermGrantPolicy: No such package:com.google.android.apps.camera.services
05-24 15:40:17.467  1307  1364 W DefaultPermGrantPolicy: No such package:com.verizon.mips.services
05-24 15:40:17.469  1307  1364 W DefaultPermGrantPolicy: No such package:com.google.android.adservices
05-24 15:40:17.474  1307  1307 I BrightnessSynchronizer: Initial brightness readings: 88(int), 0.34251967(float)
05-24 15:40:17.475  1307  1364 W DefaultPermGrantPolicy: No such package:com.google.android.apps.actionsservice
05-24 15:40:17.477  1307  1307 I SystemServiceManager: Starting com.android.server.app.GameManagerService$Lifecycle
05-24 15:40:17.484  1307  1307 I SystemServiceManager: Starting phase 520
05-24 15:40:17.505  1307  1307 W PocketService: Un-handled boot phase:520
05-24 15:40:17.506   579   579 I hwservicemanager: getTransport: Cannot find entry vendor.oplus.hardware.commondcs@1.0::ICommonDcsHalService/commondcsservice in either framework or device VINTF manifest.
05-24 15:40:17.506  1113  1329 E android.hardware.biometrics.fingerprint@2.1-service: service NULL
05-24 15:40:17.508  1307  1307 I SystemServiceManager: Starting com.android.safetycenter.SafetyCenterService
05-24 15:40:17.522  1307  1307 I SystemServiceManager: Starting com.android.server.appsearch.AppSearchModule$Lifecycle
05-24 15:40:17.537  1307  1307 I AppSearchModule: AppsIndexer service is disabled.
05-24 15:40:17.538  1307  1307 I AppSearchModule: AppOpenEventIndexer service is disabled.
05-24 15:40:17.538  1307  1307 I SystemServiceManager: Starting com.android.server.media.MediaCommunicationService
05-24 15:40:17.541  1307  1307 I SystemServiceManager: Starting com.android.server.compat.overrides.AppCompatOverridesService$Lifecycle
05-24 15:40:17.542  1307  1307 I SystemServiceManager: Starting com.android.server.power.SleepModeService
05-24 15:40:17.545  1307  1307 I SystemServiceManager: Starting com.android.server.healthconnect.HealthConnectManagerService
05-24 15:40:17.554  1307  1307 I SystemServiceManager: Starting com.android.server.devicelock.DeviceLockService
05-24 15:40:17.557  1307  1307 I DeviceLockService: Registering device_lock
05-24 15:40:17.558  1307  1307 I SystemServiceManager: Starting com.android.server.SensitiveContentProtectionManagerService
05-24 15:40:17.579   579   579 I hwservicemanager: Notifying android.hardware.wifi@1.5::IWifi/default they have clients: 1
05-24 15:40:17.595  1307  1307 E ActivityManager: Unable to find com.android.overlay.permissioncontroller/u0
05-24 15:40:17.598  1307  1307 E ActivityManager: Unable to find com.google.android.printservice.recommendation/u0
05-24 15:40:17.653  1307  1307 I SystemServer: Making services ready
05-24 15:40:17.653  1307  1307 I SystemServiceManager: Starting phase 550
05-24 15:40:17.659  1307  1307 I ThermalManagerService$ThermalHalWrapper: Thermal HAL 2.0 service connected.
05-24 15:40:17.660   852   852 I <EMAIL>: thermal_zone_num are changed0
05-24 15:40:17.660   852   852 W <EMAIL>: tz_data_v1[2].tz_idx:0
05-24 15:40:17.660   852   852 W <EMAIL>: tz_data_v1[3].tz_idx:2
05-24 15:40:17.660   852   852 W <EMAIL>: tz_data_v1[5].tz_idx:3
05-24 15:40:17.661   852   852 W <EMAIL>: tz_data_v1[0].tz_idx:5
05-24 15:40:17.661   852   852 W <EMAIL>: tz_data_v1[1].tz_idx:5
05-24 15:40:17.661   852   852 W <EMAIL>: tz_data_v1[9].tz_idx:5
05-24 15:40:17.661   852   852 W <EMAIL>: init_tz_path_v1:find out tz path
05-24 15:40:17.661   852   852 W <EMAIL>: get_tz_map: tz0, name=mtktscpu, label=CPU, muti_tz_num=1
05-24 15:40:17.661   852   852 W <EMAIL>: tz_idx0:5, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 15:40:17.661   852   852 W <EMAIL>: get_tz_map: tz1, name=mtktscpu, label=GPU, muti_tz_num=1
05-24 15:40:17.661   852   852 W <EMAIL>: tz_idx0:5, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 15:40:17.661   852   852 W <EMAIL>: get_tz_map: tz2, name=mtktsbattery, label=BATTERY, muti_tz_num=1
05-24 15:40:17.661   852   852 W <EMAIL>: tz_idx0:0, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 15:40:17.661   852   852 W <EMAIL>: get_tz_map: tz3, name=mtktsAP, label=SKIN, muti_tz_num=1
05-24 15:40:17.661   852   852 W <EMAIL>: tz_idx0:2, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 15:40:17.661   852   852 W <EMAIL>: get_tz_map: tz4, name=notsupport, label=USB_PORT, muti_tz_num=1
05-24 15:40:17.661   852   852 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 15:40:17.661   852   852 W <EMAIL>: get_tz_map: tz5, name=mtktsbtsmdpa, label=POWER_AMPLIFIER, muti_tz_num=1
05-24 15:40:17.661   852   852 W <EMAIL>: tz_idx0:3, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 15:40:17.661   852   852 W <EMAIL>: get_tz_map: tz6, name=notsupport, label=BCL_VOLTAGE, muti_tz_num=1
05-24 15:40:17.661   852   852 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 15:40:17.661   852   852 W <EMAIL>: get_tz_map: tz7, name=notsupport, label=BCL_CURRENT, muti_tz_num=1
05-24 15:40:17.661   852   852 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 15:40:17.661   852   852 W <EMAIL>: get_tz_map: tz8, name=notsupport, label=BCL_PERCENTAGE, muti_tz_num=1
05-24 15:40:17.661   852   852 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 15:40:17.661   852   852 W <EMAIL>: get_tz_map: tz9, name=mtktscpu, label=NPU, muti_tz_num=1
05-24 15:40:17.661   852   852 W <EMAIL>: tz_idx0:5, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-24 15:40:17.661   852   852 I <EMAIL>: fill_temperatures filterType0 name: CPU type: CPU throttlingStatus: NONE value: 47.522 ret_temps size 0
05-24 15:40:17.662   852   852 I <EMAIL>: fill_temperatures filterType0 name: GPU type: GPU throttlingStatus: NONE value: 47.522 ret_temps size 1
05-24 15:40:17.662   852   852 I <EMAIL>: fill_temperatures filterType0 name: BATTERY type: BATTERY throttlingStatus: NONE value: 36.425 ret_temps size 2
05-24 15:40:17.662   852   852 I <EMAIL>: fill_temperatures filterType0 name: SKIN type: SKIN throttlingStatus: NONE value: 41.185 ret_temps size 3
05-24 15:40:17.662   852   852 I <EMAIL>: fill_temperatures filterType0 name: POWER_AMPLIFIER type: POWER_AMPLIFIER throttlingStatus: NONE value: 40.081 ret_temps size 4
05-24 15:40:17.662   852   852 I <EMAIL>: fill_temperatures filterType0 name: NPU type: NPU throttlingStatus: NONE value: 47.522 ret_temps size 5
05-24 15:40:17.663   852   852 I <EMAIL>: fill_thresholds filterType1 name: SKIN type: SKIN hotThrottlingThresholds: 50 vrThrottlingThreshold: 50 ret_thresholds size 0
05-24 15:40:17.669  1307  1307 W ActivityManager: Too early to start/bind service in system_server: Phase=550 ComponentInfo{com.android.server.telecom/com.android.server.telecom.components.TelecomService}
05-24 15:40:17.669  1307  1307 W ActivityManager: Too early to start/bind service in system_server: Phase=550 ComponentInfo{com.android.server.telecom/com.android.server.telecom.components.TelecomService}
05-24 15:40:17.724  1307  1307 W SystemServiceManager: Service com.android.server.content.ContentService$Lifecycle took 54 ms in onBootPhase
05-24 15:40:17.727   579   579 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IGloveMode/default in either framework or device VINTF manifest.
05-24 15:40:17.728   579   579 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IStylusMode/default in either framework or device VINTF manifest.
05-24 15:40:17.729   579   579 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IHighTouchPollingRate/default in either framework or device VINTF manifest.
05-24 15:40:17.743  1307  1616 W StorageManagerService: Failed to get storage lifetime
05-24 15:40:17.755  1042  1042 I OMXClient: IOmx service obtained
05-24 15:40:17.757  1068  1173 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.aac.decoder'  err=ComponentNotFound(0x80001003)
05-24 15:40:17.757  1042  1042 W OmxInfoBuilder: Fail to add media type audio/mp4a-latm to codec OMX.google.aac.decoder
05-24 15:40:17.761  1042  1042 I OMXClient: IOmx service obtained
05-24 15:40:17.763  1068  1173 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.amrnb.decoder'  err=ComponentNotFound(0x80001003)
05-24 15:40:17.763  1042  1042 W OmxInfoBuilder: Fail to add media type audio/3gpp to codec OMX.google.amrnb.decoder
05-24 15:40:17.765  1042  1042 I OMXClient: IOmx service obtained
05-24 15:40:17.766  1068  1173 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.amrwb.decoder'  err=ComponentNotFound(0x80001003)
05-24 15:40:17.767  1042  1042 W OmxInfoBuilder: Fail to add media type audio/amr-wb to codec OMX.google.amrwb.decoder
05-24 15:40:17.768  1042  1042 I OMXClient: IOmx service obtained
05-24 15:40:17.769  1068  1173 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.flac.decoder'  err=ComponentNotFound(0x80001003)
05-24 15:40:17.770  1042  1042 W OmxInfoBuilder: Fail to add media type audio/flac to codec OMX.google.flac.decoder
05-24 15:40:17.771  1042  1042 I OMXClient: IOmx service obtained
05-24 15:40:17.772  1068  1173 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.g711.alaw.decoder'  err=ComponentNotFound(0x80001003)
05-24 15:40:17.772  1042  1042 W OmxInfoBuilder: Fail to add media type audio/g711-alaw to codec OMX.google.g711.alaw.decoder
05-24 15:40:17.775  1042  1042 I OMXClient: IOmx service obtained
05-24 15:40:17.776  1068  1173 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.g711.mlaw.decoder'  err=ComponentNotFound(0x80001003)
05-24 15:40:17.776  1042  1042 W OmxInfoBuilder: Fail to add media type audio/g711-mlaw to codec OMX.google.g711.mlaw.decoder
05-24 15:40:17.778  1042  1042 I OMXClient: IOmx service obtained
05-24 15:40:17.779  1068  1173 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.mp3.decoder'  err=ComponentNotFound(0x80001003)
05-24 15:40:17.780  1042  1042 W OmxInfoBuilder: Fail to add media type audio/mpeg to codec OMX.google.mp3.decoder
05-24 15:40:17.783  1042  1042 I OMXClient: IOmx service obtained
05-24 15:40:17.785  1068  1173 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.opus.decoder'  err=ComponentNotFound(0x80001003)
05-24 15:40:17.786  1042  1042 W OmxInfoBuilder: Fail to add media type audio/opus to codec OMX.google.opus.decoder
05-24 15:40:17.788  1042  1042 I OMXClient: IOmx service obtained
05-24 15:40:17.789  1068  1173 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.raw.decoder'  err=ComponentNotFound(0x80001003)
05-24 15:40:17.789  1042  1042 W OmxInfoBuilder: Fail to add media type audio/raw to codec OMX.google.raw.decoder
05-24 15:40:17.791  1042  1042 I OMXClient: IOmx service obtained
05-24 15:40:17.792  1068  1173 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.vorbis.decoder'  err=ComponentNotFound(0x80001003)
05-24 15:40:17.792  1042  1042 W OmxInfoBuilder: Fail to add media type audio/vorbis to codec OMX.google.vorbis.decoder
05-24 15:40:17.795  1042  1042 I OMXClient: IOmx service obtained
05-24 15:40:17.796  1068  1173 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.aac.encoder'  err=ComponentNotFound(0x80001003)
05-24 15:40:17.796  1042  1042 W OmxInfoBuilder: Fail to add media type audio/mp4a-latm to codec OMX.google.aac.encoder
05-24 15:40:17.798  1042  1042 I OMXClient: IOmx service obtained
05-24 15:40:17.799  1068  1173 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.amrnb.encoder'  err=ComponentNotFound(0x80001003)
05-24 15:40:17.800  1042  1042 W OmxInfoBuilder: Fail to add media type audio/3gpp to codec OMX.google.amrnb.encoder
05-24 15:40:17.802  1042  1042 I OMXClient: IOmx service obtained
05-24 15:40:17.803  1068  1173 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.amrwb.encoder'  err=ComponentNotFound(0x80001003)
05-24 15:40:17.803  1042  1042 W OmxInfoBuilder: Fail to add media type audio/amr-wb to codec OMX.google.amrwb.encoder
05-24 15:40:17.803  1307  1307 W SystemServiceManager: Service com.android.server.NetworkStatsServiceInitializer took 63 ms in onBootPhase
05-24 15:40:17.805  1042  1042 I OMXClient: IOmx service obtained
05-24 15:40:17.805  1068  1173 E android.hardware.media.omx@1.0-service: Failed to allocate omx component 'OMX.google.flac.encoder'  err=ComponentNotFound(0x80001003)
05-24 15:40:17.806  1042  1042 W OmxInfoBuilder: Fail to add media type audio/flac to codec OMX.google.flac.encoder
05-24 15:40:17.806  1307  1307 I ConnectivityServiceInitializerB: Starting vcn_management
05-24 15:40:17.811  1042  1042 I Codec2Client: Available Codec2 services: "default" "software"
05-24 15:40:17.815  1307  1641 I VcnManagementService: new snapshot: TelephonySubscriptionSnapshot{ mActiveDataSubId=-1, mSubIdToInfoMap={}, mSubIdToCarrierConfigMap={}, mPrivilegedPackages={} }
05-24 15:40:17.827  1307  1991 I AS.AudioDeviceBroker: setBluetoothScoOn: false, mBluetoothScoOn: false, btScoRequesterUId: -1, from: resetBluetoothSco
05-24 15:40:17.832  1307  1690 W BroadcastLoopers: Found previously unknown looper Thread[AudioDeviceBroker,5,main]
05-24 15:40:17.833   853   853 I android.hardware.usb@1.3-service-mediatekv2: port0
05-24 15:40:17.833  1307  1344 I ActivityManager: Start proc 2152:com.android.systemui/u0a226 for service {com.android.systemui/com.android.systemui.wallpapers.ImageWallpaper}
05-24 15:40:17.834   853   853 I android.hardware.usb@1.3-service-mediatekv2: HAL version V1_2
05-24 15:40:17.834   853   853 I android.hardware.usb@1.3-service-mediatekv2: 0:port0 connected:1 canChangeMode:1 canChagedata:0 canChangePower:0 supportedModes:3
05-24 15:40:17.834   853   853 I android.hardware.usb@1.3-service-mediatekv2: ContaminantDetectionStatus:2 ContaminantProtectionStatus:0
05-24 15:40:17.834  1307  1307 I AppLockManagerService: onBootCompleted
05-24 15:40:17.836  2040  2082 I AudioFlinger: systemReady
05-24 15:40:17.841  1307  1307 I LMOFreeform/LMOFreeformUIService: add SystemService: com.libremobileos.freeform.server.LMOFreeformUIService@65d293e
05-24 15:40:17.842  1307  1307 W PocketService: Un-handled boot phase:550
05-24 15:40:17.843  1307  1307 I AppBindingService: Updating constants with: null
05-24 15:40:17.951  1307  1991 I AS.AudioService: Audioserver started.
05-24 15:40:17.954  1307  1307 W SystemServiceManager: Service com.android.server.policy.PermissionPolicyService took 110 ms in onBootPhase
05-24 15:40:17.956  1307  1991 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-24 15:40:17.975  1307  1991 E AudioSystem-JNI: Command failed for android_media_AudioSystem_setParameters: -38
05-24 15:40:17.976  1307  1991 W AS.AudioService: AudioFlinger informed of device's low RAM attribute; status -38
05-24 15:40:17.977  2033  2039 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-24 15:40:17.978  2040  2082 W APM::AudioPolicyEngine/Base: setPhoneState() setting same state 0
05-24 15:40:17.978  2040  2082 W APM_AudioPolicyManager: setPhoneState() invalid or same state 0
05-24 15:40:17.990  1307  1604 W PinnerService: Could not find pinlist.meta for "/product/app/webview/webview.apk": pinning as blob
05-24 15:40:17.995  1307  1991 I AS.AudioService: onUpdateContextualVolumes: absolute volume driving streams 3 avrcp supported: false
05-24 15:40:18.004   825   825 I netd    : networkSetPermissionForUser(1, [1002, 10160, 10191, 10204, 10221, 10222, 10271, 10272, 10273, 10282, 10355]) <0.01ms>
05-24 15:40:18.004   825   825 I netd    : networkSetPermissionForUser(2, [1000, 1001, 1073, 2000, 10152, 10171, 10183, 10199, 10201, 10205, 10226, 10234, 10251]) <0.01ms>
05-24 15:40:18.036  2040  2154 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_HEARING_AID, connection: wireless}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 15:40:18.036  2040  2154 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x8000000, enabled 1, streamToDriveAbs 3
05-24 15:40:18.037  2040  2154 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_HEADSET, connection: bt-le}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 15:40:18.037  2040  2154 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x20000000, enabled 1, streamToDriveAbs 3
05-24 15:40:18.037  2040  2154 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_SPEAKER, connection: bt-le}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 15:40:18.037  2040  2154 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x20000001, enabled 1, streamToDriveAbs 3
05-24 15:40:18.037  2040  2154 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_BROADCAST, connection: bt-le}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-24 15:40:18.037  2040  2154 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x20000002, enabled 1, streamToDriveAbs 3
05-24 15:40:18.039  1307  1991 I AS.AudioService: after audioserver restart: initStreamVolume succeeded
05-24 15:40:18.053  1307  1991 I AS.SpatializerHelper: init effectExpected=true
05-24 15:40:18.053  1307  1991 I AS.SpatializerHelper: init(): No Spatializer found
05-24 15:40:18.053  2040  2082 I AudioFlinger: systemReady
05-24 15:40:18.053  2040  2082 W AudioFlinger: systemReady called twice
05-24 15:40:18.054  1307  1991 E AudioSystem-JNI: Command failed for android_media_AudioSystem_setParameters: -38
05-24 15:40:18.076  1042  1042 I Codec2InfoBuilder: adding type 'audio/x-adpcm-dvi-ima'
05-24 15:40:18.076  1042  1042 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-dvi-ima
05-24 15:40:18.080  1042  1042 I Codec2InfoBuilder: adding type 'audio/x-adpcm-ms'
05-24 15:40:18.081  1042  1042 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-ms
05-24 15:40:18.086  1042  1042 I Codec2InfoBuilder: adding type 'audio/alac'
05-24 15:40:18.086  1042  1042 W AudioCapabilities: Unsupported mediaType audio/alac
05-24 15:40:18.091  1042  1042 I Codec2InfoBuilder: adding type 'audio/ape'
05-24 15:40:18.092  1042  1042 W AudioCapabilities: Unsupported mediaType audio/ape
05-24 15:40:18.140  1307  1307 I SystemServiceManager: Starting phase 600
05-24 15:40:18.159  1307  1307 I ServiceWatcher: [network] chose new implementation 10369/app.grapheneos.networklocation/.NetworkLocationService@0
05-24 15:40:18.170  1307  1307 I ServiceWatcher: [fused] chose new implementation 1000/com.android.location.fused/.FusedLocationService@0
05-24 15:40:18.204  1017  1064 W MNLD    : hal_gps_init: hal_gps_init
05-24 15:40:18.205  1307  1307 I GnssLocationProviderJni: Unable to initialize IGnssGeofencing interface.
05-24 15:40:18.207  1307  1307 I GnssManager: gnss hal initialized
05-24 15:40:18.213  1307  1307 I ServiceWatcher: [GeocoderProxy] chose new implementation null
05-24 15:40:18.219  1307  1307 I ServiceWatcher: [HardwareActivityRecognitionProxy] chose new implementation null
05-24 15:40:18.220  1307  1307 I ServiceWatcher: [GeofenceProxy] chose new implementation null
05-24 15:40:18.223  1307  1307 W SystemServiceManager: Service com.android.server.location.LocationManagerService$Lifecycle took 68 ms in onBootPhase
05-24 15:40:18.224  2152  2176 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 15:40:18.265  1307  1307 I ServiceWatcher: [TrustManagerService] chose new implementation null
05-24 15:40:18.282  1307  1307 W PocketService: Un-handled boot phase:600
05-24 15:40:18.283  1307  1307 W AlarmManager: Unrecognized alarm listener com.android.server.stats.StatsCompanionService$PullingAlarmListener@b63dcdd
05-24 15:40:18.283  1307  1307 W AlarmManager: Unrecognized alarm listener com.android.server.stats.StatsCompanionService$PeriodicAlarmListener@6be9052
05-24 15:40:18.290  1307  1307 I StatsCompanionService: Told statsd that StatsCompanionService is alive.
05-24 15:40:18.295  1042  1042 I Codec2InfoBuilder: adding type 'audio/mp4a-latm'
05-24 15:40:18.300  1042  1042 I Codec2InfoBuilder: adding type 'audio/mp4a-latm'
05-24 15:40:18.302  1307  1344 I ActivityManager: Start proc 2262:app.grapheneos.networklocation/u0a369 for service {app.grapheneos.networklocation/app.grapheneos.networklocation.NetworkLocationService}
05-24 15:40:18.305  1042  1042 I Codec2InfoBuilder: adding type 'audio/3gpp'
05-24 15:40:18.307  1307  1344 I ActivityManager: Start proc 2266:com.android.networkstack.process/1073 for service {com.android.networkstack/com.android.server.NetworkStackService}
05-24 15:40:18.308  1042  1042 I Codec2InfoBuilder: adding type 'audio/3gpp'
05-24 15:40:18.313  1042  1042 I Codec2InfoBuilder: adding type 'audio/amr-wb'
05-24 15:40:18.316  1042  1042 I Codec2InfoBuilder: adding type 'audio/amr-wb'
05-24 15:40:18.343  1042  1042 I Codec2InfoBuilder: adding type 'audio/flac'
05-24 15:40:18.347  1042  1042 I Codec2InfoBuilder: adding type 'audio/flac'
05-24 15:40:18.350  1042  1042 I Codec2InfoBuilder: adding type 'audio/g711-alaw'
05-24 15:40:18.353  1042  1042 I Codec2InfoBuilder: adding type 'audio/g711-mlaw'
05-24 15:40:18.376  1042  1042 I Codec2InfoBuilder: adding type 'audio/mpeg'
05-24 15:40:18.384  1307  1307 I MR2ServiceImpl: switchUser | user: 0
05-24 15:40:18.384  1307  1307 I MmsServiceBroker: Delay connecting to MmsService until an API is called
05-24 15:40:18.392  1042  1042 I Codec2InfoBuilder: adding type 'audio/opus'
05-24 15:40:18.396  1042  1042 I Codec2InfoBuilder: adding type 'audio/opus'
05-24 15:40:18.399  1042  1042 I Codec2InfoBuilder: adding type 'audio/raw'
05-24 15:40:18.401  1307  1307 I SystemServiceManager: Calling onStartUser 0
05-24 15:40:18.402  1042  1042 I Codec2InfoBuilder: adding type 'audio/vorbis'
05-24 15:40:18.420  1307  2258 E StatsCompanionService: Could not get installer for package: com.google.android.trichromelibrary
05-24 15:40:18.420  1307  2258 E StatsCompanionService: android.content.pm.PackageManager$NameNotFoundException: com.google.android.trichromelibrary
05-24 15:40:18.420  1307  2258 E StatsCompanionService: 	at android.app.ApplicationPackageManager.getInstallSourceInfo(ApplicationPackageManager.java:2772)
05-24 15:40:18.420  1307  2258 E StatsCompanionService: 	at com.android.server.stats.StatsCompanionService.getInstallerPackageName(StatsCompanionService.java:153)
05-24 15:40:18.420  1307  2258 E StatsCompanionService: 	at com.android.server.stats.StatsCompanionService.$r8$lambda$MBPStrBhgnmbybdtzkoTAe-YOYw(StatsCompanionService.java:229)
05-24 15:40:18.420  1307  2258 E StatsCompanionService: 	at com.android.server.stats.StatsCompanionService$$ExternalSyntheticLambda1.run(R8$$SyntheticClass:0)
05-24 15:40:18.420  1307  2258 E StatsCompanionService: 	at android.os.Handler.handleCallback(Handler.java:991)
05-24 15:40:18.420  1307  2258 E StatsCompanionService: 	at android.os.Handler.dispatchMessage(Handler.java:102)
05-24 15:40:18.420  1307  2258 E StatsCompanionService: 	at android.os.Looper.loopOnce(Looper.java:232)
05-24 15:40:18.420  1307  2258 E StatsCompanionService: 	at android.os.Looper.loop(Looper.java:317)
05-24 15:40:18.420  1307  2258 E StatsCompanionService: 	at android.os.HandlerThread.run(HandlerThread.java:85)
05-24 15:40:18.421  1307  1616 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-dvi-ima
05-24 15:40:18.422  1307  1616 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-ms
05-24 15:40:18.422  1307  1616 W AudioCapabilities: Unsupported mediaType audio/alac
05-24 15:40:18.422  1307  1616 W AudioCapabilities: Unsupported mediaType audio/ape
05-24 15:40:18.427  2152  2152 E SharedConnectivityManager: To support shared connectivity service on this device, the service's package name and intent action strings must not be empty
05-24 15:40:18.433  1307  2169 I Codec2Client: Available Codec2 services: "default" "software"
05-24 15:40:18.434  1307  1608 I BluetoothSystemServer: AirplaneModeListener: Init completed. isOn=false, isOnOverrode=false
05-24 15:40:18.435  1307  1608 I BluetoothSystemServer: SatelliteModeListener: Initialized successfully with state: false
05-24 15:40:18.452  2262  2262 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-24 15:40:18.456  1307  1616 W AudioCapabilities: Unsupported mime audio/x-adpcm-dvi-ima
05-24 15:40:18.458  1307  1616 W AudioCapabilities: Unsupported mime audio/x-adpcm-dvi-ima
05-24 15:40:18.459  1307  1616 W AudioCapabilities: Unsupported mime audio/x-adpcm-ms
05-24 15:40:18.459  1307  1616 W AudioCapabilities: Unsupported mime audio/x-adpcm-ms
05-24 15:40:18.459  1307  1616 W AudioCapabilities: Unsupported mime audio/alac
05-24 15:40:18.459  1307  1616 W AudioCapabilities: Unsupported mime audio/alac
05-24 15:40:18.459  1307  1616 W AudioCapabilities: Unsupported mime audio/ape
05-24 15:40:18.459  1307  1616 W AudioCapabilities: Unsupported mime audio/ape
05-24 15:40:18.462  1307  1307 W VoiceInteractionManager: no available voice recognition services found for user 0
05-24 15:40:18.462  1307  1307 I AppLockManagerService: onUserStarting: userId = 0
05-24 15:40:18.516  2335  2335 I WebViewZygoteInit: Starting WebViewZygoteInit
05-24 15:40:18.529  2152  2191 E AppWidgetManager: Notify service of inheritance info
05-24 15:40:18.529  2152  2191 E AppWidgetManager: 	at com.android.internal.appwidget.IAppWidgetService$Stub$Proxy.getInstalledProvidersForProfile(IAppWidgetService.java:1071)
05-24 15:40:18.540  1307  1307 I SystemServiceManager: Starting com.android.server.scheduling.RebootReadinessManagerService$Lifecycle
05-24 15:40:18.540  1307  1307 I SystemServiceManager: Not starting an already started service com.android.server.scheduling.RebootReadinessManagerService$Lifecycle
05-24 15:40:18.546  2335  2335 I WebViewZygoteInit: Beginning application preload for com.android.webview
05-24 15:40:18.546  1307  1332 I ServiceWatcher: [network] connected to {app.grapheneos.networklocation/app.grapheneos.networklocation.NetworkLocationService}
05-24 15:40:18.576  2343  2343 W ContextImpl: Failed to ensure /data/user/0/com.android.se/cache: mkdir failed: ENOENT (No such file or directory)
05-24 15:40:18.576  2343  2343 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-24 15:40:18.579  2335  2335 I WebViewZygoteInit: Application preload done
05-24 15:40:18.596  1307  1332 I ServiceWatcher: [fused] connected to {com.android.location.fused/com.android.location.fused.FusedLocationService}
05-24 15:40:18.598  2343  2343 I SecureElementService: main onCreate
05-24 15:40:18.601  1307  1307 I ExplicitHealthCheckController: Service not ready to get health check supported packages. Binding...
05-24 15:40:18.602  2343  2343 I SecureElementService: Check if terminal eSE1 is available.
05-24 15:40:18.604  1307  1307 I ExplicitHealthCheckController: Explicit health check service is bound
05-24 15:40:18.605   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.secure_element@1.2::ISecureElement/eSE1 in either framework or device VINTF manifest.
05-24 15:40:18.606   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.secure_element@1.1::ISecureElement/eSE1 in either framework or device VINTF manifest.
05-24 15:40:18.607   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.secure_element@1.0::ISecureElement/eSE1 in either framework or device VINTF manifest.
05-24 15:40:18.607  2343  2343 I SecureElementService: No HAL implementation for eSE1
05-24 15:40:18.607  2343  2343 I SecureElementService: Check if terminal SIM1 is available.
05-24 15:40:18.610   579   579 I hwservicemanager: Since android.hardware.secure_element@1.2::ISecureElement/SIM1 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 15:40:18.611   579   579 I hwservicemanager: Since android.hardware.secure_element@1.2::ISecureElement/SIM1 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 15:40:18.612  2343  2343 I HidlServiceManagement: getService: Trying again for android.hardware.secure_element@1.2::ISecureElement/SIM1...
05-24 15:40:18.612   579  2407 I hwservicemanager: Tried to start android.hardware.secure_element@1.2::ISecureElement/SIM1 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 15:40:18.614   579  2409 I hwservicemanager: Tried to start android.hardware.secure_element@1.2::ISecureElement/SIM1 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 15:40:18.620  2152  2196 I CameraManagerGlobal: Connecting to camera service
05-24 15:40:18.625  1307  1332 I ServiceWatcher: [GeocoderProxy] chose new implementation null
05-24 15:40:18.625  1307  1332 I ServiceWatcher: [HardwareActivityRecognitionProxy] chose new implementation null
05-24 15:40:18.625  1307  1332 I ServiceWatcher: [GeofenceProxy] chose new implementation null
05-24 15:40:18.625  1307  1332 I ServiceWatcher: [TrustManagerService] chose new implementation null
05-24 15:40:18.626  1307  1307 I ConnectivityModuleConnector: Networking module service connected
05-24 15:40:18.626  1307  1307 I NetworkStackClient: Network stack service connected
05-24 15:40:18.626  1307  1645 W BroadcastLoopers: Found previously unknown looper Thread[AudioService Broadcast,5,main]
05-24 15:40:18.636  1018  1976 I AttributionAndPermissionUtils: checkPermission checkPermission (forDataDelivery 0 startDataDelivery 0): Permission soft denied for client attribution [uid 10226, pid 2152, packageName "<unknown>"]
05-24 15:40:18.657  1307  1307 I Telecom : SystemStateHelper: Registering broadcast receiver: android.content.IntentFilter@f62f946: TS.init@AAA
05-24 15:40:18.658  1307  1307 I Telecom : SystemStateHelper: Registering broadcast receiver: android.content.IntentFilter@73ba707: TS.init@AAA
05-24 15:40:18.664  1307  1307 I Telecom : CallAudioRouteController: calculateSupportedRouteMaskInit: is wired headset plugged in - false: TS.init@AAA
05-24 15:40:18.664  1307  1307 I Telecom : AudioRoute$Factory: createRetry; type=TYPE_SPEAKER, address=null, retryCount=2: TS.init@AAA
05-24 15:40:18.664  1307  1307 I Telecom : AudioRoute$Factory: type: 1: TS.init@AAA
05-24 15:40:18.664  1307  1307 I Telecom : AudioRoute$Factory: type: 2: TS.init@AAA
05-24 15:40:18.665  1307  1307 I Telecom : AudioRoute$Factory: createRetry; type=TYPE_EARPIECE, address=null, retryCount=2: TS.init@AAA
05-24 15:40:18.665  1307  1307 I Telecom : AudioRoute$Factory: type: 1: TS.init@AAA
05-24 15:40:18.674  1307  2417 I Telecom : CallAudioModeStateMachine: Audio focus entering UNFOCUSED state
05-24 15:40:18.674  1307  2417 I Telecom : CallAudioModeStateMachine: Message received: null.: TS.init->CAMSM.pM_1@AAA
05-24 15:40:18.681  1307  1307 I Telecom : MissedCallNotifierImpl: reloadFromDatabase: Boot not yet complete -- call log db may not be available. Deferring loading until boot complete for user 0: TS.init@AAA
05-24 15:40:18.687  1307  1307 I ConnectivityModuleConnector: Networking module service connected
05-24 15:40:18.690  1307  1307 W TrustManagerService: EXTRA_USER_HANDLE missing or invalid, value=0
05-24 15:40:18.690  1307  1307 I ContentSuggestionsManagerService: Updating for user 0: disabled=false
05-24 15:40:18.690  1307  1307 E ContentSuggestionsPerUserService: Bad service name: com.google.android.as/com.google.android.apps.miphone.aiai.app.AiAiContentSuggestionsService
05-24 15:40:18.690  1307  1307 I AutofillManagerService: Updating for user 0: disabled=false
05-24 15:40:18.691  1307  1307 E AutofillManagerServiceImpl: Bad service name: proton.android.pass/proton.android.pass.autofill.ProtonPassAutofillService
05-24 15:40:18.696  1307  1307 W ActivityManager: Unable to start service Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener } U=0: not found
05-24 15:40:18.696  1307  1307 W NotificationManagerService.NotificationListeners: Unable to bind notification listener service: Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener (has extras) } in user 0
05-24 15:40:18.698  2152  2152 I SystemUIService: Found SurfaceFlinger's GPU Priority: 13143
05-24 15:40:18.698  2152  2152 I SystemUIService: Setting SysUI's GPU Context priority to: 12545
05-24 15:40:18.712  2368  2368 E CarrierIdProvider: read carrier list from ota pb failure: java.io.FileNotFoundException: /data/misc/carrierid/carrier_list.pb: open failed: ENOENT (No such file or directory)
05-24 15:40:18.716  1307  1344 I ActivityManager: Start proc 2426:com.android.permissioncontroller/u0a266 for broadcast {com.android.permissioncontroller/com.android.permissioncontroller.privacysources.SafetyCenterReceiver}
05-24 15:40:18.719  1307  1332 W PermissionService: getPermissionFlags: Unknown user -1
05-24 15:40:18.719  1307  1332 W PermissionService: getPermissionFlags: Unknown user -1
05-24 15:40:18.745  1307  2342 W WifiService: Attempt to retrieve WifiConfiguration with invalid scanResult List
05-24 15:40:18.747  1307  2439 W WifiService: Attempt to retrieve WifiConfiguration with invalid scanResult List
05-24 15:40:18.747  1307  2356 E WifiService: Attempt to retrieve passpoint with invalid scanResult List
05-24 15:40:18.747  1307  1325 W WifiService: Attempt to retrieve OsuProviders with invalid scanResult List
05-24 15:40:18.759   886  2450 W gpuservice: AIBinder_linkToDeath is being called with a non-null cookie and no onUnlink callback set. This might not be intended. AIBinder_DeathRecipient_setOnUnlinked should be called first.
05-24 15:40:18.770  2152  2449 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 15:40:18.778  1307  1344 I ActivityManager: Start proc 2457:com.android.launcher3/u0a230 for service {com.android.launcher3/com.android.quickstep.TouchInteractionService}
05-24 15:40:18.780  2152  2449 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 15:40:18.796  2372  2372 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 15:40:18.803  2368  2368 E RILJ    : Feature android.hardware.telephony.ims is declared, but service IMS is missing [PHONE0]
05-24 15:40:18.804  2368  2368 E RILJ    : Feature android.hardware.telephony.messaging is declared, but service MESSAGING is missing [PHONE0]
05-24 15:40:18.805  2368  2368 E RILJ    : Feature android.hardware.telephony.data is declared, but service DATA is missing [PHONE0]
05-24 15:40:18.805  2368  2368 E RILJ    : Feature android.hardware.telephony.calling is declared, but service VOICE is missing [PHONE0]
05-24 15:40:18.853  1307  1335 W KeyguardServiceDelegate: onScreenTurningOn(): no keyguard service!
05-24 15:40:18.857  2457  2457 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-24 15:40:18.872  2457  2457 I QuickstepProtoLogGroup: Initializing ProtoLog.
05-24 15:40:18.888  2368  2368 E RILJ    : Feature android.hardware.telephony.ims is declared, but service IMS is missing [PHONE1]
05-24 15:40:18.889  2368  2368 E RILJ    : Feature android.hardware.telephony.messaging is declared, but service MESSAGING is missing [PHONE1]
05-24 15:40:18.891  2368  2368 E RILJ    : Feature android.hardware.telephony.data is declared, but service DATA is missing [PHONE1]
05-24 15:40:18.893  2368  2368 E RILJ    : Feature android.hardware.telephony.calling is declared, but service VOICE is missing [PHONE1]
05-24 15:40:18.930  2457  2522 E FileLog : java.io.FileNotFoundException: /data/user/0/com.android.launcher3/files/log-0: open failed: ENOENT (No such file or directory)
05-24 15:40:18.930  2457  2522 E FileLog : 	at java.io.FileOutputStream.<init>(FileOutputStream.java:259)
05-24 15:40:18.930  2457  2522 E FileLog : 	at java.io.FileWriter.<init>(FileWriter.java:113)
05-24 15:40:18.930  2457  2522 E FileLog : Caused by: android.system.ErrnoException: open failed: ENOENT (No such file or directory)
05-24 15:40:19.023  2368  2368 E SatelliteController: SatelliteController was not yet initialized.
05-24 15:40:19.040  1307  1344 I ActivityManager: Start proc 2541:com.android.smspush/u0a237 for service {com.android.smspush/com.android.smspush.WapPushManager}
05-24 15:40:19.048  2152  2152 E SharedConnectivityManager: To support shared connectivity service on this device, the service's package name and intent action strings must not be empty
05-24 15:40:19.121  2541  2541 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-24 15:40:19.130  2368  2368 E EmergencyNumberTracker: [0]Cache ota emergency database IOException: java.io.FileNotFoundException: /data/misc/emergencynumberdb/emergency_number_db: open failed: ENOENT (No such file or directory)
05-24 15:40:19.151  1307  2437 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-24 15:40:19.152  2527  2557 E CellBroadcastConfigService: fails to setCellBroadcastRanges
05-24 15:40:19.152  1307  2437 I DefaultPermGrantPolicy: Granting permissions to enabled data services for user:0
05-24 15:40:19.157  1307  2414 W Telecom : BluetoothDeviceManager: getBluetoothHeadset: Acquire BluetoothHeadset service failed due to: java.util.concurrent.TimeoutException
05-24 15:40:19.157  1307  2414 I Telecom : BluetoothRouteManager: getBluetoothAudioConnectedDevice: no service available.
05-24 15:40:19.158  1307  2415 I Telecom : CallAudioRouteController: Message received: BT_AUDIO_DISCONNECTED=1301, arg1=0
05-24 15:40:19.158  1307  2437 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-24 15:40:19.159  2527  2557 E CellBroadcastConfigService: fails to setCellBroadcastRanges
05-24 15:40:19.196  2368  2368 E SatelliteController: SatelliteController was not yet initialized.
05-24 15:40:19.242  2368  2368 E EmergencyNumberTracker: [1]Cache ota emergency database IOException: java.io.FileNotFoundException: /data/misc/emergencynumberdb/emergency_number_db: open failed: ENOENT (No such file or directory)
05-24 15:40:19.242  1307  1572 I MR2ServiceImpl: registerManager | callerUid: 10226, callerPid: 2152, callerPackage: com.android.systemui, targetPackageName: null, targetUserId: UserHandle{0}, hasMediaRoutingControl: false
05-24 15:40:19.251  1307  1571 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-24 15:40:19.252  1307  2437 I DefaultPermGrantPolicy: Granting permissions to enabled data services for user:0
05-24 15:40:19.257  1307  2437 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-24 15:40:19.324  2368  2571 I ImsResolver: Initializing cache.
05-24 15:40:19.336  2368  2368 E SatelliteModemInterface: Unable to bind to the satellite service because the package is undefined.
05-24 15:40:19.382  2368  2368 I TelephonyRcsService: updateFeatureControllers: oldSlots=0, newNumSlots=2
05-24 15:40:19.437  1307  1307 I AS.AudioService: onSubscriptionsChanged()
05-24 15:40:19.452  1307  1307 I AS.AudioService: onSubscriptionsChanged()
05-24 15:40:19.458  1307  1641 I VcnManagementService: new snapshot: TelephonySubscriptionSnapshot{ mActiveDataSubId=2, mSubIdToInfoMap={}, mSubIdToCarrierConfigMap={}, mPrivilegedPackages={} }
05-24 15:40:19.459  2368  2368 E RILJ    : getImei not supported on service MODEM < 2.1. [PHONE0]
05-24 15:40:19.466  2368  2368 E RILJ    : setNullCipherAndIntegrityEnabled not supported on service NETWORK < 2.1. [PHONE0]
05-24 15:40:19.467  2368  2368 E RILJ    : setCellularIdentifierTransparencyEnabled not supported on service NETWORK < 2.2. [PHONE0]
05-24 15:40:19.467  2368  2368 E RILJ    : setSecurityAlgorithmsUpdatedEnabled not supported on service NETWORK < 2.2. [PHONE0]
05-24 15:40:19.474  2152  2590 I Codec2Client: Available Codec2 services: "default" "software"
05-24 15:40:19.479  2368  2368 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-24 15:40:19.490  2368  2368 E NRM-C-0 : service not connected. Domain = PS
05-24 15:40:19.491  2368  2368 E NRM-C-0 : service not connected. Domain = CS
05-24 15:40:19.491  2368  2368 E NRM-I-0 : service not connected. Domain = PS
05-24 15:40:19.498  2368  2368 E NRM-C-0 : service not connected. Domain = PS
05-24 15:40:19.499  2368  2368 E NRM-C-0 : service not connected. Domain = CS
05-24 15:40:19.499  2368  2368 E NRM-I-0 : service not connected. Domain = PS
05-24 15:40:19.530  1307  1572 W Binder  : java.lang.SecurityException: Need REGISTER_STATS_PULL_ATOM permission.: Neither user 10226 nor current process has android.permission.REGISTER_STATS_PULL_ATOM.
05-24 15:40:19.530  1307  1572 W Binder  : 	at android.app.ContextImpl.enforceCallingOrSelfPermission(ContextImpl.java:2630)
05-24 15:40:19.530  1307  1572 W Binder  : 	at com.android.server.stats.StatsManagerService.enforceRegisterStatsPullAtomPermission(StatsManagerService.java:678)
05-24 15:40:19.530  1307  1572 W Binder  : 	at com.android.server.stats.StatsManagerService.registerPullAtomCallback(StatsManagerService.java:219)
05-24 15:40:19.530  1307  1572 W Binder  : 	at android.os.IStatsManagerService$Stub.onTransact(IStatsManagerService.java:434)
05-24 15:40:19.535  1307  2356 I StatusBarManagerService: registerStatusBar bar=com.android.internal.statusbar.IStatusBar$Stub$Proxy@5b06b76
05-24 15:40:19.539  2368  2368 E RILJ    : getImei not supported on service MODEM < 2.1. [PHONE1]
05-24 15:40:19.542  2368  2368 E RILJ    : setNullCipherAndIntegrityEnabled not supported on service NETWORK < 2.1. [PHONE1]
05-24 15:40:19.542  2368  2368 E RILJ    : setCellularIdentifierTransparencyEnabled not supported on service NETWORK < 2.2. [PHONE1]
05-24 15:40:19.542  2368  2368 E RILJ    : setSecurityAlgorithmsUpdatedEnabled not supported on service NETWORK < 2.2. [PHONE1]
05-24 15:40:19.547  2368  2368 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-24 15:40:19.551  2152  2152 I KeyguardSecurityView: Switching mode from Uninitialized to Default
05-24 15:40:19.555  2368  2368 E NRM-C-1 : service not connected. Domain = PS
05-24 15:40:19.555  2368  2368 E NRM-C-1 : service not connected. Domain = CS
05-24 15:40:19.555  2368  2368 E NRM-I-1 : service not connected. Domain = PS
05-24 15:40:19.561  2368  2368 E NRM-C-1 : service not connected. Domain = PS
05-24 15:40:19.561  2368  2368 E NRM-C-1 : service not connected. Domain = CS
05-24 15:40:19.561  2368  2368 E NRM-I-1 : service not connected. Domain = PS
05-24 15:40:19.563  2368  2368 E NRM-C-1 : service not connected. Domain = PS
05-24 15:40:19.563  2368  2368 E NRM-C-1 : service not connected. Domain = CS
05-24 15:40:19.563  2368  2368 E NRM-I-1 : service not connected. Domain = PS
05-24 15:40:19.565  2152  2152 E OverviewProxyService: Failed to get overview proxy for disable flags.
05-24 15:40:19.586  2152  2152 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 15:40:19.610  2152  2152 E OverviewProxyService: Failed to get overview proxy for disable flags.
05-24 15:40:19.612  2343  2343 W HidlServiceManagement: Waited one second for android.hardware.secure_element@1.2::ISecureElement/SIM1
05-24 15:40:19.613   579   579 I hwservicemanager: Since android.hardware.secure_element@1.2::ISecureElement/SIM1 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-24 15:40:19.613  2343  2343 I HidlServiceManagement: getService: Trying again for android.hardware.secure_element@1.2::ISecureElement/SIM1...
05-24 15:40:19.616   579  2610 I hwservicemanager: Tried to start android.hardware.secure_element@1.2::ISecureElement/SIM1 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-24 15:40:19.631  2152  2152 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 15:40:19.643  2152  2152 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-24 15:40:19.669  2368  2368 I Telephony: TelecomAccountRegistry$AccountEntry: registerPstnPhoneAccount: Registering account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with Telecom. subId=-1
05-24 15:40:19.671  1307  2356 W UserManagerService: Requested status bar icon for non-badged user 0
05-24 15:40:19.673  1307  2356 I Telecom : TelecomServiceImpl$1: registerPhoneAccount: account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@AAs
05-24 15:40:19.675  1307  2356 I Telecom : PhoneAccountRegistrar: New phone account registered: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@AAs
05-24 15:40:19.679  1307  2356 I Telecom : CallsManager: Sending phone-account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} registered intent as user: TSI.rPA(cap)@AAs
05-24 15:40:19.685  1307  2356 I Telecom : PhoneAccountRegistrar: Notifying telephony of voice service override change for 1 SIMs, hasService = false: TSI.rPA(cap)@AAs
05-24 15:40:19.686  1307  2356 I Telecom : PhoneAccountRegistrar: maybeNotifyTelephonyForVoiceServiceState: simTm is null.: TSI.rPA(cap)@AAs
05-24 15:40:19.686  2368  2368 I Telephony: TelecomAccountRegistry$AccountEntry: Registered phoneAccount: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with handle: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}
05-24 15:40:19.688  2368  2368 I Telephony: TelecomAccountRegistry: Unregistering phone account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 2, UserHandle{0}.
05-24 15:40:19.691  1307  1571 I Telecom : CallsManager: Sending phone-account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 2, UserHandle{0} unregistered intent as user: TSI.uPA(cap)@AA0
05-24 15:40:19.693  2368  2368 I Telephony: TelecomAccountRegistry: Unregistering phone account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 1, UserHandle{0}.
05-24 15:40:19.695  1307  1571 I Telecom : CallsManager: Sending phone-account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 1, UserHandle{0} unregistered intent as user: TSI.uPA(cap)@AA4
05-24 15:40:19.698  1307  1307 I AS.AudioService: onSubscriptionsChanged()
05-24 15:40:19.701  1307  1307 I AS.AudioService: onSubscriptionsChanged()
05-24 15:40:19.702  1307  1307 I AS.AudioService: onSubscriptionsChanged()
05-24 15:40:19.704  1307  1307 I AS.AudioService: onSubscriptionsChanged()
05-24 15:40:19.715  2152  2152 I SystemUIService: Topological CoreStartables completed in 2 iterations
05-24 15:40:19.716  2152  2152 E OverviewProxyService: Failed to get overview proxy for disable flags.
05-24 15:40:19.734  2152  2152 W SimLog  : invalid subId in handleServiceStateChange()
05-24 15:40:19.736  1307  1572 W UserManagerService: Requested status bar icon for non-badged user 0
05-24 15:40:19.755  1203  1203 E bootanimation: === MALI DEBUG ===eglp_check_display_valid_and_initialized_and_retain retun EGL_NOT_INITIALIZED
05-24 15:40:19.760  2368  2368 I Telephony: TelecomAccountRegistry$AccountEntry: registerPstnPhoneAccount: Registering account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with Telecom. subId=-1
05-24 15:40:19.761  1307  1571 I Telecom : TelecomServiceImpl$1: registerPhoneAccount: account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@AA8
05-24 15:40:19.761  1307  1571 I Telecom : PhoneAccountRegistrar: Modify account: [ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}]: TSI.rPA(cap)@AA8
05-24 15:40:19.762  1307  1571 I Telecom : CallsManager: handlePhoneAccountChanged: phoneAccount=[[X] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@AA8
05-24 15:40:19.765  1307  1571 I Telecom : PhoneAccountRegistrar: Notifying telephony of voice service override change for 1 SIMs, hasService = false: TSI.rPA(cap)@AA8
05-24 15:40:19.765  1307  1571 I Telecom : PhoneAccountRegistrar: maybeNotifyTelephonyForVoiceServiceState: simTm is null.: TSI.rPA(cap)@AA8
05-24 15:40:19.766  2368  2368 I Telephony: TelecomAccountRegistry$AccountEntry: Registered phoneAccount: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with handle: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}
05-24 15:40:19.793  2368  2368 E SST     : [0] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-24 15:40:19.793  2368  2368 E SST     : [0] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-24 15:40:19.793  2368  2368 E SST     : [0] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-24 15:40:19.796  2368  2368 E NRM-C-0 : service not connected. Domain = PS
05-24 15:40:19.796  2368  2368 E NRM-C-0 : service not connected. Domain = CS
05-24 15:40:19.796  2368  2368 E NRM-I-0 : service not connected. Domain = PS
05-24 15:40:19.804  2368  2368 E SST     : [1] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-24 15:40:19.804  2368  2368 E SST     : [1] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-24 15:40:19.804  2368  2368 E SST     : [1] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-24 15:40:19.805  2368  2368 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-24 15:40:19.806  2368  2368 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-24 15:40:19.824  2368  2368 E NRM-C-1 : service not connected. Domain = PS
05-24 15:40:19.825  2368  2368 E NRM-C-1 : service not connected. Domain = CS
05-24 15:40:19.825  2368  2368 E NRM-I-1 : service not connected. Domain = PS
05-24 15:40:19.885  2368  2368 I Telephony: TelecomAccountRegistry$AccountEntry: registerPstnPhoneAccount: Registering account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with Telecom. subId=-1
05-24 15:40:19.885  1307  2356 I Telecom : TelecomServiceImpl$1: registerPhoneAccount: account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABE
05-24 15:40:19.887  1307  2356 I Telecom : PhoneAccountRegistrar: Modify account: [ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}]: TSI.rPA(cap)@ABE
05-24 15:40:19.888  1307  2356 I Telecom : CallsManager: handlePhoneAccountChanged: phoneAccount=[[X] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABE
05-24 15:40:19.890  1307  2356 I Telecom : PhoneAccountRegistrar: Notifying telephony of voice service override change for 1 SIMs, hasService = false: TSI.rPA(cap)@ABE
05-24 15:40:19.890  1307  2356 I Telecom : PhoneAccountRegistrar: maybeNotifyTelephonyForVoiceServiceState: simTm is null.: TSI.rPA(cap)@ABE
05-24 15:40:19.891  2368  2368 I Telephony: TelecomAccountRegistry$AccountEntry: Registered phoneAccount: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with handle: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}
05-24 15:40:19.946  2368  2368 I Telephony: TelecomAccountRegistry$AccountEntry: registerPstnPhoneAccount: Registering account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with Telecom. subId=-1
05-24 15:40:19.947  1307  2356 I Telecom : TelecomServiceImpl$1: registerPhoneAccount: account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABM
05-24 15:40:19.948  1307  2356 I Telecom : PhoneAccountRegistrar: Modify account: [ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}]: TSI.rPA(cap)@ABM
05-24 15:40:19.953  1307  2356 I Telecom : CallsManager: handlePhoneAccountChanged: phoneAccount=[[X] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABM
05-24 15:40:19.956  1307  2356 I Telecom : PhoneAccountRegistrar: Notifying telephony of voice service override change for 1 SIMs, hasService = false: TSI.rPA(cap)@ABM
05-24 15:40:19.956  1307  2356 I Telecom : PhoneAccountRegistrar: maybeNotifyTelephonyForVoiceServiceState: simTm is null.: TSI.rPA(cap)@ABM
05-24 15:40:19.957  2368  2368 I Telephony: TelecomAccountRegistry$AccountEntry: Registered phoneAccount: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with handle: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}
05-24 15:40:19.965   912   970 I PowerHalLoader: Successfully connected to Power HAL AIDL service.
05-24 15:40:19.967  1307  1335 I SystemServiceManager: Starting phase 1000
05-24 15:40:19.967  1307  1335 E PowerStatsService: Failed to start PowerStatsService loggers
05-24 15:40:19.985  1307  1335 I TransparencyService: Boot completed. Getting boot integrity data.
05-24 15:40:19.985  1307  1335 I TransparencyService: Boot completed. Collecting biometric system properties.
05-24 15:40:19.987  1307  1335 I TransparencyService: Scheduling measurements to be taken.
05-24 15:40:19.987  1307  1335 I TransparencyService: Scheduling binary content-digest computation job
05-24 15:40:19.997  1307  1616 I StorageSessionController: Started resetting external storage service...
05-24 15:40:19.997  1307  1616 I StorageSessionController: Finished resetting external storage service
05-24 15:40:19.997  1307  1616 I StorageManagerService: Resetting vold...
05-24 15:40:19.997  1307  1616 I StorageManagerService: Reset vold
