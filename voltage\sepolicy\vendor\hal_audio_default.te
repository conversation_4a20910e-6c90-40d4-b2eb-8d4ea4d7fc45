allow hal_audio_default hal_audio_default:process { execmem };
allow hal_audio_default mtk_hal_power_hwservice:hwservice_manager find;
binder_call(hal_audio_default, mtk_hal_power)

allow hal_audio_default hal_audio_default:netlink_kobject_uevent_socket { create bind read setopt };
allow hal_audio_default property_socket:sock_file write;
allow hal_audio_default init:unix_stream_socket connectto;

allow hal_audio_default adsp_device:chr_file rw_file_perms;
allow hal_audio_default audio_ipi_device:chr_file rw_file_perms;
allow hal_audio_default ccci_device:chr_file rw_file_perms;
allow hal_audio_default ebc_device:chr_file rw_file_perms;
allow hal_audio_default vow_device:chr_file rw_file_perms;

allow hal_audio_default persist_data_file:dir search;
allow hal_audio_default mnt_vendor_file:dir search;
allow hal_audio_default mtk_audiohal_data_file:dir create_dir_perms;
allow hal_audio_default mtk_audiohal_data_file:file create_file_perms;

allow hal_audio_default sysfs_boot_mode:file r_file_perms;

get_prop(hal_audio_default, vendor_mtk_wmt_prop)
get_prop(hal_audio_default, vendor_mtk_audiohal_prop)
get_prop(hal_audio_default, vendor_mtk_default_prop)
get_prop(hal_audio_default, vendor_mtk_tel_switch_prop)
get_prop(hal_audio_default, vendor_audio_prop)
set_prop(hal_audio_default, vendor_audio_prop)
set_prop(hal_audio_default, vendor_mtk_audiohal_prop)

allow hal_audio_default hal_audio_default:process { execmem };
allow hal_audio_default untrusted_app:fifo_file { write };

r_dir_file(hal_audio_default, vendor_proc_oplus_version)
set_prop(hal_audio_default, system_oplus_audio_prop)
set_prop(hal_audio_default, vendor_audio_tuning_prop)

allow hal_audio_default persist_data_file:dir r_dir_perms;
binder_call(hal_audio_default, hal_performance_oplus)
allow hal_audio_default hal_performance_oplus_hwservice:hwservice_manager find;

# Fix for vendor service execution permissions
allow hal_audio_default same_process_hal_file:file { execute };
allow hal_audio_default vendor_file:file { execute execute_no_trans };

# Fix for binder communication
allow hal_audio_default vndbinder_device:chr_file { read write open ioctl map };

# Fix for network socket permissions
allow hal_audio_default hal_audio_default:netlink_generic_socket { create bind write read };

# Fix for hardware service manager access
allow hal_audio_default hwservicemanager:binder { call };
