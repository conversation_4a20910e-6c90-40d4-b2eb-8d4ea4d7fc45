allow proc_perfmgr proc:filesystem associate ;
allow proc_cpufreq proc:filesystem associate ;
allow vendor_proc_display proc:filesystem associate ;
allow init vendor_shell_exec:file {r_file_perms execute};
allow init vendor_toolbox_exec:file {r_file_perms execute};
allow init proc:file rw_file_perms;
allow init proc_swappiness:file rw_file_perms;
allow init proc_watermark_scale_factor:file rw_file_perms;
allow init sysfs_devices_block:file rw_file_perms;
allow init sysfs_leds:file create_file_perms;
allow init mtk_hal_camera_exec:file {r_file_perms execute};
allow init vendor_sysfs_otg_switch:file create_file_perms;
allow init vendor_sysfs_usb_supply:file create_file_perms;
allow init vendor_sysfs_graphics:file create_file_perms;
allow init vendor_proc_display:file create_file_perms;
allow init ccci_device:chr_file r_file_perms;
allow init vtservice_hidl:fd { use };
allow init shell_exec:file {r_file_perms execute};
allow init hal_audio_default:file rw_file_perms;
allow init system_file:file {r_file_perms execute};
allow init hal_performance_oplus_exec:file {r_file_perms execute};
allow init hal_performance_oplus_hwservice:hwservice_manager find;
allow init mtk_hal_videotelephony_hwservice:hwservice_manager find;
allow init oplus_block_device:lnk_file relabelto;
allow init oplus_orms_aidl_service_exec:file {r_file_perms execute};
allow init sysfs_vibrator:file rw_file_perms;

# Allow init to relabel files and directories
allow init system_file:dir relabelfrom;
allow init system_file:file relabelfrom;
allow init system_file:lnk_file relabelfrom;
allow init system_lib_file:dir relabelfrom;
allow init system_lib_file:file relabelfrom;

# Allow init to access system_file and app_data_file
allow init system_file:sock_file getattr;
allow init app_data_file:file getattr;

# Allow init to write to sysfs
allow init sysfs:file write;

get_prop(init, exported_system_prop)

# Allow init to set MediaTek C2 video decoder properties
allow init vendor_mtk_c2_log_prop:property_service set;

# Fix for vendor service execution permissions
allow init vendor_file:file { execute execute_no_trans };
allow init same_process_hal_file:file { execute };
allow init vndbinder_device:chr_file { read write open ioctl map };
allow init init:netlink_generic_socket { create bind write read };
allow init hwservicemanager:binder { call };
allow init socket_device:sock_file { create setattr };
