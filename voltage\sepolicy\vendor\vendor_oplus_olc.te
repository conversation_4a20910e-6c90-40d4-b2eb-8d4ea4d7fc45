# SELinux policy for vendor.oplus.hardware.olc service

# Define the domain for OLC service
type vendor_oplus_olc, domain;
type vendor_oplus_olc_exec, exec_type, vendor_file_type, file_type;

# Allow init to start the service
init_daemon_domain(vendor_oplus_olc)

# Allow execution of vendor libraries
allow vendor_oplus_olc same_process_hal_file:file { execute read open getattr map };
allow vendor_oplus_olc vendor_file:file { execute execute_no_trans read open getattr };

# Allow binder communication
allow vendor_oplus_olc vndbinder_device:chr_file { read write open ioctl map };
binder_use(vendor_oplus_olc)
binder_call(vendor_oplus_olc, hwservicemanager)

# Allow network socket operations
allow vendor_oplus_olc vendor_oplus_olc:netlink_generic_socket { create bind write read };

# Allow hardware service manager access
allow vendor_oplus_olc hwservicemanager:binder { call transfer };
allow vendor_oplus_olc default_android_hwservice:hwservice_manager { find add };

# Allow property access
get_prop(vendor_oplus_olc, vendor_default_prop)
set_prop(vendor_oplus_olc, vendor_default_prop)

# Allow file system access
allow vendor_oplus_olc vendor_configs_file:dir { search };
allow vendor_oplus_olc vendor_configs_file:file { read open getattr };
